import { useState } from 'react';
import { ArrowLeft } from 'lucide-react';
import { Link } from 'wouter';
import MobileLayout from '@/components/mobile-layout';
import CameraOverlay from '@/components/camera-overlay';
import IncidentModal from '@/components/incident-modal';
import { Button } from '@/components/ui/button';

const MOCK_USER_ID = "mock-user-123";

export default function LiveDetection() {
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [isIncidentModalOpen, setIsIncidentModalOpen] = useState(false);
  const [capturedImageData, setCapturedImageData] = useState<string>();
  const [analysisData, setAnalysisData] = useState<any>();

  const handleCameraCapture = (imageData: string, analysis?: any) => {
    setCapturedImageData(imageData);
    setAnalysisData(analysis);
    setIsCameraOpen(false);
    setIsIncidentModalOpen(true);
  };

  const handleQuickReport = () => {
    setIsCameraOpen(false);
    setIsIncidentModalOpen(true);
  };

  return (
    <>
      <MobileLayout
        title="Live Detection"
        subtitle="Real-time AI hazard detection"
        showLiveIndicator={true}
      >
        <div className="px-4 py-6">
          <div className="mb-6">
            <Link href="/">
              <Button variant="ghost" size="sm" className="mb-4">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>

          <div className="bg-white rounded-xl shadow-md p-6 text-center">
            <div className="w-20 h-20 bg-safety-green rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M15 8l-4.5 6L8 11l-2 2.5L12 19l8-10z"/>
              </svg>
            </div>
            
            <h2 className="text-xl font-bold text-gray-900 mb-2">Live Safety Detection</h2>
            <p className="text-gray-600 mb-6">
              Start live camera monitoring to detect safety hazards in real-time using AI technology.
            </p>

            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-safety-blue mb-2">Detection Capabilities:</h3>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• PPE Compliance (Helmets, Gloves, Vests)</li>
                <li>• Fall Detection</li>
                <li>• Zone Violations</li>
                <li>• Trip/Slip Hazards</li>
                <li>• Tool Placement Issues</li>
                <li>• Vehicle Proximity Warnings</li>
              </ul>
            </div>

            <Button
              onClick={() => setIsCameraOpen(true)}
              className="w-full bg-safety-green hover:bg-green-600 text-white py-4 rounded-lg font-semibold text-lg"
            >
              Start Live Detection
            </Button>
          </div>

          <div className="mt-6 bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <div className="w-5 h-5 bg-amber-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-xs font-bold">!</span>
              </div>
              <div>
                <h4 className="font-semibold text-amber-800 mb-1">Important Notes:</h4>
                <ul className="text-sm text-amber-700 space-y-1">
                  <li>• Ensure adequate lighting for best results</li>
                  <li>• Hold device steady during scanning</li>
                  <li>• AI analysis runs every 2 seconds</li>
                  <li>• Tap capture when hazards are detected</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </MobileLayout>

      <CameraOverlay
        isOpen={isCameraOpen}
        onClose={() => setIsCameraOpen(false)}
        onCapture={handleCameraCapture}
        onQuickReport={handleQuickReport}
      />

      <IncidentModal
        isOpen={isIncidentModalOpen}
        onClose={() => setIsIncidentModalOpen(false)}
        imageData={capturedImageData}
        analysis={analysisData}
        userId={MOCK_USER_ID}
      />
    </>
  );
}
