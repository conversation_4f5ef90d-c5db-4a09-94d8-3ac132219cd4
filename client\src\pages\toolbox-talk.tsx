import MobileLayout from '@/components/mobile-layout';
import Navigation from '@/components/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ArrowLeft, Calendar, Mic, MicOff, Plus, UserCheck, Users } from 'lucide-react';
import { useState } from 'react';
import { Link } from 'wouter';

const MOCK_USER_ID = 'mock-user-123';

export default function ToolboxTalk() {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [selectedTalk, setSelectedTalk] = useState<any>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // Form state
  const [title, setTitle] = useState('');
  const [topic, setTopic] = useState('');
  const [content, setContent] = useState('');
  const [location, setLocation] = useState('');
  const [scheduledDate, setScheduledDate] = useState('');

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: toolboxTalks = [], isLoading } = useQuery({
    queryKey: ['/api/toolbox-talks', MOCK_USER_ID],
  });

  const createTalkMutation = useMutation({
    mutationFn: async (data: any) => {
      return await apiRequest('POST', '/api/toolbox-talks', data);
    },
    onSuccess: () => {
      toast({
        title: 'Toolbox Talk Created',
        description: 'Your safety briefing has been scheduled successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/toolbox-talks'] });
      setIsCreateModalOpen(false);
      resetForm();
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to create toolbox talk. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const updateAttendeesMutation = useMutation({
    mutationFn: async ({ id, attendees }: { id: string; attendees: string[] }) => {
      return await apiRequest('PATCH', `/api/toolbox-talks/${id}/attendees`, { attendees });
    },
    onSuccess: () => {
      toast({
        title: 'Attendance Updated',
        description: 'Attendance has been recorded successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/toolbox-talks'] });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to update attendance.',
        variant: 'destructive',
      });
    },
  });

  const resetForm = () => {
    setTitle('');
    setTopic('');
    setContent('');
    setLocation('');
    setScheduledDate('');
  };

  const handleCreateTalk = () => {
    if (!title.trim() || !topic.trim()) {
      toast({
        title: 'Missing Information',
        description: 'Please fill in title and topic fields.',
        variant: 'destructive',
      });
      return;
    }

    const talkData = {
      conductorId: MOCK_USER_ID,
      title: title.trim(),
      topic: topic.trim(),
      content: content.trim(),
      location: location.trim(),
      scheduledAt: scheduledDate ? new Date(scheduledDate) : null,
      attendees: [],
      duration: recordingDuration,
    };

    createTalkMutation.mutate(talkData);
  };

  const handleStartRecording = () => {
    setIsRecording(true);
    setRecordingDuration(0);

    // Start timer
    const timer = setInterval(() => {
      setRecordingDuration(prev => prev + 1);
    }, 1000);

    // Store timer reference for cleanup
    (window as any).recordingTimer = timer;
  };

  const handleStopRecording = () => {
    setIsRecording(false);

    if ((window as any).recordingTimer) {
      clearInterval((window as any).recordingTimer);
    }

    toast({
      title: 'Recording Stopped',
      description: `Session recorded for ${Math.floor(recordingDuration / 60)}:${(recordingDuration % 60).toString().padStart(2, '0')}`,
    });
  };

  const handleViewDetails = (talk: any) => {
    setSelectedTalk(talk);
    setIsDetailModalOpen(true);
  };

  const handleMarkAttendance = (talkId: string) => {
    // In a real app, this would scan QR codes or use face recognition
    const currentAttendees = selectedTalk?.attendees || [];
    const updatedAttendees = [...currentAttendees, MOCK_USER_ID];

    updateAttendeesMutation.mutate({ id: talkId, attendees: updatedAttendees });
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusBadge = (talk: any) => {
    if (talk.conductedAt) {
      return <Badge className="bg-safety-green text-white">Completed</Badge>;
    }
    if (talk.scheduledAt && new Date(talk.scheduledAt) > new Date()) {
      return <Badge className="bg-safety-blue text-white">Scheduled</Badge>;
    }
    return <Badge className="bg-safety-orange text-white">Pending</Badge>;
  };

  return (
    <>
      <MobileLayout title="Toolbox Talks" subtitle="Safety briefings and training">
        <div className="px-4 py-6">
          {/* Header Actions */}
          <div className="flex items-center justify-between mb-6">
            <Link href="/">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
            </Link>
            <Button
              onClick={() => setIsCreateModalOpen(true)}
              className="bg-safety-blue hover:bg-blue-600"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Talk
            </Button>
          </div>

          {/* Today's Schedule */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Today's Schedule
              </CardTitle>
            </CardHeader>
            <CardContent>
              {(toolboxTalks as any[]).filter((talk: any) => {
                const today = new Date().toDateString();
                const talkDate = talk.scheduledAt
                  ? new Date(talk.scheduledAt).toDateString()
                  : null;
                return talkDate === today;
              }).length > 0 ? (
                <div className="space-y-3">
                  {(toolboxTalks as any[])
                    .filter((talk: any) => {
                      const today = new Date().toDateString();
                      const talkDate = talk.scheduledAt
                        ? new Date(talk.scheduledAt).toDateString()
                        : null;
                      return talkDate === today;
                    })
                    .map((talk: any) => (
                      <div
                        key={talk.id}
                        className="flex items-center justify-between p-3 bg-blue-50 rounded-lg"
                      >
                        <div>
                          <p className="font-medium text-sm">{talk.title}</p>
                          <p className="text-xs text-gray-600">{talk.topic}</p>
                          {talk.scheduledAt && (
                            <p className="text-xs text-safety-blue">
                              {new Date(talk.scheduledAt).toLocaleTimeString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit',
                              })}
                            </p>
                          )}
                        </div>
                        <Button
                          size="sm"
                          onClick={() => handleViewDetails(talk)}
                          className="bg-safety-blue hover:bg-blue-600"
                        >
                          View
                        </Button>
                      </div>
                    ))}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <Calendar className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm">No talks scheduled for today</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* All Toolbox Talks */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">All Toolbox Talks</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-3">
                  {[1, 2, 3].map(i => (
                    <div key={i} className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : (toolboxTalks as any[]).length === 0 ? (
                <div className="text-center py-6 text-gray-500">
                  <Users className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Toolbox Talks</h3>
                  <p className="text-sm">Start by creating your first safety briefing.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {(toolboxTalks as any[]).map((talk: any) => (
                    <div key={talk.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900">{talk.title}</h3>
                          <p className="text-sm text-gray-600 mt-1">{talk.topic}</p>
                        </div>
                        {getStatusBadge(talk)}
                      </div>

                      <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                        <div className="flex items-center space-x-4">
                          {talk.location && <span>{talk.location}</span>}
                          {talk.scheduledAt && (
                            <span>
                              {new Date(talk.scheduledAt).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit',
                              })}
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-1">
                          <Users className="w-3 h-3" />
                          <span>{talk.attendees?.length || 0} attended</span>
                        </div>
                      </div>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewDetails(talk)}
                        className="w-full"
                      >
                        View Details
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </MobileLayout>

      <Navigation />

      {/* Create Talk Modal */}
      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <DialogContent className="w-full max-w-md mx-auto bottom-0 fixed translate-y-0 translate-x-[-50%] left-1/2 rounded-t-3xl rounded-b-none border-0 p-0 max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            <div className="w-8 h-1 bg-gray-300 rounded-full mx-auto mb-6"></div>

            <DialogHeader>
              <DialogTitle className="text-xl font-bold text-gray-900 mb-4">
                Create Toolbox Talk
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Title *</label>
                <Input
                  value={title}
                  onChange={e => setTitle(e.target.value)}
                  placeholder="e.g., Weekly Safety Briefing"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Topic *</label>
                <Input
                  value={topic}
                  onChange={e => setTopic(e.target.value)}
                  placeholder="e.g., Lockout/Tagout Procedures"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Content</label>
                <Textarea
                  value={content}
                  onChange={e => setContent(e.target.value)}
                  placeholder="Key points to cover..."
                  rows={4}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                <Input
                  value={location}
                  onChange={e => setLocation(e.target.value)}
                  placeholder="e.g., Conference Room A"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Schedule Date/Time
                </label>
                <Input
                  type="datetime-local"
                  value={scheduledDate}
                  onChange={e => setScheduledDate(e.target.value)}
                />
              </div>

              {/* Recording Section */}
              <div className="border-t pt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Session Recording
                </label>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-sm font-medium">Record Session</p>
                    <p className="text-xs text-gray-600">
                      {isRecording
                        ? `Recording: ${formatDuration(recordingDuration)}`
                        : 'Start recording when ready'}
                    </p>
                  </div>
                  <Button
                    onClick={isRecording ? handleStopRecording : handleStartRecording}
                    variant={isRecording ? 'destructive' : 'default'}
                    size="sm"
                  >
                    {isRecording ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setIsCreateModalOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateTalk}
                disabled={createTalkMutation.isPending}
                className="flex-1 bg-safety-blue hover:bg-blue-600"
              >
                {createTalkMutation.isPending ? 'Creating...' : 'Create Talk'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Talk Details Modal */}
      <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
        <DialogContent className="w-full max-w-md mx-auto bottom-0 fixed translate-y-0 translate-x-[-50%] left-1/2 rounded-t-3xl rounded-b-none border-0 p-0 max-h-[90vh] overflow-y-auto">
          {selectedTalk && (
            <div className="p-6">
              <div className="w-8 h-1 bg-gray-300 rounded-full mx-auto mb-6"></div>

              <DialogHeader>
                <DialogTitle className="text-xl font-bold text-gray-900 mb-4">
                  {selectedTalk.title}
                </DialogTitle>
              </DialogHeader>

              <div className="space-y-4 mb-6">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">Status:</span>
                  {getStatusBadge(selectedTalk)}
                </div>

                <div>
                  <span className="text-sm font-medium text-gray-600">Topic:</span>
                  <p className="text-gray-900 mt-1">{selectedTalk.topic}</p>
                </div>

                {selectedTalk.content && (
                  <div>
                    <span className="text-sm font-medium text-gray-600">Content:</span>
                    <p className="text-gray-900 mt-1 text-sm">{selectedTalk.content}</p>
                  </div>
                )}

                {selectedTalk.location && (
                  <div>
                    <span className="text-sm font-medium text-gray-600">Location:</span>
                    <p className="text-gray-900 mt-1">{selectedTalk.location}</p>
                  </div>
                )}

                {selectedTalk.scheduledAt && (
                  <div>
                    <span className="text-sm font-medium text-gray-600">Scheduled:</span>
                    <p className="text-gray-900 mt-1">
                      {new Date(selectedTalk.scheduledAt).toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </p>
                  </div>
                )}

                {selectedTalk.duration && (
                  <div>
                    <span className="text-sm font-medium text-gray-600">Duration:</span>
                    <p className="text-gray-900 mt-1">{formatDuration(selectedTalk.duration)}</p>
                  </div>
                )}

                <div>
                  <span className="text-sm font-medium text-gray-600">Attendance:</span>
                  <div className="mt-2 flex items-center space-x-2">
                    <Users className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-900">
                      {selectedTalk.attendees?.length || 0} participants
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                {!selectedTalk.attendees?.includes(MOCK_USER_ID) && (
                  <Button
                    onClick={() => handleMarkAttendance(selectedTalk.id)}
                    disabled={updateAttendeesMutation.isPending}
                    className="w-full bg-safety-green hover:bg-green-600"
                  >
                    <UserCheck className="w-4 h-4 mr-2" />
                    Mark Attendance
                  </Button>
                )}

                <Button
                  onClick={() => setIsDetailModalOpen(false)}
                  variant="outline"
                  className="w-full"
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
