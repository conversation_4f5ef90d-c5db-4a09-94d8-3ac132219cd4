"""
Hazard Detection Model
Advanced detection of various safety hazards including spills, obstacles, and environmental risks
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional
from pathlib import Path
import cv2
import numpy as np
from ultralytics import YOLO
import torch
from sklearn.cluster import DBSCAN

logger = logging.getLogger(__name__)

class HazardDetector:
    """
    Multi-modal hazard detection system for workplace safety
    """
    
    def __init__(self, model_path: Path):
        self.model_path = model_path
        self.model_path.mkdir(exist_ok=True)
        
        self.model: Optional[YOLO] = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Hazard class mappings
        self.hazard_classes = {
            0: "spill_liquid",
            1: "spill_oil", 
            2: "obstacle_cable",
            3: "obstacle_debris",
            4: "fire_hazard",
            5: "chemical_leak",
            6: "blocked_exit",
            7: "unstable_stack",
            8: "wet_floor",
            9: "broken_equipment",
            10: "electrical_hazard"
        }
        
        # Severity mapping
        self.severity_mapping = {
            "spill_liquid": "MEDIUM",
            "spill_oil": "HIGH",
            "obstacle_cable": "MEDIUM",
            "obstacle_debris": "MEDIUM",
            "fire_hazard": "HIGH",
            "chemical_leak": "HIGH",
            "blocked_exit": "HIGH",
            "unstable_stack": "MEDIUM",
            "wet_floor": "MEDIUM",
            "broken_equipment": "MEDIUM",
            "electrical_hazard": "HIGH"
        }
        
        self.performance_metrics = {
            "total_detections": 0,
            "avg_inference_time": 0.0,
            "false_positive_rate": 0.0
        }
    
    async def load_model(self):
        """Load hazard detection model"""
        try:
            model_file = self.model_path / "hazard_yolov8.pt"
            
            if not model_file.exists():
                logger.info("Hazard model not found, setting up custom model...")
                self.model = YOLO("yolov8n.pt")
                await self._setup_custom_hazard_model()
            else:
                self.model = YOLO(str(model_file))
            
            self.model.to(self.device)
            logger.info(f"Hazard detector loaded on {self.device}")
            
        except Exception as e:
            logger.error(f"Failed to load hazard model: {e}")
            raise
    
    async def _setup_custom_hazard_model(self):
        """Setup custom hazard detection model"""
        logger.info("Setting up custom hazard detection model...")
        
        # In production, this would involve training on hazard datasets
        custom_model_path = self.model_path / "hazard_yolov8.pt"
        self.model.save(str(custom_model_path))
    
    async def detect(self, image: np.ndarray, confidence_threshold: float = 0.5) -> List[Dict[str, Any]]:
        """
        Detect hazards in image using multiple detection methods
        """
        if not self.model:
            raise RuntimeError("Hazard model not loaded")
        
        start_time = time.time()
        
        try:
            detections = []
            
            # 1. YOLO-based object detection for known hazards
            yolo_detections = await self._detect_with_yolo(image, confidence_threshold)
            detections.extend(yolo_detections)
            
            # 2. Computer vision-based detection for spills and wet areas
            cv_detections = await self._detect_with_cv(image)
            detections.extend(cv_detections)
            
            # 3. Edge detection for obstacles and cables
            edge_detections = await self._detect_obstacles(image)
            detections.extend(edge_detections)
            
            # 4. Color-based detection for chemical spills
            color_detections = await self._detect_chemical_hazards(image)
            detections.extend(color_detections)
            
            # Remove duplicate detections
            detections = self._remove_duplicates(detections)
            
            # Update performance metrics
            inference_time = time.time() - start_time
            self.performance_metrics["total_detections"] += len(detections)
            self.performance_metrics["avg_inference_time"] = (
                (self.performance_metrics["avg_inference_time"] + inference_time) / 2
            )
            
            return detections
            
        except Exception as e:
            logger.error(f"Hazard detection error: {e}")
            return []
    
    async def _detect_with_yolo(self, image: np.ndarray, confidence_threshold: float) -> List[Dict]:
        """Detect hazards using YOLO model"""
        results = self.model(image, conf=confidence_threshold, verbose=False)
        
        detections = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0].cpu().numpy())
                    class_id = int(box.cls[0].cpu().numpy())
                    
                    if class_id in self.hazard_classes:
                        hazard_type = self.hazard_classes[class_id]
                        severity = self.severity_mapping.get(hazard_type, "MEDIUM")
                        
                        detection = {
                            "type": "HAZARD",
                            "hazard_type": hazard_type,
                            "confidence": confidence,
                            "bounding_box": {
                                "x": int(x1),
                                "y": int(y1),
                                "width": int(x2 - x1),
                                "height": int(y2 - y1)
                            },
                            "description": self._get_hazard_description(hazard_type),
                            "severity": severity,
                            "recommendations": self._get_hazard_recommendations(hazard_type)
                        }
                        detections.append(detection)
        
        return detections
    
    async def _detect_with_cv(self, image: np.ndarray) -> List[Dict]:
        """Detect spills and wet areas using computer vision"""
        detections = []
        
        # Convert to different color spaces for better detection
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Detect dark wet areas (water spills)
        wet_mask = self._detect_wet_areas(hsv, gray)
        wet_contours, _ = cv2.findContours(wet_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in wet_contours:
            area = cv2.contourArea(contour)
            if area > 500:  # Minimum area threshold
                x, y, w, h = cv2.boundingRect(contour)
                
                detection = {
                    "type": "HAZARD",
                    "hazard_type": "wet_floor",
                    "confidence": 0.7,
                    "bounding_box": {"x": x, "y": y, "width": w, "height": h},
                    "description": "Wet floor area detected",
                    "severity": "MEDIUM",
                    "recommendations": [
                        "Place wet floor warning signs",
                        "Clean and dry the area",
                        "Ensure proper drainage"
                    ]
                }
                detections.append(detection)
        
        # Detect oil spills (darker, more reflective areas)
        oil_mask = self._detect_oil_spills(hsv)
        oil_contours, _ = cv2.findContours(oil_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in oil_contours:
            area = cv2.contourArea(contour)
            if area > 300:
                x, y, w, h = cv2.boundingRect(contour)
                
                detection = {
                    "type": "HAZARD",
                    "hazard_type": "spill_oil",
                    "confidence": 0.8,
                    "bounding_box": {"x": x, "y": y, "width": w, "height": h},
                    "description": "Oil spill detected",
                    "severity": "HIGH",
                    "recommendations": [
                        "Immediate cleanup required",
                        "Use absorbent materials",
                        "Check for leak source",
                        "Ensure proper ventilation"
                    ]
                }
                detections.append(detection)
        
        return detections
    
    async def _detect_obstacles(self, image: np.ndarray) -> List[Dict]:
        """Detect obstacles like cables and debris using edge detection"""
        detections = []
        
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        
        # Detect lines (potential cables)
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, minLineLength=50, maxLineGap=10)
        
        if lines is not None:
            # Group nearby lines
            line_groups = self._group_lines(lines)
            
            for group in line_groups:
                if len(group) >= 3:  # Multiple parallel lines suggest cables
                    # Calculate bounding box for the line group
                    all_points = np.array(group).reshape(-1, 2)
                    x_min, y_min = np.min(all_points, axis=0)
                    x_max, y_max = np.max(all_points, axis=0)
                    
                    detection = {
                        "type": "HAZARD",
                        "hazard_type": "obstacle_cable",
                        "confidence": 0.6,
                        "bounding_box": {
                            "x": int(x_min),
                            "y": int(y_min),
                            "width": int(x_max - x_min),
                            "height": int(y_max - y_min)
                        },
                        "description": "Cable obstacle detected",
                        "severity": "MEDIUM",
                        "recommendations": [
                            "Secure cables properly",
                            "Use cable covers",
                            "Mark hazard area"
                        ]
                    }
                    detections.append(detection)
        
        return detections
    
    async def _detect_chemical_hazards(self, image: np.ndarray) -> List[Dict]:
        """Detect chemical hazards based on color analysis"""
        detections = []
        
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Define color ranges for different chemical types
        chemical_colors = {
            "yellow": ([20, 100, 100], [30, 255, 255]),  # Sulfur compounds
            "green": ([40, 100, 100], [80, 255, 255]),   # Acids
            "red": ([0, 100, 100], [10, 255, 255]),      # Corrosives
        }
        
        for color_name, (lower, upper) in chemical_colors.items():
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 200:
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    detection = {
                        "type": "HAZARD",
                        "hazard_type": "chemical_leak",
                        "confidence": 0.5,
                        "bounding_box": {"x": x, "y": y, "width": w, "height": h},
                        "description": f"Potential {color_name} chemical detected",
                        "severity": "HIGH",
                        "recommendations": [
                            "Evacuate area immediately",
                            "Contact hazmat team",
                            "Ensure proper ventilation",
                            "Use appropriate PPE"
                        ]
                    }
                    detections.append(detection)
        
        return detections
    
    def _detect_wet_areas(self, hsv: np.ndarray, gray: np.ndarray) -> np.ndarray:
        """Detect wet areas using HSV and grayscale analysis"""
        # Wet areas typically have lower saturation and different value
        lower_wet = np.array([0, 0, 0])
        upper_wet = np.array([180, 50, 100])
        wet_mask = cv2.inRange(hsv, lower_wet, upper_wet)
        
        # Apply morphological operations to clean up the mask
        kernel = np.ones((5, 5), np.uint8)
        wet_mask = cv2.morphologyEx(wet_mask, cv2.MORPH_CLOSE, kernel)
        wet_mask = cv2.morphologyEx(wet_mask, cv2.MORPH_OPEN, kernel)
        
        return wet_mask
    
    def _detect_oil_spills(self, hsv: np.ndarray) -> np.ndarray:
        """Detect oil spills using color and texture analysis"""
        # Oil typically appears darker with rainbow-like reflections
        lower_oil = np.array([0, 0, 0])
        upper_oil = np.array([180, 255, 80])
        oil_mask = cv2.inRange(hsv, lower_oil, upper_oil)
        
        kernel = np.ones((3, 3), np.uint8)
        oil_mask = cv2.morphologyEx(oil_mask, cv2.MORPH_CLOSE, kernel)
        
        return oil_mask
    
    def _group_lines(self, lines: np.ndarray) -> List[List]:
        """Group nearby parallel lines"""
        if len(lines) == 0:
            return []
        
        # Convert lines to points for clustering
        points = []
        for line in lines:
            x1, y1, x2, y2 = line[0]
            points.append([x1, y1])
            points.append([x2, y2])
        
        points = np.array(points)
        
        # Use DBSCAN clustering to group nearby points
        clustering = DBSCAN(eps=30, min_samples=2).fit(points)
        
        # Group lines by cluster
        groups = {}
        for i, label in enumerate(clustering.labels_):
            if label != -1:  # Ignore noise
                if label not in groups:
                    groups[label] = []
                groups[label].append(points[i])
        
        return list(groups.values())
    
    def _remove_duplicates(self, detections: List[Dict]) -> List[Dict]:
        """Remove duplicate detections based on overlap"""
        if len(detections) <= 1:
            return detections
        
        unique_detections = []
        
        for detection in detections:
            is_duplicate = False
            
            for existing in unique_detections:
                if self._calculate_overlap(detection["bounding_box"], existing["bounding_box"]) > 0.5:
                    # Keep the detection with higher confidence
                    if detection["confidence"] > existing["confidence"]:
                        unique_detections.remove(existing)
                        unique_detections.append(detection)
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_detections.append(detection)
        
        return unique_detections
    
    def _calculate_overlap(self, bbox1: Dict, bbox2: Dict) -> float:
        """Calculate overlap ratio between two bounding boxes"""
        x1_min, y1_min = bbox1["x"], bbox1["y"]
        x1_max, y1_max = x1_min + bbox1["width"], y1_min + bbox1["height"]
        
        x2_min, y2_min = bbox2["x"], bbox2["y"]
        x2_max, y2_max = x2_min + bbox2["width"], y2_min + bbox2["height"]
        
        # Calculate intersection
        x_overlap = max(0, min(x1_max, x2_max) - max(x1_min, x2_min))
        y_overlap = max(0, min(y1_max, y2_max) - max(y1_min, y2_min))
        intersection = x_overlap * y_overlap
        
        # Calculate union
        area1 = bbox1["width"] * bbox1["height"]
        area2 = bbox2["width"] * bbox2["height"]
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0
    
    def _get_hazard_description(self, hazard_type: str) -> str:
        """Get description for hazard type"""
        descriptions = {
            "spill_liquid": "Liquid spill creating slip hazard",
            "spill_oil": "Oil spill - slip and environmental hazard",
            "obstacle_cable": "Cable creating trip hazard",
            "obstacle_debris": "Debris blocking walkway",
            "fire_hazard": "Fire hazard detected",
            "chemical_leak": "Chemical leak detected",
            "blocked_exit": "Emergency exit blocked",
            "unstable_stack": "Unstable material stack",
            "wet_floor": "Wet floor slip hazard",
            "broken_equipment": "Broken equipment hazard",
            "electrical_hazard": "Electrical hazard detected"
        }
        return descriptions.get(hazard_type, f"{hazard_type} detected")
    
    def _get_hazard_recommendations(self, hazard_type: str) -> List[str]:
        """Get recommendations for hazard type"""
        recommendations = {
            "spill_liquid": ["Clean spill immediately", "Place warning signs", "Check for leak source"],
            "spill_oil": ["Immediate cleanup required", "Use absorbent materials", "Ensure ventilation"],
            "obstacle_cable": ["Secure cables", "Use cable covers", "Mark hazard area"],
            "obstacle_debris": ["Remove debris", "Clear walkway", "Investigate source"],
            "fire_hazard": ["Evacuate area", "Contact fire department", "Use appropriate extinguisher"],
            "chemical_leak": ["Evacuate area", "Contact hazmat team", "Ensure ventilation"],
            "blocked_exit": ["Clear exit immediately", "Ensure compliance", "Check other exits"],
            "unstable_stack": ["Secure materials", "Reduce stack height", "Use proper supports"],
            "wet_floor": ["Place warning signs", "Clean and dry", "Check drainage"],
            "broken_equipment": ["Tag out equipment", "Schedule repair", "Use alternative"],
            "electrical_hazard": ["De-energize circuit", "Contact electrician", "Secure area"]
        }
        return recommendations.get(hazard_type, ["Investigate and address hazard"])
    
    async def get_performance_metrics(self) -> Dict[str, float]:
        """Get performance metrics"""
        return self.performance_metrics.copy()
    
    async def get_memory_usage(self) -> float:
        """Get memory usage"""
        if self.model and torch.cuda.is_available():
            return torch.cuda.memory_allocated() / 1024**2
        return 0.0
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.model:
            del self.model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        logger.info("Hazard detector cleanup complete")
