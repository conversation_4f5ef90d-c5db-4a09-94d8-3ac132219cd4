{"typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "files.associations": {"*.css": "tailwindcss"}, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "tailwindCSS.includeLanguages": {"typescript": "typescript", "typescriptreact": "typescriptreact"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "css.validate": false, "less.validate": false, "scss.validate": false, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true, "**/coverage": true}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true}, "typescript.preferences.importModuleSpecifier": "relative"}