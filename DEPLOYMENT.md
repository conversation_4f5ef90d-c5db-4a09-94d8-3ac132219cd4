# AI Safety Assistant Deployment Guide

## 🚀 Production Deployment

### System Requirements

#### Minimum Requirements
- **CPU**: 8 cores, 3.0GHz+
- **RAM**: 16GB DDR4
- **GPU**: NVIDIA RTX 3060 or equivalent (8GB VRAM)
- **Storage**: 500GB SSD
- **Network**: 1Gbps connection

#### Recommended Requirements
- **CPU**: 16 cores, 3.5GHz+
- **RAM**: 32GB DDR4
- **GPU**: NVIDIA RTX 4080 or equivalent (16GB VRAM)
- **Storage**: 1TB NVMe SSD
- **Network**: 10Gbps connection

### Docker Deployment

#### 1. Build AI Backend Container
```dockerfile
# ai_backend/Dockerfile
FROM nvidia/cuda:11.8-runtime-ubuntu20.04

WORKDIR /app

# Install Python and dependencies
RUN apt-get update && apt-get install -y \
    python3.9 \
    python3-pip \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 2. Build Frontend Container
```dockerfile
# client/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 3. Docker Compose Configuration
```yaml
# docker-compose.yml
version: '3.8'

services:
  ai-backend:
    build: ./ai_backend
    ports:
      - "8000:8000"
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - MODEL_PATH=/app/models
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  frontend:
    build: ./client
    ports:
      - "80:80"
    depends_on:
      - ai-backend

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: safeguard_ai
      POSTGRES_USER: safeguard
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  redis_data:
  postgres_data:
```

### Kubernetes Deployment

#### 1. AI Backend Deployment
```yaml
# k8s/ai-backend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-backend
  template:
    metadata:
      labels:
        app: ai-backend
    spec:
      containers:
      - name: ai-backend
        image: safeguardai/backend:latest
        ports:
        - containerPort: 8000
        resources:
          requests:
            memory: "8Gi"
            cpu: "4"
            nvidia.com/gpu: 1
          limits:
            memory: "16Gi"
            cpu: "8"
            nvidia.com/gpu: 1
        env:
        - name: MODEL_PATH
          value: "/app/models"
        - name: REDIS_URL
          value: "redis://redis-service:6379"
        volumeMounts:
        - name: model-storage
          mountPath: /app/models
      volumes:
      - name: model-storage
        persistentVolumeClaim:
          claimName: model-pvc
```

#### 2. Frontend Deployment
```yaml
# k8s/frontend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: safeguardai/frontend:latest
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

### Cloud Deployment Options

#### AWS Deployment
```bash
# Using AWS EKS with GPU nodes
eksctl create cluster \
  --name safeguard-ai \
  --region us-west-2 \
  --nodegroup-name gpu-nodes \
  --node-type p3.2xlarge \
  --nodes 2 \
  --nodes-min 1 \
  --nodes-max 4
```

#### Google Cloud Deployment
```bash
# Using GKE with GPU nodes
gcloud container clusters create safeguard-ai \
  --zone us-central1-a \
  --machine-type n1-standard-4 \
  --accelerator type=nvidia-tesla-t4,count=1 \
  --num-nodes 2
```

#### Azure Deployment
```bash
# Using AKS with GPU nodes
az aks create \
  --resource-group safeguard-ai \
  --name safeguard-ai-cluster \
  --node-count 2 \
  --node-vm-size Standard_NC6s_v3 \
  --enable-addons monitoring
```

### Environment Configuration

#### Production Environment Variables
```bash
# .env.production
# Database
DATABASE_URL=****************************************/safeguard_ai
REDIS_URL=redis://redis:6379

# AI Models
MODEL_PATH=/app/models
CUDA_VISIBLE_DEVICES=0
BATCH_SIZE=32
MAX_WORKERS=4

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here
CORS_ORIGINS=https://your-domain.com

# Monitoring
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=INFO
METRICS_ENABLED=true

# External Services
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Storage
S3_BUCKET=safeguard-ai-models
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
```

### Model Deployment

#### 1. Model Download Script
```bash
#!/bin/bash
# scripts/download_models.sh

MODEL_DIR="./models"
mkdir -p $MODEL_DIR

# Download YOLOv8 models
wget -O $MODEL_DIR/yolov8n.pt https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt
wget -O $MODEL_DIR/yolov8s.pt https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s.pt

# Download custom trained models (replace with your model URLs)
wget -O $MODEL_DIR/ppe_detector.pt https://your-model-storage.com/ppe_detector.pt
wget -O $MODEL_DIR/hazard_detector.pt https://your-model-storage.com/hazard_detector.pt

echo "Models downloaded successfully!"
```

#### 2. Model Validation
```python
# scripts/validate_models.py
import torch
from ultralytics import YOLO

def validate_models():
    models = [
        "models/ppe_detector.pt",
        "models/hazard_detector.pt",
        "models/yolov8n.pt"
    ]
    
    for model_path in models:
        try:
            model = YOLO(model_path)
            print(f"✅ {model_path} loaded successfully")
        except Exception as e:
            print(f"❌ {model_path} failed to load: {e}")

if __name__ == "__main__":
    validate_models()
```

### Monitoring & Observability

#### 1. Prometheus Configuration
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ai-backend'
    static_configs:
      - targets: ['ai-backend:8000']
    metrics_path: '/metrics'

  - job_name: 'frontend'
    static_configs:
      - targets: ['frontend:80']
```

#### 2. Grafana Dashboard
```json
{
  "dashboard": {
    "title": "AI Safety Assistant Metrics",
    "panels": [
      {
        "title": "Model Inference Time",
        "type": "graph",
        "targets": [
          {
            "expr": "avg(model_inference_duration_seconds)"
          }
        ]
      },
      {
        "title": "Detection Accuracy",
        "type": "stat",
        "targets": [
          {
            "expr": "avg(model_accuracy_score)"
          }
        ]
      }
    ]
  }
}
```

### Security Configuration

#### 1. SSL/TLS Setup
```nginx
# nginx/ssl.conf
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api/ {
        proxy_pass http://ai-backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 2. API Rate Limiting
```python
# Rate limiting configuration
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

@app.post("/api/v2/analyze-image")
@limiter.limit("10/minute")
async def analyze_image(request: Request, ...):
    # API endpoint with rate limiting
    pass
```

### Performance Optimization

#### 1. Model Optimization
```python
# Model optimization for production
import torch
from torch.jit import script

# Convert to TorchScript for faster inference
model = YOLO("models/ppe_detector.pt")
scripted_model = script(model)
scripted_model.save("models/ppe_detector_optimized.pt")

# Enable mixed precision
torch.backends.cudnn.benchmark = True
torch.backends.cudnn.enabled = True
```

#### 2. Caching Strategy
```python
# Redis caching for frequent requests
import redis
import json

redis_client = redis.Redis(host='redis', port=6379, db=0)

async def cached_detection(image_hash: str, model_name: str):
    cache_key = f"detection:{model_name}:{image_hash}"
    cached_result = redis_client.get(cache_key)
    
    if cached_result:
        return json.loads(cached_result)
    
    # Perform detection
    result = await run_detection(image_hash, model_name)
    
    # Cache for 1 hour
    redis_client.setex(cache_key, 3600, json.dumps(result))
    
    return result
```

### Backup & Recovery

#### 1. Database Backup
```bash
#!/bin/bash
# scripts/backup_db.sh

BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create database backup
pg_dump -h postgres -U safeguard safeguard_ai > $BACKUP_DIR/db_backup_$DATE.sql

# Upload to S3
aws s3 cp $BACKUP_DIR/db_backup_$DATE.sql s3://safeguard-ai-backups/

# Clean old backups (keep last 7 days)
find $BACKUP_DIR -name "db_backup_*.sql" -mtime +7 -delete
```

#### 2. Model Backup
```bash
#!/bin/bash
# scripts/backup_models.sh

MODEL_DIR="/app/models"
BACKUP_DIR="/backups/models"
DATE=$(date +%Y%m%d_%H%M%S)

# Create model backup
tar -czf $BACKUP_DIR/models_backup_$DATE.tar.gz $MODEL_DIR

# Upload to S3
aws s3 cp $BACKUP_DIR/models_backup_$DATE.tar.gz s3://safeguard-ai-backups/models/

echo "Model backup completed: models_backup_$DATE.tar.gz"
```

### Health Checks

#### 1. Application Health Check
```python
# Health check endpoint
@app.get("/health")
async def health_check():
    checks = {
        "database": await check_database(),
        "redis": await check_redis(),
        "models": await check_models(),
        "gpu": check_gpu_availability()
    }
    
    all_healthy = all(checks.values())
    status_code = 200 if all_healthy else 503
    
    return JSONResponse(
        status_code=status_code,
        content={"status": "healthy" if all_healthy else "unhealthy", "checks": checks}
    )
```

#### 2. Kubernetes Liveness Probe
```yaml
livenessProbe:
  httpGet:
    path: /health
    port: 8000
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3
```

### Scaling Configuration

#### 1. Horizontal Pod Autoscaler
```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ai-backend-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-backend
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Troubleshooting

#### Common Issues
1. **GPU Memory Issues**: Reduce batch size or model complexity
2. **Slow Inference**: Check GPU utilization and model optimization
3. **High Memory Usage**: Implement model caching and cleanup
4. **Network Timeouts**: Increase timeout values and implement retries

#### Debug Commands
```bash
# Check GPU status
nvidia-smi

# Monitor container resources
docker stats

# Check logs
kubectl logs -f deployment/ai-backend

# Test API endpoints
curl -X POST http://localhost:8000/health
```

---

**For additional support, contact the deployment team or refer to the troubleshooting guide.**
