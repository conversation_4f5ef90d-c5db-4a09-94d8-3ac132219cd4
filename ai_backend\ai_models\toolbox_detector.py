"""
Toolbox and Tools Detection Model
Detects improperly placed tools and toolbox safety violations
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional
from pathlib import Path
import cv2
import numpy as np
from ultralytics import YOLO
import torch

logger = logging.getLogger(__name__)

class ToolboxDetector:
    """
    Toolbox and tools safety detection system
    """
    
    def __init__(self, model_path: Path):
        self.model_path = model_path
        self.model_path.mkdir(exist_ok=True)
        
        self.model: Optional[YOLO] = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Tool categories and safety rules
        self.tool_categories = {
            "hand_tools": ["hammer", "screwdriver", "wrench", "pliers"],
            "power_tools": ["drill", "saw", "grinder", "welder"],
            "measuring_tools": ["ruler", "level", "caliper"],
            "cutting_tools": ["knife", "blade", "scissors"],
            "containers": ["toolbox", "bucket", "container"]
        }
        
        # Safety zones where tools should not be left
        self.prohibited_zones = [
            "walkway", "emergency_exit", "electrical_panel", 
            "fire_exit", "stairway", "doorway"
        ]
        
        self.performance_metrics = {
            "total_detections": 0,
            "avg_inference_time": 0.0,
            "tool_violations": 0
        }
    
    async def load_model(self):
        """Load toolbox detection model"""
        try:
            # Load YOLO model
            self.model = YOLO("yolov8n.pt")
            self.model.to(self.device)
            
            logger.info("Toolbox detector loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load toolbox detector: {e}")
            raise
    
    async def detect(self, image: np.ndarray, confidence_threshold: float = 0.5) -> List[Dict[str, Any]]:
        """
        Detect toolbox and tool safety violations
        """
        if not self.model:
            raise RuntimeError("Toolbox detector model not loaded")
        
        start_time = time.time()
        
        try:
            detections = []
            
            # Step 1: Detect tools and toolboxes
            tool_objects = await self._detect_tools(image, confidence_threshold)
            
            # Step 2: Analyze tool placement safety
            for tool in tool_objects:
                safety_analysis = await self._analyze_tool_safety(image, tool)
                if safety_analysis["is_violation"]:
                    detections.append(safety_analysis["detection"])
            
            # Step 3: Check for scattered tools
            scattered_tools = await self._detect_scattered_tools(tool_objects)
            detections.extend(scattered_tools)
            
            # Step 4: Check toolbox organization
            toolbox_violations = await self._check_toolbox_organization(image, tool_objects)
            detections.extend(toolbox_violations)
            
            # Update performance metrics
            inference_time = time.time() - start_time
            self.performance_metrics["total_detections"] += len(detections)
            self.performance_metrics["tool_violations"] += len(detections)
            self.performance_metrics["avg_inference_time"] = (
                (self.performance_metrics["avg_inference_time"] + inference_time) / 2
            )
            
            return detections
            
        except Exception as e:
            logger.error(f"Toolbox detection error: {e}")
            return []
    
    async def _detect_tools(self, image: np.ndarray, confidence_threshold: float) -> List[Dict]:
        """Detect tools and toolboxes in the image"""
        # Run YOLO detection
        results = self.model(image, conf=confidence_threshold, verbose=False)
        
        tools = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0].cpu().numpy())
                    class_id = int(box.cls[0].cpu().numpy())
                    
                    # Map COCO classes to tool categories
                    tool_info = self._classify_tool(class_id)
                    
                    if tool_info:
                        tool = {
                            "class_id": class_id,
                            "tool_type": tool_info["type"],
                            "tool_category": tool_info["category"],
                            "confidence": confidence,
                            "bounding_box": {
                                "x": int(x1),
                                "y": int(y1),
                                "width": int(x2 - x1),
                                "height": int(y2 - y1)
                            },
                            "center": (int((x1 + x2) / 2), int((y1 + y2) / 2))
                        }
                        tools.append(tool)
        
        return tools
    
    def _classify_tool(self, class_id: int) -> Optional[Dict[str, str]]:
        """Classify detected object as tool type"""
        # COCO class mappings to tools (simplified)
        tool_mappings = {
            # Common objects that could be tools
            64: {"type": "laptop", "category": "equipment"},
            65: "mouse",
            66: "remote",
            67: "keyboard",
            68: "cell_phone",
            # Add more specific tool mappings as needed
        }
        
        # For demo purposes, treat some objects as tools
        if class_id in [64, 65, 66, 67, 68]:  # Electronics as proxy for tools
            return {"type": "tool", "category": "hand_tools"}
        
        return None
    
    async def _analyze_tool_safety(self, image: np.ndarray, tool: Dict) -> Dict[str, Any]:
        """Analyze tool placement for safety violations"""
        tool_center = tool["center"]
        tool_bbox = tool["bounding_box"]
        
        # Check if tool is in a prohibited area
        violation_type = await self._check_prohibited_placement(image, tool_center)
        
        if violation_type:
            return {
                "is_violation": True,
                "detection": {
                    "type": "TOOLBOX_HAZARD",
                    "tool_type": tool["tool_type"],
                    "tool_category": tool["tool_category"],
                    "confidence": tool["confidence"],
                    "bounding_box": tool_bbox,
                    "description": f"{tool['tool_type']} left in {violation_type}",
                    "severity": self._get_violation_severity(violation_type),
                    "violation_type": violation_type,
                    "recommendations": self._get_tool_recommendations(violation_type, tool["tool_category"])
                }
            }
        
        # Check if tool is properly secured
        security_violation = await self._check_tool_security(image, tool)
        if security_violation:
            return {
                "is_violation": True,
                "detection": security_violation
            }
        
        return {"is_violation": False}
    
    async def _check_prohibited_placement(self, image: np.ndarray, tool_center: tuple) -> Optional[str]:
        """Check if tool is placed in a prohibited area"""
        # Analyze image regions to identify prohibited zones
        
        # Method 1: Edge detection for walkways
        if await self._is_in_walkway(image, tool_center):
            return "walkway"
        
        # Method 2: Color detection for marked areas
        prohibited_area = await self._detect_marked_areas(image, tool_center)
        if prohibited_area:
            return prohibited_area
        
        # Method 3: Proximity to exits/doors
        if await self._is_near_exit(image, tool_center):
            return "emergency_exit"
        
        return None
    
    async def _is_in_walkway(self, image: np.ndarray, point: tuple) -> bool:
        """Check if point is in a walkway using edge detection"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        
        # Look for parallel lines that might indicate walkway boundaries
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, minLineLength=100, maxLineGap=10)
        
        if lines is not None:
            # Simple heuristic: if point is between parallel horizontal lines
            horizontal_lines = []
            for line in lines:
                x1, y1, x2, y2 = line[0]
                if abs(y2 - y1) < 10:  # Nearly horizontal
                    horizontal_lines.append((y1 + y2) / 2)
            
            if len(horizontal_lines) >= 2:
                horizontal_lines.sort()
                point_y = point[1]
                
                # Check if point is between any pair of lines
                for i in range(len(horizontal_lines) - 1):
                    if horizontal_lines[i] <= point_y <= horizontal_lines[i + 1]:
                        line_distance = horizontal_lines[i + 1] - horizontal_lines[i]
                        if 50 <= line_distance <= 200:  # Reasonable walkway width
                            return True
        
        return False
    
    async def _detect_marked_areas(self, image: np.ndarray, point: tuple) -> Optional[str]:
        """Detect marked safety areas using color analysis"""
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Safety marking colors
        safety_colors = {
            "fire_zone": ([0, 120, 70], [10, 255, 255]),      # Red
            "electrical": ([20, 120, 70], [30, 255, 255]),    # Yellow
            "emergency": ([40, 120, 70], [80, 255, 255])      # Green
        }
        
        for area_type, (lower, upper) in safety_colors.items():
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            
            # Check if point is in colored area
            if mask[point[1], point[0]] > 0:
                return area_type
        
        return None
    
    async def _is_near_exit(self, image: np.ndarray, point: tuple) -> bool:
        """Check if point is near an exit using sign detection"""
        # Look for exit signs (green rectangles with text)
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Green color range for exit signs
        lower_green = np.array([40, 50, 50])
        upper_green = np.array([80, 255, 255])
        mask = cv2.inRange(hsv, lower_green, upper_green)
        
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 100 < area < 5000:  # Reasonable size for exit sign
                x, y, w, h = cv2.boundingRect(contour)
                sign_center = (x + w//2, y + h//2)
                
                # Check distance to sign
                distance = np.sqrt((point[0] - sign_center[0])**2 + (point[1] - sign_center[1])**2)
                if distance < 100:  # Within 100 pixels of exit sign
                    return True
        
        return False
    
    async def _check_tool_security(self, image: np.ndarray, tool: Dict) -> Optional[Dict]:
        """Check if tool is properly secured"""
        # Check if tool appears to be falling or unstable
        bbox = tool["bounding_box"]
        
        # Extract tool region
        x, y, w, h = bbox["x"], bbox["y"], bbox["width"], bbox["height"]
        tool_roi = image[y:y+h, x:x+w]
        
        # Analyze orientation (simplified)
        if h > w * 2:  # Very tall object might be unstable
            return {
                "type": "TOOLBOX_HAZARD",
                "tool_type": tool["tool_type"],
                "confidence": 0.7,
                "bounding_box": bbox,
                "description": f"Unstable {tool['tool_type']} - risk of falling",
                "severity": "MEDIUM",
                "violation_type": "unstable_placement",
                "recommendations": [
                    "Secure tool properly",
                    "Use appropriate tool storage",
                    "Check tool stability"
                ]
            }
        
        return None
    
    async def _detect_scattered_tools(self, tools: List[Dict]) -> List[Dict]:
        """Detect scattered tools that should be organized"""
        violations = []
        
        if len(tools) >= 3:  # Multiple tools detected
            # Check if tools are scattered (not grouped together)
            tool_centers = [tool["center"] for tool in tools]
            
            # Calculate average distance between tools
            total_distance = 0
            pair_count = 0
            
            for i in range(len(tool_centers)):
                for j in range(i + 1, len(tool_centers)):
                    distance = np.sqrt(
                        (tool_centers[i][0] - tool_centers[j][0])**2 + 
                        (tool_centers[i][1] - tool_centers[j][1])**2
                    )
                    total_distance += distance
                    pair_count += 1
            
            if pair_count > 0:
                avg_distance = total_distance / pair_count
                
                # If tools are too spread out
                if avg_distance > 150:  # pixels
                    # Create a bounding box encompassing all tools
                    all_x = [tool["bounding_box"]["x"] for tool in tools]
                    all_y = [tool["bounding_box"]["y"] for tool in tools]
                    all_x2 = [tool["bounding_box"]["x"] + tool["bounding_box"]["width"] for tool in tools]
                    all_y2 = [tool["bounding_box"]["y"] + tool["bounding_box"]["height"] for tool in tools]
                    
                    violation = {
                        "type": "TOOLBOX_HAZARD",
                        "tool_type": "multiple_tools",
                        "confidence": 0.8,
                        "bounding_box": {
                            "x": min(all_x),
                            "y": min(all_y),
                            "width": max(all_x2) - min(all_x),
                            "height": max(all_y2) - min(all_y)
                        },
                        "description": f"{len(tools)} tools scattered in work area",
                        "severity": "MEDIUM",
                        "violation_type": "scattered_tools",
                        "tool_count": len(tools),
                        "recommendations": [
                            "Organize tools in designated storage",
                            "Use toolbox or tool belt",
                            "Implement 5S workplace organization",
                            "Designate specific tool storage areas"
                        ]
                    }
                    violations.append(violation)
        
        return violations
    
    async def _check_toolbox_organization(self, image: np.ndarray, tools: List[Dict]) -> List[Dict]:
        """Check toolbox organization and accessibility"""
        violations = []
        
        # Find toolboxes
        toolboxes = [tool for tool in tools if "box" in tool["tool_type"].lower() or "container" in tool["tool_type"].lower()]
        
        for toolbox in toolboxes:
            # Check if toolbox is accessible
            accessibility_issue = await self._check_toolbox_accessibility(image, toolbox)
            if accessibility_issue:
                violations.append(accessibility_issue)
        
        return violations
    
    async def _check_toolbox_accessibility(self, image: np.ndarray, toolbox: Dict) -> Optional[Dict]:
        """Check if toolbox is accessible and properly positioned"""
        bbox = toolbox["bounding_box"]
        
        # Check if toolbox is too high or in awkward position
        image_height = image.shape[0]
        toolbox_y = bbox["y"]
        
        # If toolbox is in upper portion of image (might be too high)
        if toolbox_y < image_height * 0.3:
            return {
                "type": "TOOLBOX_HAZARD",
                "tool_type": "toolbox",
                "confidence": 0.6,
                "bounding_box": bbox,
                "description": "Toolbox positioned too high - accessibility concern",
                "severity": "LOW",
                "violation_type": "poor_accessibility",
                "recommendations": [
                    "Position toolbox at accessible height",
                    "Ensure ergonomic access",
                    "Provide step platform if needed"
                ]
            }
        
        return None
    
    def _get_violation_severity(self, violation_type: str) -> str:
        """Get severity level for violation type"""
        severity_map = {
            "walkway": "HIGH",
            "emergency_exit": "HIGH",
            "fire_zone": "HIGH",
            "electrical": "HIGH",
            "scattered_tools": "MEDIUM",
            "unstable_placement": "MEDIUM",
            "poor_accessibility": "LOW"
        }
        return severity_map.get(violation_type, "MEDIUM")
    
    def _get_tool_recommendations(self, violation_type: str, tool_category: str) -> List[str]:
        """Get recommendations for tool violations"""
        base_recommendations = {
            "walkway": [
                "Remove tool from walkway immediately",
                "Store in designated tool area",
                "Ensure clear passage for personnel"
            ],
            "emergency_exit": [
                "Clear emergency exit area immediately",
                "Store tools in proper location",
                "Maintain exit accessibility at all times"
            ],
            "electrical": [
                "Remove tools from electrical area",
                "Use proper tool storage",
                "Maintain electrical safety clearances"
            ],
            "scattered_tools": [
                "Organize tools in toolbox",
                "Implement tool management system",
                "Use tool belts or portable organizers"
            ]
        }
        
        recommendations = base_recommendations.get(violation_type, [
            "Store tools properly",
            "Follow tool management procedures"
        ])
        
        # Add category-specific recommendations
        if tool_category == "power_tools":
            recommendations.append("Ensure power tools are unplugged when not in use")
        elif tool_category == "cutting_tools":
            recommendations.append("Store cutting tools with blade guards")
        
        return recommendations
    
    async def get_performance_metrics(self) -> Dict[str, float]:
        """Get performance metrics"""
        return self.performance_metrics.copy()
    
    async def get_memory_usage(self) -> float:
        """Get memory usage"""
        if self.model and torch.cuda.is_available():
            return torch.cuda.memory_allocated() / 1024**2
        return 0.0
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.model:
            del self.model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        logger.info("Toolbox detector cleanup complete")
