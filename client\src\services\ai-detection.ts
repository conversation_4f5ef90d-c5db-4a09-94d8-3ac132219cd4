import { AIAnalysis, AIDetection } from '@/types/safety';

export class AIDetectionService {
  static async analyzeImage(imageData: string, detectionType: string): Promise<AIAnalysis> {
    try {
      const response = await fetch('/api/ai/analyze-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageData,
          type: detectionType
        })
      });

      if (!response.ok) {
        throw new Error('Failed to analyze image');
      }

      return await response.json();
    } catch (error) {
      console.error('AI Analysis Error:', error);
      throw error;
    }
  }

  static async processLiveFrame(videoElement: HTMLVideoElement): Promise<AIDetection[]> {
    // Capture frame from video
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    
    canvas.width = videoElement.videoWidth;
    canvas.height = videoElement.videoHeight;
    
    if (context) {
      context.drawImage(videoElement, 0, 0);
      const imageData = canvas.toDataURL('image/jpeg', 0.5);
      
      try {
        const analysis = await this.analyzeImage(imageData, 'LIVE_DETECTION');
        return analysis.detections;
      } catch (error) {
        console.error('Live frame processing error:', error);
        return [];
      }
    }
    
    return [];
  }

  static detectPPEViolations(detections: AIDetection[]): AIDetection[] {
    return detections.filter(detection => 
      detection.type === 'PPE_VIOLATION' && detection.confidence > 0.7
    );
  }

  static detectHazards(detections: AIDetection[]): AIDetection[] {
    const hazardTypes = ['TRIP_HAZARD', 'SPILL', 'OBSTRUCTION', 'UNSAFE_EQUIPMENT'];
    return detections.filter(detection => 
      hazardTypes.includes(detection.type) && detection.confidence > 0.6
    );
  }

  static calculateRiskScore(detections: AIDetection[]): number {
    if (detections.length === 0) return 100;

    const violationCount = this.detectPPEViolations(detections).length;
    const hazardCount = this.detectHazards(detections).length;
    
    const baseScore = 100;
    const violationPenalty = violationCount * 20;
    const hazardPenalty = hazardCount * 15;
    
    return Math.max(0, baseScore - violationPenalty - hazardPenalty);
  }
}
