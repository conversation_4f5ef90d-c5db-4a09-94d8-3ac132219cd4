import { useState } from 'react';
import { <PERSON> } from 'wouter';
import { 
  Shield, 
  ArrowLeft,
  Calendar,
  User,
  Clock,
  ArrowRight,
  Search,
  Tag
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

export default function BlogPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Posts', count: 24 },
    { id: 'ai-technology', name: 'AI Technology', count: 8 },
    { id: 'safety-tips', name: 'Safety Tips', count: 6 },
    { id: 'case-studies', name: 'Case Studies', count: 5 },
    { id: 'industry-news', name: 'Industry News', count: 3 },
    { id: 'product-updates', name: 'Product Updates', count: 2 }
  ];

  const featuredPost = {
    id: 1,
    title: "How AI Reduced Workplace Accidents by 85% at Manufacturing Giant",
    excerpt: "A comprehensive case study showing how our AI-powered safety system transformed safety protocols at a major manufacturing facility, preventing over 200 potential accidents in the first year.",
    author: "Dr. <PERSON>",
    date: "2024-01-15",
    readTime: "8 min read",
    category: "case-studies",
    image: "🏭",
    featured: true
  };

  const blogPosts = [
    {
      id: 2,
      title: "The Future of Computer Vision in Workplace Safety",
      excerpt: "Exploring how advanced computer vision algorithms are revolutionizing real-time hazard detection and PPE compliance monitoring.",
      author: "Michael Rodriguez",
      date: "2024-01-12",
      readTime: "6 min read",
      category: "ai-technology",
      image: "🤖"
    },
    {
      id: 3,
      title: "10 Essential PPE Compliance Tips for 2024",
      excerpt: "Updated guidelines and best practices for maintaining PPE compliance in modern workplaces, enhanced with AI monitoring.",
      author: "Lisa Thompson",
      date: "2024-01-10",
      readTime: "5 min read",
      category: "safety-tips",
      image: "🦺"
    },
    {
      id: 4,
      title: "Voice-Activated Safety Reporting: A Game Changer",
      excerpt: "How natural language processing is making incident reporting faster, more accurate, and hands-free for frontline workers.",
      author: "Emily Johnson",
      date: "2024-01-08",
      readTime: "4 min read",
      category: "ai-technology",
      image: "🎤"
    },
    {
      id: 5,
      title: "Construction Site Safety: AI vs Traditional Methods",
      excerpt: "Comparing the effectiveness of AI-powered safety monitoring against traditional safety protocols in construction environments.",
      author: "David Kim",
      date: "2024-01-05",
      readTime: "7 min read",
      category: "case-studies",
      image: "🏗️"
    },
    {
      id: 6,
      title: "New Feature: Predictive Analytics Dashboard",
      excerpt: "Introducing our latest feature that uses machine learning to predict and prevent safety incidents before they occur.",
      author: "Product Team",
      date: "2024-01-03",
      readTime: "3 min read",
      category: "product-updates",
      image: "📊"
    },
    {
      id: 7,
      title: "OSHA Compliance in the Age of AI",
      excerpt: "Understanding how AI-powered safety systems help organizations exceed OSHA requirements and maintain regulatory compliance.",
      author: "James Wilson",
      date: "2024-01-01",
      readTime: "6 min read",
      category: "industry-news",
      image: "📋"
    }
  ];

  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/">
              <Button variant="ghost" className="text-white hover:bg-white/10">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Home
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <Shield className="h-8 w-8 text-blue-400" />
              <span className="text-xl font-bold text-white">SafeGuard AI</span>
            </div>
            <Link href="/dashboard">
              <Button className="bg-blue-600 hover:bg-blue-700">
                Launch App
              </Button>
            </Link>
          </div>
        </div>
      </nav>

      {/* Hero */}
      <section className="pt-32 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Safety
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              {" "}Insights
            </span>
          </h1>
          <p className="text-xl text-white/80 mb-8">
            Latest insights, case studies, and innovations in AI-powered workplace safety
          </p>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row gap-6 mb-8">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/50" />
              <Input
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/50"
              />
            </div>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className={selectedCategory === category.id 
                    ? "bg-blue-600 hover:bg-blue-700" 
                    : "border-white/20 text-white hover:bg-white/10"
                  }
                >
                  {category.name} ({category.count})
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Post */}
      <section className="pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <Card className="bg-white/5 backdrop-blur-sm border-white/10 overflow-hidden">
            <div className="grid lg:grid-cols-2 gap-0">
              <div className="aspect-video lg:aspect-square bg-gradient-to-br from-blue-600/20 to-purple-600/20 flex items-center justify-center">
                <div className="text-8xl">{featuredPost.image}</div>
              </div>
              <div className="p-8">
                <Badge className="bg-blue-600 mb-4">Featured</Badge>
                <h2 className="text-3xl font-bold text-white mb-4">{featuredPost.title}</h2>
                <p className="text-white/80 mb-6">{featuredPost.excerpt}</p>
                <div className="flex items-center space-x-4 text-white/60 text-sm mb-6">
                  <div className="flex items-center space-x-1">
                    <User className="h-4 w-4" />
                    <span>{featuredPost.author}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>{new Date(featuredPost.date).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{featuredPost.readTime}</span>
                  </div>
                </div>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  Read Full Article
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredPosts.map((post) => (
              <Card key={post.id} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300">
                <CardHeader className="pb-4">
                  <div className="aspect-video bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-lg flex items-center justify-center mb-4">
                    <div className="text-4xl">{post.image}</div>
                  </div>
                  <div className="flex items-center space-x-2 mb-3">
                    <Badge variant="outline" className="border-blue-400 text-blue-400">
                      <Tag className="w-3 h-3 mr-1" />
                      {categories.find(c => c.id === post.category)?.name}
                    </Badge>
                  </div>
                  <CardTitle className="text-xl text-white leading-tight">{post.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-white/70 mb-4 line-clamp-3">{post.excerpt}</p>
                  <div className="flex items-center justify-between text-white/60 text-sm mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        <User className="h-3 w-3" />
                        <span>{post.author}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{post.readTime}</span>
                      </div>
                    </div>
                    <span>{new Date(post.date).toLocaleDateString()}</span>
                  </div>
                  <Button variant="outline" size="sm" className="w-full border-white/20 text-white hover:bg-white/10">
                    Read More
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredPosts.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-2xl font-bold text-white mb-2">No articles found</h3>
              <p className="text-white/70">Try adjusting your search or filter criteria</p>
            </div>
          )}
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Stay Updated with Safety Insights
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Get the latest articles, case studies, and AI safety innovations delivered to your inbox.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
            <Input
              placeholder="Enter your email"
              className="bg-white/20 border-white/30 text-white placeholder:text-white/70"
            />
            <Button className="bg-white text-blue-600 hover:bg-gray-100">
              Subscribe
            </Button>
          </div>
          <p className="text-white/70 text-sm mt-4">
            No spam, unsubscribe anytime. Read our privacy policy.
          </p>
        </div>
      </section>
    </div>
  );
}
