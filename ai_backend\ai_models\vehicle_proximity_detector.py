"""
Vehicle Proximity Detection Model
Detects dangerous proximity between people and vehicles/machinery
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import cv2
import numpy as np
from ultralytics import YOLO
import torch

logger = logging.getLogger(__name__)

class VehicleProximityDetector:
    """
    Vehicle proximity detection system for workplace safety
    """
    
    def __init__(self, model_path: Path):
        self.model_path = model_path
        self.model_path.mkdir(exist_ok=True)
        
        self.model: Optional[YOLO] = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Vehicle types and their safety distances (in pixels, would be calibrated to real distances)
        self.vehicle_types = {
            "forklift": {"min_distance": 100, "severity": "HIGH"},
            "truck": {"min_distance": 150, "severity": "HIGH"},
            "car": {"min_distance": 80, "severity": "MEDIUM"},
            "motorcycle": {"min_distance": 60, "severity": "MEDIUM"},
            "bicycle": {"min_distance": 40, "severity": "LOW"},
            "machinery": {"min_distance": 120, "severity": "HIGH"}
        }
        
        # COCO class IDs for vehicles
        self.vehicle_class_ids = {
            1: "bicycle",
            2: "car", 
            3: "motorcycle",
            5: "bus",
            7: "truck"
        }
        
        self.performance_metrics = {
            "total_detections": 0,
            "avg_inference_time": 0.0,
            "proximity_violations": 0
        }
    
    async def load_model(self):
        """Load vehicle proximity detection model"""
        try:
            # Load YOLO model
            self.model = YOLO("yolov8n.pt")
            self.model.to(self.device)
            
            logger.info("Vehicle proximity detector loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load vehicle proximity detector: {e}")
            raise
    
    async def detect(self, image: np.ndarray, confidence_threshold: float = 0.5) -> List[Dict[str, Any]]:
        """
        Detect dangerous proximity between people and vehicles
        """
        if not self.model:
            raise RuntimeError("Vehicle proximity detector model not loaded")
        
        start_time = time.time()
        
        try:
            detections = []
            
            # Step 1: Detect people and vehicles
            people = await self._detect_people(image, confidence_threshold)
            vehicles = await self._detect_vehicles(image, confidence_threshold)
            
            # Step 2: Analyze proximity between people and vehicles
            for person in people:
                for vehicle in vehicles:
                    proximity_analysis = await self._analyze_proximity(person, vehicle, image)
                    if proximity_analysis["is_violation"]:
                        detections.append(proximity_analysis["detection"])
            
            # Step 3: Detect vehicle movement patterns
            movement_violations = await self._detect_movement_violations(vehicles, image)
            detections.extend(movement_violations)
            
            # Step 4: Check for blind spot violations
            blind_spot_violations = await self._check_blind_spots(people, vehicles, image)
            detections.extend(blind_spot_violations)
            
            # Update performance metrics
            inference_time = time.time() - start_time
            self.performance_metrics["total_detections"] += len(detections)
            self.performance_metrics["proximity_violations"] += len(detections)
            self.performance_metrics["avg_inference_time"] = (
                (self.performance_metrics["avg_inference_time"] + inference_time) / 2
            )
            
            return detections
            
        except Exception as e:
            logger.error(f"Vehicle proximity detection error: {e}")
            return []
    
    async def _detect_people(self, image: np.ndarray, confidence_threshold: float) -> List[Dict]:
        """Detect people in the image"""
        results = self.model(image, classes=[0], conf=confidence_threshold, verbose=False)
        
        people = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0].cpu().numpy())
                    
                    person = {
                        "type": "person",
                        "confidence": confidence,
                        "bounding_box": {
                            "x": int(x1),
                            "y": int(y1),
                            "width": int(x2 - x1),
                            "height": int(y2 - y1)
                        },
                        "center": (int((x1 + x2) / 2), int((y1 + y2) / 2))
                    }
                    people.append(person)
        
        return people
    
    async def _detect_vehicles(self, image: np.ndarray, confidence_threshold: float) -> List[Dict]:
        """Detect vehicles in the image"""
        # Detect vehicles using specific class IDs
        vehicle_classes = list(self.vehicle_class_ids.keys())
        results = self.model(image, classes=vehicle_classes, conf=confidence_threshold, verbose=False)
        
        vehicles = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0].cpu().numpy())
                    class_id = int(box.cls[0].cpu().numpy())
                    
                    vehicle_type = self.vehicle_class_ids.get(class_id, "unknown_vehicle")
                    
                    vehicle = {
                        "type": vehicle_type,
                        "class_id": class_id,
                        "confidence": confidence,
                        "bounding_box": {
                            "x": int(x1),
                            "y": int(y1),
                            "width": int(x2 - x1),
                            "height": int(y2 - y1)
                        },
                        "center": (int((x1 + x2) / 2), int((y1 + y2) / 2)),
                        "safety_params": self.vehicle_types.get(vehicle_type, {"min_distance": 80, "severity": "MEDIUM"})
                    }
                    vehicles.append(vehicle)
        
        return vehicles
    
    async def _analyze_proximity(self, person: Dict, vehicle: Dict, image: np.ndarray) -> Dict[str, Any]:
        """Analyze proximity between a person and vehicle"""
        person_center = person["center"]
        vehicle_center = vehicle["center"]
        
        # Calculate distance between centers
        distance = np.sqrt(
            (person_center[0] - vehicle_center[0])**2 + 
            (person_center[1] - vehicle_center[1])**2
        )
        
        # Get minimum safe distance for this vehicle type
        min_distance = vehicle["safety_params"]["min_distance"]
        severity = vehicle["safety_params"]["severity"]
        
        # Check if distance is too close
        if distance < min_distance:
            # Calculate violation severity based on how close they are
            violation_ratio = distance / min_distance
            
            if violation_ratio < 0.5:
                actual_severity = "HIGH"
            elif violation_ratio < 0.8:
                actual_severity = severity
            else:
                actual_severity = "MEDIUM" if severity == "HIGH" else "LOW"
            
            # Create combined bounding box
            combined_bbox = self._create_combined_bbox(person["bounding_box"], vehicle["bounding_box"])
            
            return {
                "is_violation": True,
                "detection": {
                    "type": "VEHICLE_PROXIMITY",
                    "vehicle_type": vehicle["type"],
                    "confidence": min(person["confidence"], vehicle["confidence"]),
                    "bounding_box": combined_bbox,
                    "description": f"Person too close to {vehicle['type']} ({distance:.0f}px < {min_distance}px)",
                    "severity": actual_severity,
                    "distance": distance,
                    "min_safe_distance": min_distance,
                    "violation_ratio": violation_ratio,
                    "recommendations": self._get_proximity_recommendations(vehicle["type"], actual_severity)
                }
            }
        
        return {"is_violation": False}
    
    async def _detect_movement_violations(self, vehicles: List[Dict], image: np.ndarray) -> List[Dict]:
        """Detect vehicle movement violations"""
        violations = []
        
        # This would require frame-to-frame analysis for actual movement detection
        # For now, we'll detect potential movement hazards based on vehicle positioning
        
        for vehicle in vehicles:
            # Check if vehicle is in a restricted movement area
            movement_violation = await self._check_movement_restrictions(vehicle, image)
            if movement_violation:
                violations.append(movement_violation)
        
        return violations
    
    async def _check_movement_restrictions(self, vehicle: Dict, image: np.ndarray) -> Optional[Dict]:
        """Check if vehicle is in a restricted movement area"""
        vehicle_center = vehicle["center"]
        
        # Check if vehicle is near pedestrian areas
        pedestrian_area = await self._detect_pedestrian_areas(image, vehicle_center)
        
        if pedestrian_area:
            return {
                "type": "VEHICLE_PROXIMITY",
                "vehicle_type": vehicle["type"],
                "confidence": vehicle["confidence"],
                "bounding_box": vehicle["bounding_box"],
                "description": f"{vehicle['type']} in pedestrian area",
                "severity": "HIGH",
                "violation_type": "restricted_area",
                "recommendations": [
                    "Move vehicle away from pedestrian area",
                    "Use designated vehicle routes",
                    "Ensure proper traffic control",
                    "Post warning signs if necessary"
                ]
            }
        
        return None
    
    async def _detect_pedestrian_areas(self, image: np.ndarray, vehicle_center: Tuple[int, int]) -> bool:
        """Detect if vehicle is in a pedestrian area"""
        # Look for pedestrian crossing markings or walkway indicators
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Detect zebra crossing patterns
        zebra_pattern = await self._detect_zebra_crossing(gray, vehicle_center)
        if zebra_pattern:
            return True
        
        # Detect sidewalk edges
        sidewalk_area = await self._detect_sidewalk(image, vehicle_center)
        if sidewalk_area:
            return True
        
        return False
    
    async def _detect_zebra_crossing(self, gray_image: np.ndarray, point: Tuple[int, int]) -> bool:
        """Detect zebra crossing patterns"""
        # Extract region around the point
        x, y = point
        roi_size = 50
        x1, y1 = max(0, x - roi_size), max(0, y - roi_size)
        x2, y2 = min(gray_image.shape[1], x + roi_size), min(gray_image.shape[0], y + roi_size)
        
        roi = gray_image[y1:y2, x1:x2]
        
        if roi.size == 0:
            return False
        
        # Look for alternating light/dark stripes
        # Calculate horizontal intensity profile
        horizontal_profile = np.mean(roi, axis=0)
        
        # Find peaks and valleys
        if len(horizontal_profile) > 10:
            # Simple pattern detection for alternating stripes
            diff = np.diff(horizontal_profile)
            sign_changes = np.sum(np.diff(np.sign(diff)) != 0)
            
            # If there are many sign changes, it might be a zebra pattern
            if sign_changes > len(horizontal_profile) * 0.3:
                return True
        
        return False
    
    async def _detect_sidewalk(self, image: np.ndarray, point: Tuple[int, int]) -> bool:
        """Detect if point is on a sidewalk"""
        # Look for sidewalk characteristics (different texture, color)
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Extract region around point
        x, y = point
        roi_size = 30
        x1, y1 = max(0, x - roi_size), max(0, y - roi_size)
        x2, y2 = min(image.shape[1], x + roi_size), min(image.shape[0], y + roi_size)
        
        roi_hsv = hsv[y1:y2, x1:x2]
        
        if roi_hsv.size == 0:
            return False
        
        # Check for typical sidewalk colors (gray, concrete)
        gray_lower = np.array([0, 0, 50])
        gray_upper = np.array([180, 30, 200])
        
        mask = cv2.inRange(roi_hsv, gray_lower, gray_upper)
        gray_ratio = np.sum(mask > 0) / mask.size
        
        # If most of the area is gray/concrete colored, might be sidewalk
        return gray_ratio > 0.7
    
    async def _check_blind_spots(self, people: List[Dict], vehicles: List[Dict], image: np.ndarray) -> List[Dict]:
        """Check for people in vehicle blind spots"""
        violations = []
        
        for vehicle in vehicles:
            blind_spot_areas = self._calculate_blind_spots(vehicle)
            
            for person in people:
                if self._is_in_blind_spot(person["center"], blind_spot_areas):
                    combined_bbox = self._create_combined_bbox(person["bounding_box"], vehicle["bounding_box"])
                    
                    violation = {
                        "type": "VEHICLE_PROXIMITY",
                        "vehicle_type": vehicle["type"],
                        "confidence": min(person["confidence"], vehicle["confidence"]),
                        "bounding_box": combined_bbox,
                        "description": f"Person in {vehicle['type']} blind spot",
                        "severity": "HIGH",
                        "violation_type": "blind_spot",
                        "recommendations": [
                            "Move person out of blind spot immediately",
                            "Use spotter when operating vehicle",
                            "Install blind spot mirrors/cameras",
                            "Implement vehicle safety protocols"
                        ]
                    }
                    violations.append(violation)
        
        return violations
    
    def _calculate_blind_spots(self, vehicle: Dict) -> List[Dict]:
        """Calculate blind spot areas for a vehicle"""
        vehicle_bbox = vehicle["bounding_box"]
        vehicle_center = vehicle["center"]
        
        # Simplified blind spot calculation based on vehicle type
        blind_spots = []
        
        if vehicle["type"] in ["truck", "bus"]:
            # Large vehicles have significant blind spots
            # Right side blind spot
            blind_spots.append({
                "area": "right_side",
                "polygon": [
                    (vehicle_bbox["x"] + vehicle_bbox["width"], vehicle_bbox["y"]),
                    (vehicle_bbox["x"] + vehicle_bbox["width"] + 80, vehicle_bbox["y"]),
                    (vehicle_bbox["x"] + vehicle_bbox["width"] + 80, vehicle_bbox["y"] + vehicle_bbox["height"]),
                    (vehicle_bbox["x"] + vehicle_bbox["width"], vehicle_bbox["y"] + vehicle_bbox["height"])
                ]
            })
            
            # Rear blind spot
            blind_spots.append({
                "area": "rear",
                "polygon": [
                    (vehicle_bbox["x"], vehicle_bbox["y"] + vehicle_bbox["height"]),
                    (vehicle_bbox["x"] + vehicle_bbox["width"], vehicle_bbox["y"] + vehicle_bbox["height"]),
                    (vehicle_bbox["x"] + vehicle_bbox["width"], vehicle_bbox["y"] + vehicle_bbox["height"] + 60),
                    (vehicle_bbox["x"], vehicle_bbox["y"] + vehicle_bbox["height"] + 60)
                ]
            })
        
        elif vehicle["type"] == "forklift":
            # Forklift has blind spots due to mast and load
            blind_spots.append({
                "area": "front_mast",
                "polygon": [
                    (vehicle_bbox["x"], vehicle_bbox["y"] - 40),
                    (vehicle_bbox["x"] + vehicle_bbox["width"], vehicle_bbox["y"] - 40),
                    (vehicle_bbox["x"] + vehicle_bbox["width"], vehicle_bbox["y"]),
                    (vehicle_bbox["x"], vehicle_bbox["y"])
                ]
            })
        
        return blind_spots
    
    def _is_in_blind_spot(self, point: Tuple[int, int], blind_spots: List[Dict]) -> bool:
        """Check if point is in any blind spot area"""
        for blind_spot in blind_spots:
            if self._point_in_polygon(point, blind_spot["polygon"]):
                return True
        return False
    
    def _point_in_polygon(self, point: Tuple[int, int], polygon: List[Tuple[int, int]]) -> bool:
        """Check if point is inside polygon using ray casting"""
        x, y = point
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def _create_combined_bbox(self, bbox1: Dict, bbox2: Dict) -> Dict:
        """Create a bounding box that encompasses both input boxes"""
        x1_min = min(bbox1["x"], bbox2["x"])
        y1_min = min(bbox1["y"], bbox2["y"])
        x1_max = max(bbox1["x"] + bbox1["width"], bbox2["x"] + bbox2["width"])
        y1_max = max(bbox1["y"] + bbox1["height"], bbox2["y"] + bbox2["height"])
        
        return {
            "x": x1_min,
            "y": y1_min,
            "width": x1_max - x1_min,
            "height": y1_max - y1_min
        }
    
    def _get_proximity_recommendations(self, vehicle_type: str, severity: str) -> List[str]:
        """Get recommendations for proximity violations"""
        base_recommendations = [
            f"Maintain safe distance from {vehicle_type}",
            "Be aware of vehicle movement",
            "Use designated walkways"
        ]
        
        vehicle_specific = {
            "forklift": [
                "Stay clear of forklift operation area",
                "Make eye contact with operator",
                "Never walk under raised forks"
            ],
            "truck": [
                "Stay visible to truck driver",
                "Avoid blind spots",
                "Use designated loading areas"
            ],
            "car": [
                "Be visible to driver",
                "Use crosswalks",
                "Follow traffic signals"
            ]
        }
        
        recommendations = base_recommendations + vehicle_specific.get(vehicle_type, [])
        
        if severity == "HIGH":
            recommendations.insert(0, "IMMEDIATE: Move to safe distance")
        
        return recommendations
    
    async def get_performance_metrics(self) -> Dict[str, float]:
        """Get performance metrics"""
        return self.performance_metrics.copy()
    
    async def get_memory_usage(self) -> float:
        """Get memory usage"""
        if self.model and torch.cuda.is_available():
            return torch.cuda.memory_allocated() / 1024**2
        return 0.0
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.model:
            del self.model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        logger.info("Vehicle proximity detector cleanup complete")
