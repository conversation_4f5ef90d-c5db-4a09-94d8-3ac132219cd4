"""
Fall Detection Model
Advanced fall detection using pose estimation and motion analysis
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import cv2
import numpy as np
import torch
from ultralytics import YOLO
import mediapipe as mp

logger = logging.getLogger(__name__)

class FallDetector:
    """
    Advanced fall detection using pose estimation and motion analysis
    """
    
    def __init__(self, model_path: Path):
        self.model_path = model_path
        self.model_path.mkdir(exist_ok=True)
        
        self.pose_model = None
        self.yolo_model = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # MediaPipe pose detection
        self.mp_pose = mp.solutions.pose
        self.mp_drawing = mp.solutions.drawing_utils
        self.pose_detector = None
        
        # Fall detection parameters
        self.fall_threshold_angle = 45  # degrees
        self.fall_threshold_ratio = 0.8  # height/width ratio
        self.motion_threshold = 50      # pixel movement threshold
        
        # Tracking variables for temporal analysis
        self.previous_poses = []
        self.pose_history_length = 10
        
        self.performance_metrics = {
            "total_detections": 0,
            "avg_inference_time": 0.0,
            "false_positive_rate": 0.0
        }
    
    async def load_model(self):
        """Load fall detection models"""
        try:
            logger.info("Loading fall detection models...")
            
            # Load YOLO for person detection
            self.yolo_model = YOLO("yolov8n.pt")
            self.yolo_model.to(self.device)
            
            # Initialize MediaPipe pose detection
            self.pose_detector = self.mp_pose.Pose(
                static_image_mode=False,
                model_complexity=1,
                enable_segmentation=False,
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5
            )
            
            logger.info("Fall detector loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load fall detection models: {e}")
            raise
    
    async def detect(self, image: np.ndarray, confidence_threshold: float = 0.5) -> List[Dict[str, Any]]:
        """
        Detect falls in image using multiple methods
        """
        if not self.yolo_model or not self.pose_detector:
            raise RuntimeError("Fall detection models not loaded")
        
        start_time = time.time()
        
        try:
            detections = []
            
            # Step 1: Detect people in the image
            people = await self._detect_people(image, confidence_threshold)
            
            # Step 2: Analyze each person for fall indicators
            for person in people:
                fall_analysis = await self._analyze_person_for_fall(image, person)
                
                if fall_analysis["is_fall"]:
                    detection = {
                        "type": "FALL_DETECTED",
                        "confidence": fall_analysis["confidence"],
                        "bounding_box": person["bounding_box"],
                        "description": fall_analysis["description"],
                        "severity": "HIGH",
                        "fall_type": fall_analysis["fall_type"],
                        "pose_analysis": fall_analysis["pose_data"],
                        "recommendations": [
                            "Immediate medical attention required",
                            "Check for consciousness and injuries",
                            "Do not move person unless necessary",
                            "Call emergency services if serious"
                        ]
                    }
                    detections.append(detection)
            
            # Step 3: Temporal analysis for motion-based fall detection
            motion_falls = await self._detect_motion_based_falls(image)
            detections.extend(motion_falls)
            
            # Update performance metrics
            inference_time = time.time() - start_time
            self.performance_metrics["total_detections"] += len(detections)
            self.performance_metrics["avg_inference_time"] = (
                (self.performance_metrics["avg_inference_time"] + inference_time) / 2
            )
            
            return detections
            
        except Exception as e:
            logger.error(f"Fall detection error: {e}")
            return []
    
    async def _detect_people(self, image: np.ndarray, confidence_threshold: float) -> List[Dict]:
        """Detect people in the image using YOLO"""
        results = self.yolo_model(image, classes=[0], conf=confidence_threshold, verbose=False)
        
        people = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0].cpu().numpy())
                    
                    person = {
                        "confidence": confidence,
                        "bounding_box": {
                            "x": int(x1),
                            "y": int(y1),
                            "width": int(x2 - x1),
                            "height": int(y2 - y1)
                        }
                    }
                    people.append(person)
        
        return people
    
    async def _analyze_person_for_fall(self, image: np.ndarray, person: Dict) -> Dict[str, Any]:
        """Analyze a person for fall indicators using pose estimation"""
        bbox = person["bounding_box"]
        
        # Extract person region
        x, y, w, h = bbox["x"], bbox["y"], bbox["width"], bbox["height"]
        person_roi = image[y:y+h, x:x+w]
        
        # Convert BGR to RGB for MediaPipe
        rgb_roi = cv2.cvtColor(person_roi, cv2.COLOR_BGR2RGB)
        
        # Get pose landmarks
        pose_results = self.pose_detector.process(rgb_roi)
        
        if not pose_results.pose_landmarks:
            return {
                "is_fall": False,
                "confidence": 0.0,
                "description": "No pose detected",
                "fall_type": "none",
                "pose_data": {}
            }
        
        # Analyze pose for fall indicators
        landmarks = pose_results.pose_landmarks.landmark
        
        # Method 1: Analyze body orientation
        orientation_analysis = self._analyze_body_orientation(landmarks)
        
        # Method 2: Analyze body proportions
        proportion_analysis = self._analyze_body_proportions(landmarks, w, h)
        
        # Method 3: Analyze key joint positions
        joint_analysis = self._analyze_joint_positions(landmarks)
        
        # Combine analyses
        fall_indicators = [
            orientation_analysis["is_fall"],
            proportion_analysis["is_fall"],
            joint_analysis["is_fall"]
        ]
        
        fall_count = sum(fall_indicators)
        is_fall = fall_count >= 2  # Require at least 2 indicators
        
        # Calculate confidence
        confidence = fall_count / len(fall_indicators)
        
        # Determine fall type
        fall_type = "none"
        if is_fall:
            if orientation_analysis["is_fall"]:
                fall_type = "horizontal_fall"
            elif joint_analysis["is_fall"]:
                fall_type = "collapse_fall"
            else:
                fall_type = "general_fall"
        
        description = f"Fall detected: {fall_type}" if is_fall else "No fall detected"
        
        return {
            "is_fall": is_fall,
            "confidence": confidence,
            "description": description,
            "fall_type": fall_type,
            "pose_data": {
                "orientation": orientation_analysis,
                "proportions": proportion_analysis,
                "joints": joint_analysis
            }
        }
    
    def _analyze_body_orientation(self, landmarks) -> Dict[str, Any]:
        """Analyze body orientation to detect horizontal falls"""
        # Get key points for body orientation
        left_shoulder = landmarks[self.mp_pose.PoseLandmark.LEFT_SHOULDER.value]
        right_shoulder = landmarks[self.mp_pose.PoseLandmark.RIGHT_SHOULDER.value]
        left_hip = landmarks[self.mp_pose.PoseLandmark.LEFT_HIP.value]
        right_hip = landmarks[self.mp_pose.PoseLandmark.RIGHT_HIP.value]
        
        # Calculate shoulder line angle
        shoulder_angle = np.arctan2(
            right_shoulder.y - left_shoulder.y,
            right_shoulder.x - left_shoulder.x
        ) * 180 / np.pi
        
        # Calculate hip line angle
        hip_angle = np.arctan2(
            right_hip.y - left_hip.y,
            right_hip.x - left_hip.x
        ) * 180 / np.pi
        
        # Calculate torso angle (vertical axis)
        torso_center_x = (left_shoulder.x + right_shoulder.x + left_hip.x + right_hip.x) / 4
        torso_center_y = (left_shoulder.y + right_shoulder.y + left_hip.y + right_hip.y) / 4
        
        head_y = landmarks[self.mp_pose.PoseLandmark.NOSE.value].y
        
        torso_angle = np.arctan2(
            torso_center_y - head_y,
            0  # Vertical reference
        ) * 180 / np.pi
        
        # Check if body is horizontal (fall indicator)
        is_horizontal = abs(shoulder_angle) > self.fall_threshold_angle or abs(hip_angle) > self.fall_threshold_angle
        
        return {
            "is_fall": is_horizontal,
            "shoulder_angle": shoulder_angle,
            "hip_angle": hip_angle,
            "torso_angle": torso_angle
        }
    
    def _analyze_body_proportions(self, landmarks, width: int, height: int) -> Dict[str, Any]:
        """Analyze body proportions to detect falls"""
        # Get bounding box of pose
        x_coords = [lm.x for lm in landmarks]
        y_coords = [lm.y for lm in landmarks]
        
        pose_width = (max(x_coords) - min(x_coords)) * width
        pose_height = (max(y_coords) - min(y_coords)) * height
        
        # Calculate aspect ratio
        if pose_height > 0:
            aspect_ratio = pose_width / pose_height
        else:
            aspect_ratio = 0
        
        # Normal standing person has height > width
        # Fallen person has width > height
        is_fall = aspect_ratio > self.fall_threshold_ratio
        
        return {
            "is_fall": is_fall,
            "aspect_ratio": aspect_ratio,
            "pose_width": pose_width,
            "pose_height": pose_height
        }
    
    def _analyze_joint_positions(self, landmarks) -> Dict[str, Any]:
        """Analyze joint positions for fall indicators"""
        # Get key joint positions
        head = landmarks[self.mp_pose.PoseLandmark.NOSE.value]
        left_shoulder = landmarks[self.mp_pose.PoseLandmark.LEFT_SHOULDER.value]
        right_shoulder = landmarks[self.mp_pose.PoseLandmark.RIGHT_SHOULDER.value]
        left_hip = landmarks[self.mp_pose.PoseLandmark.LEFT_HIP.value]
        right_hip = landmarks[self.mp_pose.PoseLandmark.RIGHT_HIP.value]
        left_knee = landmarks[self.mp_pose.PoseLandmark.LEFT_KNEE.value]
        right_knee = landmarks[self.mp_pose.PoseLandmark.RIGHT_KNEE.value]
        
        # Calculate average positions
        shoulder_y = (left_shoulder.y + right_shoulder.y) / 2
        hip_y = (left_hip.y + right_hip.y) / 2
        knee_y = (left_knee.y + right_knee.y) / 2
        
        # Check for abnormal joint relationships
        # In a fall, head might be at same level or below shoulders/hips
        head_below_shoulders = head.y >= shoulder_y
        head_below_hips = head.y >= hip_y
        
        # Check if knees are above hips (person might be collapsed)
        knees_above_hips = knee_y < hip_y
        
        # Combine indicators
        fall_indicators = [head_below_shoulders, head_below_hips, knees_above_hips]
        is_fall = sum(fall_indicators) >= 2
        
        return {
            "is_fall": is_fall,
            "head_below_shoulders": head_below_shoulders,
            "head_below_hips": head_below_hips,
            "knees_above_hips": knees_above_hips,
            "joint_positions": {
                "head_y": head.y,
                "shoulder_y": shoulder_y,
                "hip_y": hip_y,
                "knee_y": knee_y
            }
        }
    
    async def _detect_motion_based_falls(self, image: np.ndarray) -> List[Dict]:
        """Detect falls based on motion analysis across frames"""
        # This would require frame-to-frame analysis
        # For now, return empty list as this requires video stream processing
        return []
    
    def _update_pose_history(self, pose_data: Dict):
        """Update pose history for temporal analysis"""
        self.previous_poses.append(pose_data)
        
        # Keep only recent poses
        if len(self.previous_poses) > self.pose_history_length:
            self.previous_poses.pop(0)
    
    def _analyze_motion_patterns(self) -> Dict[str, Any]:
        """Analyze motion patterns from pose history"""
        if len(self.previous_poses) < 3:
            return {"is_fall": False, "motion_type": "insufficient_data"}
        
        # Analyze velocity and acceleration of key points
        # This would involve calculating movement vectors across frames
        
        return {"is_fall": False, "motion_type": "normal"}
    
    async def get_performance_metrics(self) -> Dict[str, float]:
        """Get performance metrics"""
        return self.performance_metrics.copy()
    
    async def get_memory_usage(self) -> float:
        """Get memory usage"""
        memory_usage = 0.0
        
        if self.yolo_model and torch.cuda.is_available():
            memory_usage += torch.cuda.memory_allocated() / 1024**2
        
        # MediaPipe uses CPU, estimate memory usage
        if self.pose_detector:
            memory_usage += 50.0  # Approximate MB
        
        return memory_usage
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.yolo_model:
            del self.yolo_model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        
        if self.pose_detector:
            self.pose_detector.close()
        
        logger.info("Fall detector cleanup complete")
