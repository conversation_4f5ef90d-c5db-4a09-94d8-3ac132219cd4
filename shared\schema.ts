import { sql } from "drizzle-orm";
import { pgTable, text, varchar, timestamp, integer, boolean, jsonb } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  role: text("role").notNull().default("inspector"),
  location: text("location"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const incidents = pgTable("incidents", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").notNull().references(() => users.id),
  type: text("type").notNull(), // PPE_VIOLATION, FALL_DETECTION, ZONE_VIOLATION, etc.
  severity: text("severity").notNull(), // HIGH, MEDIUM, LOW
  description: text("description").notNull(),
  location: text("location"),
  gpsCoordinates: jsonb("gps_coordinates"),
  imageUrl: text("image_url"),
  videoUrl: text("video_url"),
  audioUrl: text("audio_url"),
  aiAnalysis: jsonb("ai_analysis"), // AI detection results
  status: text("status").notNull().default("open"), // open, investigating, resolved
  createdAt: timestamp("created_at").defaultNow().notNull(),
  resolvedAt: timestamp("resolved_at"),
});

export const safetyChecks = pgTable("safety_checks", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").notNull().references(() => users.id),
  type: text("type").notNull(), // PHOTO_CHECK, VIDEO_CHECK, PPE_CHECK, etc.
  location: text("location"),
  gpsCoordinates: jsonb("gps_coordinates"),
  results: jsonb("results"), // AI analysis results
  complianceScore: integer("compliance_score"), // 0-100
  imageUrl: text("image_url"),
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const toolboxTalks = pgTable("toolbox_talks", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  conductorId: varchar("conductor_id").notNull().references(() => users.id),
  title: text("title").notNull(),
  topic: text("topic").notNull(),
  content: text("content"),
  location: text("location"),
  attendees: jsonb("attendees"), // Array of user IDs
  audioRecording: text("audio_recording"),
  duration: integer("duration"), // in minutes
  scheduledAt: timestamp("scheduled_at"),
  conductedAt: timestamp("conducted_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const documents = pgTable("documents", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").notNull().references(() => users.id),
  name: text("name").notNull(),
  type: text("type").notNull(), // SOP, MSDS, PERMIT, etc.
  fileUrl: text("file_url").notNull(),
  status: text("status").notNull().default("active"), // active, expired, pending
  expiryDate: timestamp("expiry_date"),
  tags: jsonb("tags"), // Array of strings
  gapAnalysis: jsonb("gap_analysis"), // Missing documents analysis
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const alerts = pgTable("alerts", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  incidentId: varchar("incident_id").references(() => incidents.id),
  userId: varchar("user_id").notNull().references(() => users.id),
  type: text("type").notNull(), // EMERGENCY, WARNING, INFO
  message: text("message").notNull(),
  channels: jsonb("channels"), // SMS, EMAIL, WHATSAPP, PUSH
  sent: boolean("sent").default(false),
  sentAt: timestamp("sent_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  incidents: many(incidents),
  safetyChecks: many(safetyChecks),
  toolboxTalks: many(toolboxTalks),
  documents: many(documents),
  alerts: many(alerts),
}));

export const incidentsRelations = relations(incidents, ({ one, many }) => ({
  user: one(users, {
    fields: [incidents.userId],
    references: [users.id],
  }),
  alerts: many(alerts),
}));

export const safetyChecksRelations = relations(safetyChecks, ({ one }) => ({
  user: one(users, {
    fields: [safetyChecks.userId],
    references: [users.id],
  }),
}));

export const toolboxTalksRelations = relations(toolboxTalks, ({ one }) => ({
  conductor: one(users, {
    fields: [toolboxTalks.conductorId],
    references: [users.id],
  }),
}));

export const documentsRelations = relations(documents, ({ one }) => ({
  user: one(users, {
    fields: [documents.userId],
    references: [users.id],
  }),
}));

export const alertsRelations = relations(alerts, ({ one }) => ({
  user: one(users, {
    fields: [alerts.userId],
    references: [users.id],
  }),
  incident: one(incidents, {
    fields: [alerts.incidentId],
    references: [incidents.id],
  }),
}));

// Insert schemas
export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  createdAt: true,
});

export const insertIncidentSchema = createInsertSchema(incidents).omit({
  id: true,
  createdAt: true,
  resolvedAt: true,
});

export const insertSafetyCheckSchema = createInsertSchema(safetyChecks).omit({
  id: true,
  createdAt: true,
});

export const insertToolboxTalkSchema = createInsertSchema(toolboxTalks).omit({
  id: true,
  createdAt: true,
});

export const insertDocumentSchema = createInsertSchema(documents).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertAlertSchema = createInsertSchema(alerts).omit({
  id: true,
  createdAt: true,
  sentAt: true,
});

// Types
export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export type InsertIncident = z.infer<typeof insertIncidentSchema>;
export type Incident = typeof incidents.$inferSelect;

export type InsertSafetyCheck = z.infer<typeof insertSafetyCheckSchema>;
export type SafetyCheck = typeof safetyChecks.$inferSelect;

export type InsertToolboxTalk = z.infer<typeof insertToolboxTalkSchema>;
export type ToolboxTalk = typeof toolboxTalks.$inferSelect;

export type InsertDocument = z.infer<typeof insertDocumentSchema>;
export type Document = typeof documents.$inferSelect;

export type InsertAlert = z.infer<typeof insertAlertSchema>;
export type Alert = typeof alerts.$inferSelect;
