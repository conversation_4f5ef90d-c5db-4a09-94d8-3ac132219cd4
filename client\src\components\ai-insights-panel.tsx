import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Brain, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Target,
  Lightbulb,
  BarChart3,
  Zap
} from 'lucide-react';

interface AIInsight {
  id: string;
  type: 'prediction' | 'recommendation' | 'pattern' | 'alert';
  title: string;
  description: string;
  confidence: number;
  priority: 'high' | 'medium' | 'low';
  timestamp: string;
  actionable: boolean;
}

interface AIInsightsPanelProps {
  incidents: any[];
}

export default function AIInsightsPanel({ incidents }: AIInsightsPanelProps) {
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [selectedInsight, setSelectedInsight] = useState<string | null>(null);

  useEffect(() => {
    generateAIInsights();
    
    // Update insights every 30 seconds
    const interval = setInterval(generateAIInsights, 30000);
    return () => clearInterval(interval);
  }, [incidents]);

  const generateAIInsights = () => {
    const newInsights: AIInsight[] = [
      {
        id: '1',
        type: 'prediction',
        title: 'Increased Fall Risk Detected',
        description: 'AI models predict 23% higher fall risk in Zone B based on recent activity patterns and environmental factors.',
        confidence: 87.3,
        priority: 'high',
        timestamp: '2 min ago',
        actionable: true
      },
      {
        id: '2',
        type: 'pattern',
        title: 'PPE Compliance Pattern',
        description: 'Workers show 15% lower PPE compliance during shift changes. Recommend additional safety briefings.',
        confidence: 92.1,
        priority: 'medium',
        timestamp: '5 min ago',
        actionable: true
      },
      {
        id: '3',
        type: 'recommendation',
        title: 'Optimize Camera Placement',
        description: 'AI suggests repositioning Camera 3 to improve detection coverage by 34% in the machinery area.',
        confidence: 78.9,
        priority: 'medium',
        timestamp: '8 min ago',
        actionable: true
      },
      {
        id: '4',
        type: 'alert',
        title: 'Anomalous Behavior Detected',
        description: 'Unusual movement patterns detected near chemical storage. Recommend immediate investigation.',
        confidence: 94.7,
        priority: 'high',
        timestamp: '12 min ago',
        actionable: true
      },
      {
        id: '5',
        type: 'prediction',
        title: 'Equipment Maintenance Alert',
        description: 'Predictive models indicate Conveyor Belt 2 may require maintenance within 48 hours based on vibration analysis.',
        confidence: 81.2,
        priority: 'low',
        timestamp: '15 min ago',
        actionable: true
      }
    ];

    setInsights(newInsights);
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'prediction': return TrendingUp;
      case 'recommendation': return Lightbulb;
      case 'pattern': return BarChart3;
      case 'alert': return AlertTriangle;
      default: return Brain;
    }
  };

  const getInsightColor = (type: string, priority: string) => {
    if (priority === 'high') return 'text-safety-red';
    if (type === 'prediction') return 'text-ai-primary';
    if (type === 'recommendation') return 'text-safety-orange';
    if (type === 'pattern') return 'text-safety-purple';
    if (type === 'alert') return 'text-safety-red';
    return 'text-gray-600';
  };

  const getPriorityBadge = (priority: string) => {
    const colors = {
      high: 'bg-safety-red text-white',
      medium: 'bg-safety-orange text-white',
      low: 'bg-safety-green text-white'
    };
    return colors[priority as keyof typeof colors] || 'bg-gray-500 text-white';
  };

  const handleInsightAction = (insight: AIInsight) => {
    // Simulate taking action on insight
    console.log('Taking action on insight:', insight.title);
  };

  return (
    <Card className="glass-card border-0">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold flex items-center">
            <Brain className="w-5 h-5 mr-2 text-ai-neural" />
            AI Insights & Predictions
          </h3>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-ai-accent rounded-full animate-pulse"></div>
            <span className="text-xs text-gray-600">Live Analysis</span>
          </div>
        </div>

        {/* AI Performance Summary */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center p-3 glass rounded-lg">
            <Target className="w-6 h-6 mx-auto mb-2 text-ai-primary" />
            <div className="text-lg font-bold text-ai-primary">94.2%</div>
            <div className="text-xs text-gray-600">Accuracy</div>
          </div>
          <div className="text-center p-3 glass rounded-lg">
            <Zap className="w-6 h-6 mx-auto mb-2 text-safety-green" />
            <div className="text-lg font-bold text-safety-green">156ms</div>
            <div className="text-xs text-gray-600">Response Time</div>
          </div>
          <div className="text-center p-3 glass rounded-lg">
            <CheckCircle className="w-6 h-6 mx-auto mb-2 text-safety-cyan" />
            <div className="text-lg font-bold text-safety-cyan">{insights.length}</div>
            <div className="text-xs text-gray-600">Active Insights</div>
          </div>
        </div>

        {/* Insights List */}
        <div className="space-y-3">
          {insights.map((insight) => {
            const IconComponent = getInsightIcon(insight.type);
            const isSelected = selectedInsight === insight.id;
            
            return (
              <div
                key={insight.id}
                className={`p-4 rounded-lg border transition-all duration-200 cursor-pointer ${
                  isSelected 
                    ? 'bg-ai-primary/10 border-ai-primary' 
                    : 'bg-white/50 border-gray-200 hover:bg-white/80'
                }`}
                onClick={() => setSelectedInsight(isSelected ? null : insight.id)}
              >
                <div className="flex items-start space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    insight.priority === 'high' ? 'bg-safety-red/20' :
                    insight.priority === 'medium' ? 'bg-safety-orange/20' :
                    'bg-safety-green/20'
                  }`}>
                    <IconComponent className={`w-4 h-4 ${getInsightColor(insight.type, insight.priority)}`} />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium text-sm text-gray-900 truncate">
                        {insight.title}
                      </h4>
                      <div className="flex items-center space-x-2">
                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${getPriorityBadge(insight.priority)}`}>
                          {insight.priority.toUpperCase()}
                        </span>
                        <span className="text-xs text-gray-500">{insight.confidence}%</span>
                      </div>
                    </div>
                    
                    <p className="text-xs text-gray-600 mb-2">
                      {insight.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Clock className="w-3 h-3 text-gray-400" />
                        <span className="text-xs text-gray-500">{insight.timestamp}</span>
                      </div>
                      
                      {insight.actionable && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-xs h-6 px-2"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleInsightAction(insight);
                          }}
                        >
                          Take Action
                        </Button>
                      )}
                    </div>
                    
                    {/* Confidence Bar */}
                    <div className="mt-2">
                      <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className={`h-full bg-gradient-to-r from-ai-primary to-ai-secondary transition-all duration-1000 ${
                            insight.confidence >= 90 ? 'w-full' :
                            insight.confidence >= 80 ? 'w-5/6' :
                            insight.confidence >= 70 ? 'w-4/5' :
                            insight.confidence >= 60 ? 'w-3/5' :
                            insight.confidence >= 50 ? 'w-1/2' :
                            insight.confidence >= 40 ? 'w-2/5' :
                            insight.confidence >= 30 ? 'w-1/3' :
                            insight.confidence >= 20 ? 'w-1/5' :
                            insight.confidence >= 10 ? 'w-1/6' : 'w-1/12'
                          }`}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Expanded Details */}
                {isSelected && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="grid grid-cols-2 gap-4 text-xs">
                      <div>
                        <span className="font-medium text-gray-700">Model Used:</span>
                        <span className="ml-2 text-gray-600">
                          {insight.type === 'prediction' ? 'Predictive Analytics v2.1' :
                           insight.type === 'pattern' ? 'Pattern Recognition v1.8' :
                           insight.type === 'recommendation' ? 'Optimization Engine v3.0' :
                           'Anomaly Detection v2.3'}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Data Sources:</span>
                        <span className="ml-2 text-gray-600">
                          Camera feeds, Sensors, Historical data
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Impact Score:</span>
                        <span className="ml-2 text-gray-600">
                          {insight.priority === 'high' ? '8.7/10' :
                           insight.priority === 'medium' ? '6.2/10' : '4.1/10'}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Next Review:</span>
                        <span className="ml-2 text-gray-600">
                          {insight.priority === 'high' ? '15 min' :
                           insight.priority === 'medium' ? '1 hour' : '4 hours'}
                        </span>
                      </div>
                    </div>
                    
                    {/* Action Buttons */}
                    <div className="flex space-x-2 mt-4">
                      <Button size="sm" className="bg-ai-primary hover:bg-ai-primary/80 text-white">
                        Implement Suggestion
                      </Button>
                      <Button size="sm" variant="outline">
                        Schedule Review
                      </Button>
                      <Button size="sm" variant="ghost" className="text-gray-500">
                        Dismiss
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* AI Learning Status */}
        <div className="mt-6 p-4 bg-gradient-to-r from-ai-primary/10 to-ai-secondary/10 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Brain className="w-5 h-5 text-ai-neural" />
              <span className="font-medium text-gray-900">AI Learning Status</span>
            </div>
            <div className="text-sm text-gray-600">
              Model accuracy improving by 0.3% daily
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-600">
            Neural networks have processed 2.3M safety events and continue learning from new data patterns.
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
