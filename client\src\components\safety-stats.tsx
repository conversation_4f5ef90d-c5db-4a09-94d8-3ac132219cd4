import { DashboardStats } from '@/types/safety';
import { useQuery } from '@tanstack/react-query';

interface SafetyStatsProps {
  userId: string;
}

export default function SafetyStats({ userId }: SafetyStatsProps) {
  const { data: stats, isLoading } = useQuery<DashboardStats>({
    queryKey: ['/api/dashboard', userId],
    enabled: !!userId,
  });

  if (isLoading) {
    return (
      <section className="px-4 py-6 bg-gradient-to-r from-safety-blue to-blue-600 text-white">
        <h2 className="text-lg font-semibold mb-4">Today's Safety Overview</h2>
        <div className="grid grid-cols-3 gap-4">
          {[1, 2, 3].map(i => (
            <div key={i} className="text-center">
              <div className="text-2xl font-bold animate-pulse bg-white/20 rounded h-8 mb-1"></div>
              <div className="text-xs opacity-90 animate-pulse bg-white/20 rounded h-4"></div>
            </div>
          ))}
        </div>
      </section>
    );
  }

  if (!stats) {
    return (
      <section className="px-4 py-6 bg-gradient-to-r from-safety-blue to-blue-600 text-white">
        <h2 className="text-lg font-semibold mb-4">Today's Safety Overview</h2>
        <div className="text-center text-white/80">Unable to load safety statistics</div>
      </section>
    );
  }

  return (
    <section className="px-4 py-6 bg-gradient-to-r from-safety-blue to-blue-600 text-white">
      <h2 className="text-lg font-semibold mb-4">Today's Safety Overview</h2>
      <div className="grid grid-cols-3 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold">{stats.todayInspections}</div>
          <div className="text-xs opacity-90">Inspections</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-safety-orange">{stats.todayAlerts}</div>
          <div className="text-xs opacity-90">Alerts</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-safety-green">{stats.complianceScore}%</div>
          <div className="text-xs opacity-90">Compliance</div>
        </div>
      </div>
    </section>
  );
}
