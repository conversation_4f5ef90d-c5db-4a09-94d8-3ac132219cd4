import { VoiceClassification } from '@/types/safety';

export class VoiceRecognitionService {
  static async processVoiceInput(transcript: string, audioData?: string): Promise<VoiceClassification> {
    try {
      const response = await fetch('/api/ai/process-voice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transcript,
          audioData
        })
      });

      if (!response.ok) {
        throw new Error('Failed to process voice input');
      }

      return await response.json();
    } catch (error) {
      console.error('Voice processing error:', error);
      throw error;
    }
  }

  static extractHazardKeywords(transcript: string): string[] {
    const hazardKeywords = [
      'spill', 'leak', 'fall', 'trip', 'slip', 'fire', 'smoke', 'chemical',
      'broken', 'damaged', 'unsafe', 'blocked', 'obstruction', 'violation',
      'accident', 'injury', 'emergency', 'danger', 'hazard', 'risk'
    ];

    return hazardKeywords.filter(keyword => 
      transcript.toLowerCase().includes(keyword)
    );
  }

  static determineUrgency(transcript: string): 'LOW' | 'MEDIUM' | 'HIGH' {
    const urgentWords = ['emergency', 'urgent', 'immediate', 'critical', 'fire', 'injury'];
    const mediumWords = ['leak', 'spill', 'broken', 'unsafe'];
    
    const lowerTranscript = transcript.toLowerCase();
    
    if (urgentWords.some(word => lowerTranscript.includes(word))) {
      return 'HIGH';
    }
    
    if (mediumWords.some(word => lowerTranscript.includes(word))) {
      return 'MEDIUM';
    }
    
    return 'LOW';
  }

  static generateStructuredReport(transcript: string): {
    type: string;
    urgency: string;
    actionItems: string[];
  } {
    const urgency = this.determineUrgency(transcript);
    const keywords = this.extractHazardKeywords(transcript);
    
    let type = 'General Safety Concern';
    let actionItems: string[] = [];

    // Determine incident type based on keywords
    if (keywords.includes('spill') || keywords.includes('leak')) {
      type = 'Chemical/Liquid Spill';
      actionItems = [
        'Isolate affected area',
        'Deploy spill response team',
        'Notify environmental safety officer'
      ];
    } else if (keywords.includes('fire') || keywords.includes('smoke')) {
      type = 'Fire/Smoke Incident';
      actionItems = [
        'Evacuate immediate area',
        'Contact fire department',
        'Activate fire suppression systems'
      ];
    } else if (keywords.includes('fall') || keywords.includes('injury')) {
      type = 'Personnel Injury';
      actionItems = [
        'Provide immediate medical assistance',
        'Secure the area',
        'Notify emergency medical services'
      ];
    } else if (keywords.includes('blocked') || keywords.includes('obstruction')) {
      type = 'Access/Egress Obstruction';
      actionItems = [
        'Remove obstruction if safe',
        'Mark alternative routes',
        'Notify facilities management'
      ];
    }

    return {
      type,
      urgency,
      actionItems
    };
  }
}
