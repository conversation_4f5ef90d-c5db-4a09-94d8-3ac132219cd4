# 🚀 How to Run the AI Safety Assistant Project

## ⚡ Quick Start (3 Minutes)

### Step 1: Prerequisites Check
Make sure you have:
- ✅ **Node.js 18+** ([Download here](https://nodejs.org/))
- ✅ **npm** (comes with Node.js)
- ✅ **Modern browser** (Chrome, Firefox, Safari, Edge)

### Step 2: Verify Your Setup
```bash
# Check if you have the right versions
node --version    # Should be 18.0.0 or higher
npm --version     # Should be 8.0.0 or higher

# Run our verification script
node verify-setup.js
```

### Step 3: Run the Project

**🪟 Windows Users:**
```bash
# Automated setup and start
setup.bat
start.bat
```

**🍎 macOS/Linux Users:**
```bash
# Make scripts executable
chmod +x setup.sh start.sh

# Automated setup and start
./setup.sh
./start.sh
```

**🔧 Manual Method (All Platforms):**
```bash
# 1. Install root dependencies
npm install

# 2. Install client dependencies
cd client
npm install

# 3. Start development server
npm run dev
```

### Step 4: Open the Application
🌐 **Open your browser to: http://localhost:5173**

---

## 🎯 What You'll See

### Main Features Available:
1. **🤖 AI Command Center** - Neural network dashboard
2. **📱 Live Detection** - Real-time safety monitoring
3. **📸 Photo Check** - Instant image analysis
4. **🎤 Voice Report** - AI-powered voice reporting
5. **📊 Analytics** - Safety insights and trends

### Demo Features:
- ✨ **Mock AI Detection** - Simulated safety violations
- 🎨 **Modern UI** - Glassmorphism design with animations
- 📱 **Mobile Responsive** - Works on all devices
- 🔄 **Real-time Updates** - Live metrics and notifications

---

## 🔧 Troubleshooting

### Common Issues:

#### ❌ "npm command not found"
**Solution:** Install Node.js from [nodejs.org](https://nodejs.org/)

#### ❌ "Port 5173 is already in use"
**Solution:** 
```bash
# Kill the process using the port
npx kill-port 5173
# Or use a different port
npm run dev -- --port 3000
```

#### ❌ "Module not found" errors
**Solution:**
```bash
# Clear and reinstall dependencies
rm -rf node_modules package-lock.json
npm install
cd client
rm -rf node_modules package-lock.json
npm install
```

#### ❌ Camera not working
**Solution:**
- Allow camera permissions in browser
- Use Chrome/Firefox for best compatibility
- Ensure you're on localhost or HTTPS

### 🆘 Need More Help?
- 📖 Check **TROUBLESHOOTING.md** for detailed solutions
- 🔍 Run `node verify-setup.js` to diagnose issues
- 💬 Check browser console (F12) for error messages

---

## 📁 Project Structure

```
SafeGuardAI/
├── 🚀 start.sh/start.bat          # Quick start scripts
├── ⚙️ setup.sh/setup.bat          # Setup scripts
├── 🔍 verify-setup.js             # Setup verification
├── 📖 README.md                   # Full documentation
├── 🔧 TROUBLESHOOTING.md          # Problem solutions
├── 
├── client/                        # Frontend React app
│   ├── 📦 package.json           # Client dependencies
│   ├── ⚙️ vite.config.ts         # Build configuration
│   ├── 🎨 tailwind.config.js     # Styling configuration
│   └── src/
│       ├── 🏠 App.tsx            # Main application
│       ├── 🤖 components/        # AI components
│       ├── 📱 pages/             # Application pages
│       └── 🎨 index.css          # Global styles
│
├── ai_backend/                    # Python AI backend (optional)
│   ├── 📋 requirements.txt       # Python dependencies
│   ├── 🧠 ai_models/            # AI detection models
│   └── 🔧 utils/                # Utility functions
│
└── 📦 package.json               # Root dependencies
```

---

## 🎮 Using the Application

### 1. AI Command Center (`/ai-dashboard`)
- View neural network activity
- Monitor AI model performance
- See real-time safety insights
- Access predictive analytics

### 2. Live Detection (`/ai-live`)
- Start camera for real-time analysis
- See AI detection overlays
- Monitor multiple AI modules
- Get instant safety alerts

### 3. Photo Check (`/photo-check`)
- Upload or capture safety photos
- Get instant AI analysis
- View detailed safety reports
- Export results

### 4. Voice Report (`/voice-report`)
- Record voice safety reports
- AI transcription and analysis
- Automatic incident classification
- Generate structured reports

---

## 🚀 Performance Tips

### For Best Experience:
- 🖥️ Use a modern browser (Chrome recommended)
- 📱 Test on actual mobile devices
- 🔌 Ensure stable internet connection
- 💾 Close unnecessary browser tabs
- 🎥 Allow camera/microphone permissions

### Development Mode Features:
- 🔄 Hot reload for instant updates
- 🐛 Source maps for debugging
- 📊 Performance monitoring
- 🎨 Live CSS updates

---

## 🎉 Success Indicators

You'll know everything is working when you see:
- ✅ Development server starts without errors
- ✅ Browser opens to http://localhost:5173
- ✅ AI Dashboard loads with animated components
- ✅ Camera access works (when permitted)
- ✅ No console errors in browser dev tools

---

## 📞 Support

If you encounter any issues:

1. **First:** Run `node verify-setup.js`
2. **Second:** Check **TROUBLESHOOTING.md**
3. **Third:** Look at browser console (F12 → Console)
4. **Finally:** Create an issue with error details

---

**🎯 Goal: Get you running the AI Safety Assistant in under 5 minutes!**

**Happy coding! 🚀**
