import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowR<PERSON>, BarChart3, Brain, Camera, Mic, Play, Shield, Star, Zap } from 'lucide-react';
import { useState } from 'react';
import { <PERSON> } from 'wouter';

export default function LandingPage() {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  const features = [
    {
      icon: Brain,
      title: 'AI-Powered Detection',
      description:
        'Advanced neural networks detect safety violations in real-time with 94.2% accuracy',
    },
    {
      icon: Camera,
      title: 'Computer Vision',
      description:
        'Instant PPE compliance checking and hazard identification through smart cameras',
    },
    {
      icon: Mic,
      title: 'Voice Recognition',
      description: 'Hands-free incident reporting with natural language processing',
    },
    {
      icon: BarChart3,
      title: 'Predictive Analytics',
      description: 'Forecast safety risks and prevent incidents before they happen',
    },
    {
      icon: Shield,
      title: 'Real-time Monitoring',
      description: '24/7 workplace surveillance with instant alerts and notifications',
    },
    {
      icon: Zap,
      title: 'Instant Response',
      description: 'Automated emergency protocols with sub-second response times',
    },
  ];

  const stats = [
    { number: '94.2%', label: 'Detection Accuracy' },
    { number: '500+', label: 'Companies Trust Us' },
    { number: '85%', label: 'Incident Reduction' },
    { number: '24/7', label: 'Monitoring' },
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      role: 'Safety Director, TechCorp',
      content:
        'SafeGuard AI reduced our workplace incidents by 78% in just 6 months. The AI detection is incredibly accurate.',
      rating: 5,
    },
    {
      name: 'Michael Chen',
      role: 'Operations Manager, BuildSafe',
      content:
        'The real-time monitoring and predictive analytics have transformed our safety protocols completely.',
      rating: 5,
    },
    {
      name: 'Emily Rodriguez',
      role: 'HSE Manager, IndustrialPlus',
      content:
        "Best investment we've made. The voice reporting feature saves us hours of paperwork daily.",
      rating: 5,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <Shield className="h-8 w-8 text-blue-400" />
              <span className="text-xl font-bold text-white">SafeGuard AI</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <Link href="/features" className="text-white/80 hover:text-white transition-colors">
                Features
              </Link>
              <Link href="/pricing" className="text-white/80 hover:text-white transition-colors">
                Pricing
              </Link>
              <Link href="/about" className="text-white/80 hover:text-white transition-colors">
                About
              </Link>
              <Link href="/blog" className="text-white/80 hover:text-white transition-colors">
                Blog
              </Link>
              <Link href="/contact" className="text-white/80 hover:text-white transition-colors">
                Contact
              </Link>
              <Link href="/dashboard">
                <Button className="bg-blue-600 hover:bg-blue-700">Launch App</Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
            The Future of
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              {' '}
              Workplace Safety
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-white/80 mb-8 max-w-3xl mx-auto">
            AI-powered safety assistant that prevents workplace accidents before they happen.
            Real-time monitoring, predictive analytics, and instant response.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/dashboard">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-lg px-8 py-4">
                Start Free Trial
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Button
              size="lg"
              variant="outline"
              className="border-white/20 text-white hover:bg-white/10 text-lg px-8 py-4"
              onClick={() => setIsVideoPlaying(true)}
            >
              <Play className="mr-2 h-5 w-5" />
              Watch Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-blue-400 mb-2">
                  {stat.number}
                </div>
                <div className="text-white/80">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Powered by Advanced AI
            </h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Our cutting-edge artificial intelligence combines computer vision, natural language
              processing, and predictive analytics to create the most comprehensive safety solution.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card
                key={index}
                className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300"
              >
                <CardContent className="p-6">
                  <feature.icon className="h-12 w-12 text-blue-400 mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
                  <p className="text-white/70">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Demo Section */}
      <section id="demo" className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">See It In Action</h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Experience the power of AI-driven safety monitoring with our interactive demo
            </p>
          </div>
          <div className="relative max-w-4xl mx-auto">
            <div className="aspect-video bg-gradient-to-br from-blue-900/50 to-purple-900/50 rounded-2xl border border-white/10 flex items-center justify-center">
              {!isVideoPlaying ? (
                <Button
                  size="lg"
                  className="bg-blue-600 hover:bg-blue-700"
                  onClick={() => setIsVideoPlaying(true)}
                >
                  <Play className="mr-2 h-6 w-6" />
                  Play Demo Video
                </Button>
              ) : (
                <div className="text-white text-center">
                  <div className="animate-pulse">
                    <Brain className="h-16 w-16 mx-auto mb-4 text-blue-400" />
                    <p>AI Demo Loading...</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Trusted by Industry Leaders
            </h2>
            <p className="text-xl text-white/80">
              Join hundreds of companies already using SafeGuard AI
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10">
                <CardContent className="p-6">
                  <div className="flex mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-white/80 mb-4">"{testimonial.content}"</p>
                  <div>
                    <div className="font-semibold text-white">{testimonial.name}</div>
                    <div className="text-white/60">{testimonial.role}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to Transform Your Workplace Safety?
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Join the AI safety revolution. Start your free trial today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/dashboard">
              <Button
                size="lg"
                className="bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-4"
              >
                Start Free Trial
              </Button>
            </Link>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white/10 text-lg px-8 py-4"
            >
              Contact Sales
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black/40 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Shield className="h-8 w-8 text-blue-400" />
                <span className="text-xl font-bold text-white">SafeGuard AI</span>
              </div>
              <p className="text-white/60">
                The future of workplace safety powered by artificial intelligence.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Product</h3>
              <ul className="space-y-2 text-white/60">
                <li>
                  <Link href="/features" className="hover:text-white">
                    Features
                  </Link>
                </li>
                <li>
                  <Link href="/dashboard" className="hover:text-white">
                    Demo
                  </Link>
                </li>
                <li>
                  <Link href="/pricing" className="hover:text-white">
                    Pricing
                  </Link>
                </li>
                <li>
                  <Link href="/dashboard" className="hover:text-white">
                    Dashboard
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Company</h3>
              <ul className="space-y-2 text-white/60">
                <li>
                  <Link href="/about" className="hover:text-white">
                    About
                  </Link>
                </li>
                <li>
                  <Link href="/careers" className="hover:text-white">
                    Careers
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="hover:text-white">
                    Contact
                  </Link>
                </li>
                <li>
                  <Link href="/blog" className="hover:text-white">
                    Blog
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Support</h3>
              <ul className="space-y-2 text-white/60">
                <li>
                  <a href="#help" className="hover:text-white">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#docs" className="hover:text-white">
                    Documentation
                  </a>
                </li>
                <li>
                  <a href="#api" className="hover:text-white">
                    API
                  </a>
                </li>
                <li>
                  <a href="#status" className="hover:text-white">
                    Status
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-white/10 mt-8 pt-8 text-center text-white/60">
            <p>&copy; 2024 SafeGuard AI. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
