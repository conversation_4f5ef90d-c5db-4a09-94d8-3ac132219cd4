# Troubleshooting Guide

## 🔧 Common Issues and Solutions

### 1. Node.js and npm Issues

#### Error: "npm command not found"
**Solution:**
- Install Node.js from [nodejs.org](https://nodejs.org/)
- Restart your terminal/command prompt
- Verify installation: `node --version` and `npm --version`

#### Error: "EACCES: permission denied"
**Solution (macOS/Linux):**
```bash
sudo chown -R $(whoami) ~/.npm
```

**Solution (Windows):**
- Run command prompt as Administrator
- Or use: `npm config set prefix %APPDATA%\npm`

### 2. TypeScript Errors

#### Error: "Cannot find type definition file for 'node'"
**Solution:**
```bash
cd client
npm install @types/node --save-dev
```

#### Error: "Module not found" or path resolution issues
**Solution:**
- Check if `client/tsconfig.json` exists
- Verify path mappings in tsconfig.json
- Restart your IDE/editor

### 3. Tailwind CSS Issues

#### Error: "Unknown at rule @tailwind"
**Solution:**
- Install Tailwind CSS IntelliSense extension in VS Code
- Ensure `tailwind.config.js` and `postcss.config.js` exist in client folder

#### Styles not applying
**Solution:**
```bash
cd client
npm run build
# Then restart dev server
npm run dev
```

### 4. Camera/Media Issues

#### Error: "Camera access denied"
**Solution:**
- Allow camera permissions in browser
- Use HTTPS in production (required for camera access)
- Check if camera is being used by another application

#### Error: "getUserMedia is not a function"
**Solution:**
- Ensure you're using HTTPS or localhost
- Update your browser to latest version
- Check browser compatibility

### 5. Development Server Issues

#### Error: "Port 5173 is already in use"
**Solution:**
```bash
# Kill process using the port
npx kill-port 5173

# Or use a different port
npm run dev -- --port 3000
```

#### Error: "Module build failed"
**Solution:**
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Or for client specifically
cd client
rm -rf node_modules package-lock.json
npm install
```

### 6. AI Backend Issues (Optional)

#### Error: Python dependencies installation failed
**Solution:**
```bash
# Create virtual environment
python -m venv ai_backend/venv

# Activate virtual environment
# Windows:
ai_backend\venv\Scripts\activate
# macOS/Linux:
source ai_backend/venv/bin/activate

# Install dependencies
pip install --upgrade pip
pip install -r ai_backend/requirements.txt
```

#### Error: CUDA/GPU issues
**Solution:**
- Install CPU-only versions:
```bash
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
```

### 7. Browser Compatibility

#### Supported Browsers:
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

#### Features requiring modern browsers:
- Camera access (getUserMedia)
- WebRTC for video recording
- CSS Grid and Flexbox
- ES6+ JavaScript features

### 8. Performance Issues

#### Slow loading or high memory usage
**Solution:**
- Close other browser tabs
- Disable browser extensions
- Use Chrome DevTools to identify memory leaks
- Restart the development server

#### AI detection is slow
**Solution:**
- The demo uses mock AI responses (1-2 second delays)
- In production, optimize model inference
- Use GPU acceleration when available

### 9. Mobile Device Issues

#### Touch interactions not working
**Solution:**
- Ensure you're using touch events, not just mouse events
- Test on actual mobile devices, not just browser dev tools
- Check viewport meta tag in index.html

#### Camera not working on mobile
**Solution:**
- Use HTTPS (required on mobile)
- Test camera permissions
- Some mobile browsers have restrictions

### 10. Build and Deployment Issues

#### Error: "Build failed"
**Solution:**
```bash
# Check for TypeScript errors
npm run type-check

# Fix any linting issues
npm run lint

# Clear cache and rebuild
rm -rf dist
npm run build
```

#### Error: "Assets not loading in production"
**Solution:**
- Check base URL in vite.config.ts
- Ensure all assets are in public folder
- Verify server configuration for SPA routing

## 🆘 Getting Help

### Debug Information to Collect:

1. **System Information:**
   - Operating System and version
   - Node.js version (`node --version`)
   - npm version (`npm --version`)
   - Browser and version

2. **Error Details:**
   - Full error message
   - Console logs (F12 → Console)
   - Network tab errors (F12 → Network)
   - Steps to reproduce

3. **Project State:**
   - Git commit hash
   - Modified files
   - Environment variables

### Useful Commands for Debugging:

```bash
# Check Node.js and npm versions
node --version
npm --version

# Check project dependencies
npm list

# Clear npm cache
npm cache clean --force

# Check for outdated packages
npm outdated

# Verbose logging
npm run dev --verbose

# Check TypeScript compilation
npx tsc --noEmit

# Check for port conflicts
netstat -tulpn | grep :5173
```

### Reset Everything (Nuclear Option):

```bash
# Remove all dependencies and caches
rm -rf node_modules package-lock.json
cd client
rm -rf node_modules package-lock.json
cd ..

# Clear npm cache
npm cache clean --force

# Reinstall everything
npm install
cd client
npm install
cd ..

# Start fresh
npm run dev
```

## 📞 Support Channels

- **GitHub Issues**: Report bugs and feature requests
- **Documentation**: Check README.md for detailed setup
- **Community**: Join our Discord/Slack for real-time help

---

**Most issues can be resolved by ensuring you have the latest Node.js version and following the setup steps exactly as described in the README.md file.**
