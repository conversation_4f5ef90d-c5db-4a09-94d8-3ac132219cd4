import AIInsightsPanel from '@/components/ai-insights-panel';
import NeuralNetworkVisualization from '@/components/neural-network-viz';
import RealTimeMetrics from '@/components/real-time-metrics';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { useQuery } from '@tanstack/react-query';
import {
  Activity,
  AlertTriangle,
  Brain,
  CheckCircle,
  Clock,
  Eye,
  Shield,
  TrendingUp,
  Users,
  Zap,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Link } from 'wouter';

const MOCK_USER_ID = 'mock-user-123';

interface AIModule {
  id: string;
  name: string;
  icon: string;
  status: 'active' | 'processing' | 'idle';
  accuracy: number;
  lastUpdate: string;
  color: string;
  route: string;
}

export default function AIDashboard() {
  const [selectedModule] = useState<string | null>(null);
  const [aiMetrics, setAIMetrics] = useState({
    totalDetections: 0,
    accuracy: 0,
    processingSpeed: 0,
    activeModels: 0,
  });

  const { toast } = useToast();

  const { data: incidents = [] } = useQuery({
    queryKey: ['/api/incidents', MOCK_USER_ID, 5],
  });

  const aiModules: AIModule[] = [
    {
      id: 'ppe-detection',
      name: 'PPE Detection',
      icon: 'shield',
      status: 'active',
      accuracy: 94.2,
      lastUpdate: '2 min ago',
      color: 'safety-green',
      route: '/ppe-detection',
    },
    {
      id: 'hazard-detection',
      name: 'Hazard Detection',
      icon: 'alert-triangle',
      status: 'active',
      accuracy: 91.8,
      lastUpdate: '1 min ago',
      color: 'safety-orange',
      route: '/hazard-detection',
    },
    {
      id: 'fall-detection',
      name: 'Fall Detection',
      icon: 'activity',
      status: 'processing',
      accuracy: 96.5,
      lastUpdate: '30 sec ago',
      color: 'safety-red',
      route: '/fall-detection',
    },
    {
      id: 'zone-monitoring',
      name: 'Zone Monitoring',
      icon: 'eye',
      status: 'active',
      accuracy: 89.3,
      lastUpdate: '45 sec ago',
      color: 'safety-blue',
      route: '/zone-monitoring',
    },
    {
      id: 'voice-processing',
      name: 'Voice AI',
      icon: 'brain',
      status: 'idle',
      accuracy: 87.6,
      lastUpdate: '5 min ago',
      color: 'safety-purple',
      route: '/voice-report',
    },
    {
      id: 'vehicle-proximity',
      name: 'Vehicle Safety',
      icon: 'zap',
      status: 'active',
      accuracy: 92.1,
      lastUpdate: '1 min ago',
      color: 'safety-cyan',
      route: '/vehicle-safety',
    },
  ];

  useEffect(() => {
    // Simulate real-time AI metrics updates
    const interval = setInterval(() => {
      setAIMetrics(prev => ({
        totalDetections: prev.totalDetections + Math.floor(Math.random() * 3),
        accuracy: 90 + Math.random() * 8,
        processingSpeed: 150 + Math.random() * 50,
        activeModels: aiModules.filter(m => m.status === 'active').length,
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const getModuleIcon = (iconName: string) => {
    const icons = {
      shield: Shield,
      'alert-triangle': AlertTriangle,
      activity: Activity,
      eye: Eye,
      brain: Brain,
      zap: Zap,
    };
    return icons[iconName as keyof typeof icons] || Shield;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-safety-green';
      case 'processing':
        return 'bg-ai-primary animate-pulse-ai';
      case 'idle':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  const handleEmergencyAlert = () => {
    toast({
      title: '🚨 Emergency Alert Activated',
      description: 'AI systems are coordinating emergency response protocols.',
      variant: 'destructive',
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50">
      {/* AI Header */}
      <div className="glass-card mx-4 mt-4 p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-ai rounded-full flex items-center justify-center animate-glow">
              <Brain className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold bg-gradient-ai bg-clip-text text-transparent">
                AI Safety Command Center
              </h1>
              <p className="text-sm text-gray-600">
                Neural networks actively monitoring • {aiMetrics.activeModels} models online
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-safety-green rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-safety-green">AI ACTIVE</span>
          </div>
        </div>
      </div>

      {/* Real-time AI Metrics */}
      <div className="px-4 mb-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="glass-card border-0">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-ai-primary" />
                <div>
                  <p className="text-xs text-gray-600">Detections Today</p>
                  <p className="text-lg font-bold text-ai-primary">{aiMetrics.totalDetections}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-0">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-safety-green" />
                <div>
                  <p className="text-xs text-gray-600">AI Accuracy</p>
                  <p className="text-lg font-bold text-safety-green">
                    {aiMetrics.accuracy.toFixed(1)}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-0">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="w-5 h-5 text-ai-secondary" />
                <div>
                  <p className="text-xs text-gray-600">Processing Speed</p>
                  <p className="text-lg font-bold text-ai-secondary">
                    {aiMetrics.processingSpeed.toFixed(0)}ms
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-0">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Users className="w-5 h-5 text-safety-cyan" />
                <div>
                  <p className="text-xs text-gray-600">Active Models</p>
                  <p className="text-lg font-bold text-safety-cyan">{aiMetrics.activeModels}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Neural Network Visualization */}
      <div className="px-4 mb-6">
        <Card className="glass-card border-0">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Brain className="w-5 h-5 mr-2 text-ai-neural" />
              Neural Network Activity
            </h3>
            <NeuralNetworkVisualization modules={aiModules} />
          </CardContent>
        </Card>
      </div>

      {/* AI Modules Grid */}
      <div className="px-4 mb-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-900">AI Detection Modules</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {aiModules.map(module => {
            const IconComponent = getModuleIcon(module.icon);

            return (
              <Link key={module.id} href={module.route}>
                <Card className="glass-card border-0 hover:scale-105 transition-all duration-300 cursor-pointer">
                  <CardContent className="p-4">
                    <div className="flex flex-col items-center text-center space-y-3">
                      <div
                        className={`w-12 h-12 ${getStatusColor(module.status)} rounded-full flex items-center justify-center relative`}
                      >
                        <IconComponent className="w-6 h-6 text-white" />
                        {module.status === 'processing' && (
                          <div className="absolute inset-0 rounded-full border-2 border-white border-t-transparent animate-spin"></div>
                        )}
                      </div>

                      <div>
                        <h4 className="font-semibold text-sm">{module.name}</h4>
                        <p className="text-xs text-gray-600">{module.accuracy}% accuracy</p>
                        <p className="text-xs text-gray-500">{module.lastUpdate}</p>
                      </div>

                      <div className={`w-full h-1 bg-gray-200 rounded-full overflow-hidden`}>
                        <div
                          className={`h-full bg-${module.color} transition-all duration-1000 ${
                            module.accuracy >= 90
                              ? 'w-full'
                              : module.accuracy >= 80
                                ? 'w-5/6'
                                : module.accuracy >= 70
                                  ? 'w-4/5'
                                  : module.accuracy >= 60
                                    ? 'w-3/5'
                                    : module.accuracy >= 50
                                      ? 'w-1/2'
                                      : module.accuracy >= 40
                                        ? 'w-2/5'
                                        : module.accuracy >= 30
                                          ? 'w-1/3'
                                          : module.accuracy >= 20
                                            ? 'w-1/5'
                                            : module.accuracy >= 10
                                              ? 'w-1/6'
                                              : 'w-1/12'
                          }`}
                        ></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>
      </div>

      {/* AI Insights Panel */}
      <div className="px-4 mb-6">
        <AIInsightsPanel incidents={incidents as any[]} />
      </div>

      {/* Real-time Metrics */}
      <div className="px-4 mb-6">
        <RealTimeMetrics />
      </div>

      {/* Emergency Actions */}
      <div className="px-4 mb-20">
        <Card className="glass-card border-0 bg-gradient-to-r from-red-500/10 to-orange-500/10">
          <CardContent className="p-6">
            <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2 text-safety-red" />
              Emergency Response
            </h4>
            <div className="grid grid-cols-2 gap-3">
              <Button
                onClick={handleEmergencyAlert}
                className="bg-safety-red hover:bg-red-600 text-white py-3 px-4 rounded-lg font-medium animate-glow"
              >
                <AlertTriangle className="w-5 h-5 mr-2" />
                Emergency Alert
              </Button>
              <Link href="/live">
                <Button className="w-full bg-gradient-ai text-white py-3 px-4 rounded-lg font-medium">
                  <Eye className="w-5 h-5 mr-2" />
                  Live Detection
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
