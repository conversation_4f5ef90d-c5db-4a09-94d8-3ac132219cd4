import { ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { X, Bell, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileLayoutProps {
  children: ReactNode;
  title: string;
  subtitle?: string;
  showStatusBar?: boolean;
  showEmergencyButton?: boolean;
  showLiveIndicator?: boolean;
  alertActive?: boolean;
  alertMessage?: string;
  onCloseAlert?: () => void;
  onEmergencyAlert?: () => void;
  className?: string;
}

export default function MobileLayout({
  children,
  title,
  subtitle,
  showStatusBar = true,
  showEmergencyButton = true,
  showLiveIndicator = false,
  alertActive = false,
  alertMessage,
  onCloseAlert,
  onEmergencyAlert,
  className
}: MobileLayoutProps) {
  const currentTime = new Date().toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });

  return (
    <div className={cn("max-w-md mx-auto bg-white shadow-2xl min-h-screen relative overflow-hidden", className)}>
      {/* Status Bar */}
      {showStatusBar && (
        <div className="bg-safety-blue text-white px-4 py-2 flex justify-between items-center text-sm">
          <div className="flex items-center space-x-2">
            <div className="flex space-x-1">
              <div className="w-1 h-3 bg-white rounded-full"></div>
              <div className="w-1 h-3 bg-white rounded-full"></div>
              <div className="w-1 h-3 bg-white rounded-full"></div>
              <div className="w-1 h-3 bg-white/60 rounded-full"></div>
            </div>
            <span className="font-medium">SafetyNet 5G</span>
          </div>
          <div className="flex items-center space-x-2">
            <span>{currentTime}</span>
            <div className="flex space-x-1">
              <div className="w-6 h-3 border border-white rounded-sm">
                <div className="w-4 h-full bg-white rounded-sm"></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <header className="bg-white shadow-md px-4 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-safety-blue rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L13.09 8.26L22 9L13.09 15.74L12 22L10.91 15.74L2 9L10.91 8.26L12 2Z" />
              </svg>
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900">{title}</h1>
              {subtitle && <p className="text-xs text-gray-600">{subtitle}</p>}
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {showLiveIndicator && (
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-safety-green rounded-full animate-pulse-green"></div>
                <span className="text-xs text-safety-green font-medium">LIVE</span>
              </div>
            )}
            <Button variant="ghost" size="sm" className="p-2 rounded-lg hover:bg-gray-100">
              <Bell className="w-5 h-5 text-gray-600" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-safety-red rounded-full"></span>
            </Button>
          </div>
        </div>
      </header>

      {/* Alert Banner */}
      {alertActive && (
        <div className="bg-safety-red text-white px-4 py-3 flex items-center space-x-3">
          <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
            </svg>
          </div>
          <div className="flex-1">
            <p className="font-semibold">HAZARD DETECTED</p>
            <p className="text-sm opacity-90">{alertMessage || "Safety violation detected"}</p>
          </div>
          {onCloseAlert && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onCloseAlert}
              className="text-white hover:bg-red-600 p-1 rounded"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      )}

      {/* Main Content */}
      <main className="pb-20 mobile-safe-area">
        {children}
      </main>

      {/* Floating Emergency Button */}
      {showEmergencyButton && (
        <Button 
          onClick={onEmergencyAlert}
          className="fixed bottom-24 right-6 w-14 h-14 bg-safety-red text-white rounded-full shadow-lg flex items-center justify-center z-40 animate-pulse hover:bg-red-600"
        >
          <Zap className="w-6 h-6" />
        </Button>
      )}
    </div>
  );
}
