import MobileLayout from '@/components/mobile-layout';
import Navigation from '@/components/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AlertTriangle, Calendar, CheckCircle, Eye, MapPin, Search } from 'lucide-react';
import { useState } from 'react';

const MOCK_USER_ID = 'mock-user-123';

export default function Incidents() {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [severityFilter, setSeverityFilter] = useState('all');
  const [selectedIncident, setSelectedIncident] = useState<any>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: incidents = [], isLoading } = useQuery({
    queryKey: ['/api/incidents', MOCK_USER_ID, 50],
  });

  const updateStatusMutation = useMutation({
    mutationFn: async ({ id, status }: { id: string; status: string }) => {
      return await apiRequest('PATCH', `/api/incidents/${id}/status`, { status });
    },
    onSuccess: () => {
      toast({
        title: 'Status Updated',
        description: 'Incident status has been updated successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/incidents'] });
      queryClient.invalidateQueries({ queryKey: ['/api/dashboard'] });
      setIsDetailModalOpen(false);
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to update incident status.',
        variant: 'destructive',
      });
    },
  });

  const filteredIncidents = (incidents as any[]).filter((incident: any) => {
    const matchesSearch =
      incident.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      incident.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (incident.location && incident.location.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesStatus = statusFilter === 'all' || incident.status === statusFilter;
    const matchesSeverity =
      severityFilter === 'all' || incident.severity.toLowerCase() === severityFilter;

    return matchesSearch && matchesStatus && matchesSeverity;
  });

  const getSeverityColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'high':
        return 'border-safety-red bg-red-50';
      case 'medium':
        return 'border-safety-orange bg-orange-50';
      case 'low':
        return 'border-safety-green bg-green-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getSeverityBadgeColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'high':
        return 'bg-safety-red text-white hover:bg-red-600';
      case 'medium':
        return 'bg-safety-orange text-white hover:bg-orange-600';
      case 'low':
        return 'bg-safety-green text-white hover:bg-green-600';
      default:
        return 'bg-gray-500 text-white hover:bg-gray-600';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'open':
        return 'bg-safety-red text-white hover:bg-red-600';
      case 'investigating':
        return 'bg-safety-orange text-white hover:bg-orange-600';
      case 'resolved':
        return 'bg-safety-green text-white hover:bg-green-600';
      default:
        return 'bg-gray-500 text-white hover:bg-gray-600';
    }
  };

  const getTypeIcon = (type: string) => {
    if (type.includes('PPE')) return '⛑️';
    if (type.includes('FALL')) return '🚨';
    if (type.includes('SPILL')) return '💧';
    if (type.includes('FIRE')) return '🔥';
    if (type.includes('TOOL')) return '🔧';
    return '⚠️';
  };

  const handleViewDetails = (incident: any) => {
    setSelectedIncident(incident);
    setIsDetailModalOpen(true);
  };

  const handleStatusChange = (status: string) => {
    if (selectedIncident) {
      updateStatusMutation.mutate({ id: selectedIncident.id, status });
    }
  };

  return (
    <>
      <MobileLayout title="Safety Incidents" subtitle="Incident management and tracking">
        <div className="px-4 py-6">
          {/* Search and Filters */}
          <div className="space-y-4 mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                placeholder="Search incidents..."
                className="pl-10"
              />
            </div>

            <div className="grid grid-cols-2 gap-3">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="investigating">Investigating</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                </SelectContent>
              </Select>

              <Select value={severityFilter} onValueChange={setSeverityFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Severity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Severity</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Incidents List */}
          {isLoading ? (
            <div className="space-y-4">
              {[1, 2, 3].map(i => (
                <Card key={i}>
                  <CardContent className="p-4">
                    <div className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (filteredIncidents as any[]).length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <AlertTriangle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Incidents Found</h3>
                <p className="text-gray-600">
                  {searchQuery || statusFilter !== 'all' || severityFilter !== 'all'
                    ? 'No incidents match your current filters.'
                    : 'No safety incidents have been reported yet.'}
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {(filteredIncidents as any[]).map((incident: any) => (
                <Card
                  key={incident.id}
                  className={`border-l-4 ${getSeverityColor(incident.severity)}`}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-xl">{getTypeIcon(incident.type)}</span>
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 text-sm leading-tight">
                            {incident.description}
                          </h3>
                          <p className="text-xs text-gray-600 mt-1">
                            {incident.type.replace(/_/g, ' ')}
                          </p>
                        </div>
                      </div>
                      <Button
                        onClick={() => handleViewDetails(incident)}
                        variant="ghost"
                        size="sm"
                        className="p-1"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    </div>

                    <div className="flex items-center justify-between text-xs text-gray-600 mb-3">
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-3 h-3" />
                        <span>{incident.location || 'Unknown Location'}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3" />
                        <span>
                          {new Date(incident.createdAt).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                          })}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex space-x-2">
                        <Badge className={getSeverityBadgeColor(incident.severity)}>
                          {incident.severity?.toUpperCase()}
                        </Badge>
                        <Badge className={getStatusBadgeColor(incident.status)}>
                          {incident.status?.toUpperCase()}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </MobileLayout>

      <Navigation />

      {/* Incident Detail Modal */}
      <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
        <DialogContent className="w-full max-w-md mx-auto bottom-0 fixed translate-y-0 translate-x-[-50%] left-1/2 rounded-t-3xl rounded-b-none border-0 p-0 max-h-[90vh] overflow-y-auto">
          {selectedIncident && (
            <div className="p-6">
              <div className="w-8 h-1 bg-gray-300 rounded-full mx-auto mb-6"></div>

              <DialogHeader>
                <DialogTitle className="text-xl font-bold text-gray-900 mb-4">
                  Incident Details
                </DialogTitle>
              </DialogHeader>

              {/* Incident Image */}
              {selectedIncident.imageUrl && (
                <div className="mb-4">
                  <img
                    src={selectedIncident.imageUrl}
                    alt="Incident"
                    className="w-full h-48 object-cover rounded-lg"
                  />
                </div>
              )}

              {/* Incident Info */}
              <div className="space-y-4 mb-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Type</label>
                    <p className="text-sm text-gray-900">
                      {selectedIncident.type.replace(/_/g, ' ')}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Severity</label>
                    <Badge className={getSeverityBadgeColor(selectedIncident.severity)}>
                      {selectedIncident.severity?.toUpperCase()}
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-600">Description</label>
                  <p className="text-sm text-gray-900 mt-1">{selectedIncident.description}</p>
                </div>

                {selectedIncident.notes && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Notes</label>
                    <p className="text-sm text-gray-900 mt-1">{selectedIncident.notes}</p>
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Location</label>
                    <p className="text-sm text-gray-900">
                      {selectedIncident.location || 'Not specified'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Reported</label>
                    <p className="text-sm text-gray-900">
                      {new Date(selectedIncident.createdAt).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </p>
                  </div>
                </div>

                {/* AI Analysis */}
                {selectedIncident.aiAnalysis && (
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="font-semibold text-safety-blue mb-2">AI Analysis</h4>
                    <div className="text-sm space-y-1">
                      <p>
                        <strong>Risk Level:</strong> {selectedIncident.aiAnalysis.riskLevel}
                      </p>
                      {selectedIncident.aiAnalysis.detections && (
                        <div>
                          <strong>Detections:</strong>
                          <ul className="mt-1 ml-4 space-y-1">
                            {(selectedIncident.aiAnalysis.detections as any[]).map(
                              (detection: any, index: number) => (
                                <li key={index} className="text-xs">
                                  • {detection.description} (
                                  {Math.round(detection.confidence * 100)}%)
                                </li>
                              )
                            )}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Status Actions */}
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-600 mb-2 block">
                    Update Status
                  </label>
                  <div className="flex space-x-2">
                    {selectedIncident.status !== 'investigating' && (
                      <Button
                        onClick={() => handleStatusChange('investigating')}
                        disabled={updateStatusMutation.isPending}
                        className="flex-1 bg-safety-orange hover:bg-orange-600"
                        size="sm"
                      >
                        <AlertTriangle className="w-4 h-4 mr-1" />
                        Investigate
                      </Button>
                    )}
                    {selectedIncident.status !== 'resolved' && (
                      <Button
                        onClick={() => handleStatusChange('resolved')}
                        disabled={updateStatusMutation.isPending}
                        className="flex-1 bg-safety-green hover:bg-green-600"
                        size="sm"
                      >
                        <CheckCircle className="w-4 h-4 mr-1" />
                        Resolve
                      </Button>
                    )}
                  </div>
                </div>

                <Button
                  onClick={() => setIsDetailModalOpen(false)}
                  variant="outline"
                  className="w-full"
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
