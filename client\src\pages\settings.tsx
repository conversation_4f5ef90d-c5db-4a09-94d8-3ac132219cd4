import { useState } from 'react';
import { ArrowLeft, Bell, Camera, Mic, MapPin, Download, Shield, User, LogOut } from 'lucide-react';
import { Link } from 'wouter';
import MobileLayout from '@/components/mobile-layout';
import Navigation from '@/components/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';

export default function Settings() {
  const [notificationEnabled, setNotificationEnabled] = useState(true);
  const [locationEnabled, setLocationEnabled] = useState(true);
  const [cameraEnabled, setCameraEnabled] = useState(true);
  const [microphoneEnabled, setMicrophoneEnabled] = useState(true);
  const [alertFrequency, setAlertFrequency] = useState('immediate');
  const [autoExport, setAutoExport] = useState(false);
  const [darkMode, setDarkMode] = useState(false);

  const { toast } = useToast();

  const handlePermissionRequest = async (type: string) => {
    try {
      switch (type) {
        case 'camera':
          await navigator.mediaDevices.getUserMedia({ video: true });
          setCameraEnabled(true);
          toast({
            title: "Camera Access Granted",
            description: "Camera permission has been enabled for safety detection.",
          });
          break;
        case 'microphone':
          await navigator.mediaDevices.getUserMedia({ audio: true });
          setMicrophoneEnabled(true);
          toast({
            title: "Microphone Access Granted",
            description: "Microphone permission has been enabled for voice reporting.",
          });
          break;
        case 'location':
          await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject);
          });
          setLocationEnabled(true);
          toast({
            title: "Location Access Granted",
            description: "Location permission has been enabled for incident tracking.",
          });
          break;
        case 'notifications':
          const permission = await Notification.requestPermission();
          if (permission === 'granted') {
            setNotificationEnabled(true);
            toast({
              title: "Notifications Enabled",
              description: "You will now receive safety alerts and notifications.",
            });
          }
          break;
      }
    } catch (error) {
      toast({
        title: "Permission Denied",
        description: `Failed to enable ${type} access. Please check browser settings.`,
        variant: "destructive",
      });
    }
  };

  const handleClearData = () => {
    toast({
      title: "Data Cleared",
      description: "Local app data has been cleared successfully.",
    });
  };

  const handleExportData = () => {
    toast({
      title: "Data Export",
      description: "Your safety data has been exported successfully.",
    });
  };

  return (
    <>
      <MobileLayout
        title="Settings"
        subtitle="App configuration and preferences"
      >
        <div className="px-4 py-6">
          {/* User Profile Section */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <User className="w-5 h-5 mr-2" />
                User Profile
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-16 h-16 bg-safety-blue rounded-full flex items-center justify-center">
                  <User className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Safety Inspector</h3>
                  <p className="text-sm text-gray-600">Building A - Level 2</p>
                  <p className="text-xs text-gray-500"><EMAIL></p>
                </div>
              </div>
              <Button variant="outline" className="w-full">
                Edit Profile
              </Button>
            </CardContent>
          </Card>

          {/* Permissions Section */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Shield className="w-5 h-5 mr-2" />
                Permissions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Camera className="w-5 h-5 text-gray-600" />
                    <div>
                      <p className="font-medium">Camera Access</p>
                      <p className="text-sm text-gray-600">For live detection and photo capture</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch 
                      checked={cameraEnabled} 
                      onCheckedChange={(checked) => {
                        if (checked) {
                          handlePermissionRequest('camera');
                        } else {
                          setCameraEnabled(false);
                        }
                      }}
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Mic className="w-5 h-5 text-gray-600" />
                    <div>
                      <p className="font-medium">Microphone Access</p>
                      <p className="text-sm text-gray-600">For voice reporting functionality</p>
                    </div>
                  </div>
                  <Switch 
                    checked={microphoneEnabled} 
                    onCheckedChange={(checked) => {
                      if (checked) {
                        handlePermissionRequest('microphone');
                      } else {
                        setMicrophoneEnabled(false);
                      }
                    }}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-gray-600" />
                    <div>
                      <p className="font-medium">Location Access</p>
                      <p className="text-sm text-gray-600">For GPS tracking of incidents</p>
                    </div>
                  </div>
                  <Switch 
                    checked={locationEnabled} 
                    onCheckedChange={(checked) => {
                      if (checked) {
                        handlePermissionRequest('location');
                      } else {
                        setLocationEnabled(false);
                      }
                    }}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Bell className="w-5 h-5 text-gray-600" />
                    <div>
                      <p className="font-medium">Push Notifications</p>
                      <p className="text-sm text-gray-600">For safety alerts and updates</p>
                    </div>
                  </div>
                  <Switch 
                    checked={notificationEnabled} 
                    onCheckedChange={(checked) => {
                      if (checked) {
                        handlePermissionRequest('notifications');
                      } else {
                        setNotificationEnabled(false);
                      }
                    }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Alert Settings */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Alert Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Alert Frequency
                  </label>
                  <Select value={alertFrequency} onValueChange={setAlertFrequency}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="immediate">Immediate</SelectItem>
                      <SelectItem value="5min">Every 5 Minutes</SelectItem>
                      <SelectItem value="15min">Every 15 Minutes</SelectItem>
                      <SelectItem value="hourly">Hourly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Auto Export Reports</p>
                    <p className="text-sm text-gray-600">Automatically export daily reports</p>
                  </div>
                  <Switch checked={autoExport} onCheckedChange={setAutoExport} />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* App Preferences */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">App Preferences</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Dark Mode</p>
                    <p className="text-sm text-gray-600">Switch to dark theme</p>
                  </div>
                  <Switch checked={darkMode} onCheckedChange={setDarkMode} />
                </div>

                <div>
                  <p className="font-medium mb-2">App Version</p>
                  <p className="text-sm text-gray-600">AI Safety Assistant v2.1.0</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Data Management */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Download className="w-5 h-5 mr-2" />
                Data Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button 
                  onClick={handleExportData}
                  variant="outline" 
                  className="w-full justify-start"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Export My Data
                </Button>
                
                <Button 
                  onClick={handleClearData}
                  variant="outline" 
                  className="w-full justify-start text-safety-red border-safety-red hover:bg-red-50"
                >
                  <Shield className="w-4 h-4 mr-2" />
                  Clear Local Data
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Support & About */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Support & About</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button variant="ghost" className="w-full justify-start">
                  Help & FAQ
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  Contact Support
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  Privacy Policy
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  Terms of Service
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Logout */}
          <Button 
            variant="outline" 
            className="w-full border-safety-red text-safety-red hover:bg-red-50 mb-20"
          >
            <LogOut className="w-4 h-4 mr-2" />
            Sign Out
          </Button>
        </div>
      </MobileLayout>

      <Navigation />
    </>
  );
}
