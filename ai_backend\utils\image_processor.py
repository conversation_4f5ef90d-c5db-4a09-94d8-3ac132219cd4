"""
Image Processing Utilities
Advanced image processing functions for AI safety detection
"""

import base64
import io
import logging
from typing import Tuple, Optional, List, Dict, Any
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import albumentations as A

logger = logging.getLogger(__name__)

class ImageProcessor:
    """
    Advanced image processing utilities for safety detection
    """
    
    @staticmethod
    def decode_base64_image(base64_string: str) -> np.ndarray:
        """
        Decode base64 encoded image to numpy array
        
        Args:
            base64_string: Base64 encoded image string
            
        Returns:
            Image as numpy array in BGR format
        """
        try:
            # Remove data URL prefix if present
            if base64_string.startswith('data:image'):
                base64_string = base64_string.split(',')[1]
            
            # Decode base64
            image_bytes = base64.b64decode(base64_string)
            
            # Convert to PIL Image
            pil_image = Image.open(io.BytesIO(image_bytes))
            
            # Convert to RGB if necessary
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            # Convert to numpy array (RGB)
            image_array = np.array(pil_image)
            
            # Convert RGB to BGR for OpenCV
            image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            
            return image_bgr
            
        except Exception as e:
            logger.error(f"Failed to decode base64 image: {e}")
            raise ValueError(f"Invalid image data: {e}")
    
    @staticmethod
    def encode_image_to_base64(image: np.ndarray, format: str = 'JPEG', quality: int = 85) -> str:
        """
        Encode numpy array image to base64 string
        
        Args:
            image: Image as numpy array in BGR format
            format: Output format ('JPEG', 'PNG')
            quality: JPEG quality (1-100)
            
        Returns:
            Base64 encoded image string
        """
        try:
            # Convert BGR to RGB
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Convert to PIL Image
            pil_image = Image.fromarray(image_rgb)
            
            # Save to bytes buffer
            buffer = io.BytesIO()
            if format.upper() == 'JPEG':
                pil_image.save(buffer, format='JPEG', quality=quality, optimize=True)
            else:
                pil_image.save(buffer, format=format.upper())
            
            # Encode to base64
            image_bytes = buffer.getvalue()
            base64_string = base64.b64encode(image_bytes).decode('utf-8')
            
            return base64_string
            
        except Exception as e:
            logger.error(f"Failed to encode image to base64: {e}")
            raise ValueError(f"Image encoding failed: {e}")
    
    @staticmethod
    def resize_image(image: np.ndarray, target_size: Tuple[int, int], 
                    maintain_aspect_ratio: bool = True) -> np.ndarray:
        """
        Resize image to target size
        
        Args:
            image: Input image
            target_size: (width, height) target size
            maintain_aspect_ratio: Whether to maintain aspect ratio
            
        Returns:
            Resized image
        """
        try:
            height, width = image.shape[:2]
            target_width, target_height = target_size
            
            if maintain_aspect_ratio:
                # Calculate scaling factor
                scale = min(target_width / width, target_height / height)
                
                # Calculate new dimensions
                new_width = int(width * scale)
                new_height = int(height * scale)
                
                # Resize image
                resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
                
                # Create canvas with target size
                canvas = np.zeros((target_height, target_width, image.shape[2]), dtype=image.dtype)
                
                # Center the resized image on canvas
                y_offset = (target_height - new_height) // 2
                x_offset = (target_width - new_width) // 2
                canvas[y_offset:y_offset + new_height, x_offset:x_offset + new_width] = resized
                
                return canvas
            else:
                return cv2.resize(image, target_size, interpolation=cv2.INTER_AREA)
                
        except Exception as e:
            logger.error(f"Failed to resize image: {e}")
            return image
    
    @staticmethod
    def enhance_image_quality(image: np.ndarray) -> np.ndarray:
        """
        Enhance image quality for better AI detection
        
        Args:
            image: Input image
            
        Returns:
            Enhanced image
        """
        try:
            # Convert to PIL for enhancement
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(pil_image)
            enhanced = enhancer.enhance(1.2)
            
            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(enhanced)
            enhanced = enhancer.enhance(1.1)
            
            # Enhance brightness slightly
            enhancer = ImageEnhance.Brightness(enhanced)
            enhanced = enhancer.enhance(1.05)
            
            # Convert back to numpy array
            enhanced_array = np.array(enhanced)
            enhanced_bgr = cv2.cvtColor(enhanced_array, cv2.COLOR_RGB2BGR)
            
            return enhanced_bgr
            
        except Exception as e:
            logger.error(f"Failed to enhance image: {e}")
            return image
    
    @staticmethod
    def denoise_image(image: np.ndarray) -> np.ndarray:
        """
        Remove noise from image
        
        Args:
            image: Input image
            
        Returns:
            Denoised image
        """
        try:
            # Use Non-local Means Denoising
            if len(image.shape) == 3:
                denoised = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
            else:
                denoised = cv2.fastNlMeansDenoising(image, None, 10, 7, 21)
            
            return denoised
            
        except Exception as e:
            logger.error(f"Failed to denoise image: {e}")
            return image
    
    @staticmethod
    def normalize_lighting(image: np.ndarray) -> np.ndarray:
        """
        Normalize lighting conditions in image
        
        Args:
            image: Input image
            
        Returns:
            Lighting-normalized image
        """
        try:
            # Convert to LAB color space
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            
            # Split channels
            l, a, b = cv2.split(lab)
            
            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization) to L channel
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l_enhanced = clahe.apply(l)
            
            # Merge channels
            lab_enhanced = cv2.merge([l_enhanced, a, b])
            
            # Convert back to BGR
            normalized = cv2.cvtColor(lab_enhanced, cv2.COLOR_LAB2BGR)
            
            return normalized
            
        except Exception as e:
            logger.error(f"Failed to normalize lighting: {e}")
            return image
    
    @staticmethod
    def apply_safety_augmentations(image: np.ndarray) -> List[np.ndarray]:
        """
        Apply safety-specific augmentations for robust detection
        
        Args:
            image: Input image
            
        Returns:
            List of augmented images
        """
        try:
            # Define augmentation pipeline
            transform = A.Compose([
                A.OneOf([
                    A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=0.5),
                    A.HueSaturationValue(hue_shift_limit=10, sat_shift_limit=20, val_shift_limit=20, p=0.5),
                ], p=0.7),
                A.OneOf([
                    A.GaussNoise(var_limit=(10.0, 50.0), p=0.3),
                    A.ISONoise(color_shift=(0.01, 0.05), intensity=(0.1, 0.5), p=0.3),
                ], p=0.4),
                A.OneOf([
                    A.MotionBlur(blur_limit=3, p=0.3),
                    A.GaussianBlur(blur_limit=3, p=0.3),
                ], p=0.3),
            ])
            
            augmented_images = []
            
            # Apply augmentations multiple times
            for _ in range(3):
                augmented = transform(image=image)['image']
                augmented_images.append(augmented)
            
            return augmented_images
            
        except Exception as e:
            logger.error(f"Failed to apply augmentations: {e}")
            return [image]
    
    @staticmethod
    def extract_roi(image: np.ndarray, bbox: Dict[str, int], padding: int = 10) -> np.ndarray:
        """
        Extract region of interest from image
        
        Args:
            image: Input image
            bbox: Bounding box dictionary with x, y, width, height
            padding: Padding around the ROI
            
        Returns:
            Extracted ROI
        """
        try:
            height, width = image.shape[:2]
            
            x = max(0, bbox['x'] - padding)
            y = max(0, bbox['y'] - padding)
            x2 = min(width, bbox['x'] + bbox['width'] + padding)
            y2 = min(height, bbox['y'] + bbox['height'] + padding)
            
            roi = image[y:y2, x:x2]
            
            return roi
            
        except Exception as e:
            logger.error(f"Failed to extract ROI: {e}")
            return image
    
    @staticmethod
    def draw_detection_overlay(image: np.ndarray, detections: List[Dict[str, Any]]) -> np.ndarray:
        """
        Draw detection overlays on image
        
        Args:
            image: Input image
            detections: List of detection results
            
        Returns:
            Image with detection overlays
        """
        try:
            overlay_image = image.copy()
            
            # Color mapping for different detection types
            colors = {
                'PPE_VIOLATION': (0, 0, 255),      # Red
                'PPE_EQUIPMENT': (0, 255, 0),      # Green
                'HAZARD': (0, 165, 255),           # Orange
                'FALL_DETECTED': (0, 0, 139),      # Dark Red
                'ZONE_VIOLATION': (255, 255, 0),   # Yellow
                'VEHICLE_PROXIMITY': (255, 0, 255), # Magenta
                'TOOLBOX_HAZARD': (128, 0, 128)    # Purple
            }
            
            for detection in detections:
                bbox = detection.get('bounding_box', {})
                detection_type = detection.get('type', 'UNKNOWN')
                confidence = detection.get('confidence', 0.0)
                description = detection.get('description', '')
                
                if bbox:
                    x, y, w, h = bbox['x'], bbox['y'], bbox['width'], bbox['height']
                    color = colors.get(detection_type, (128, 128, 128))
                    
                    # Draw bounding box
                    cv2.rectangle(overlay_image, (x, y), (x + w, y + h), color, 2)
                    
                    # Draw label background
                    label = f"{detection_type}: {confidence:.2f}"
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
                    cv2.rectangle(overlay_image, (x, y - label_size[1] - 10), 
                                (x + label_size[0], y), color, -1)
                    
                    # Draw label text
                    cv2.putText(overlay_image, label, (x, y - 5), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                    
                    # Draw description if space allows
                    if len(description) < 30:
                        cv2.putText(overlay_image, description, (x, y + h + 15), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
            
            return overlay_image
            
        except Exception as e:
            logger.error(f"Failed to draw detection overlay: {e}")
            return image
    
    @staticmethod
    def calculate_image_quality_score(image: np.ndarray) -> float:
        """
        Calculate image quality score for AI processing
        
        Args:
            image: Input image
            
        Returns:
            Quality score (0.0 to 1.0)
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Calculate sharpness using Laplacian variance
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            sharpness_score = min(laplacian_var / 1000.0, 1.0)  # Normalize
            
            # Calculate brightness
            brightness = np.mean(gray) / 255.0
            brightness_score = 1.0 - abs(brightness - 0.5) * 2  # Optimal around 0.5
            
            # Calculate contrast
            contrast = gray.std() / 255.0
            contrast_score = min(contrast * 4, 1.0)  # Normalize
            
            # Calculate noise level (inverse of quality)
            noise_level = np.std(cv2.GaussianBlur(gray, (5, 5), 0) - gray)
            noise_score = max(0, 1.0 - noise_level / 50.0)
            
            # Combine scores
            quality_score = (sharpness_score * 0.4 + brightness_score * 0.2 + 
                           contrast_score * 0.2 + noise_score * 0.2)
            
            return min(max(quality_score, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Failed to calculate image quality: {e}")
            return 0.5  # Default medium quality
    
    @staticmethod
    def preprocess_for_ai(image: np.ndarray, target_size: Tuple[int, int] = (640, 640)) -> np.ndarray:
        """
        Preprocess image for AI model inference
        
        Args:
            image: Input image
            target_size: Target size for AI model
            
        Returns:
            Preprocessed image
        """
        try:
            # Enhance image quality
            enhanced = ImageProcessor.enhance_image_quality(image)
            
            # Normalize lighting
            normalized = ImageProcessor.normalize_lighting(enhanced)
            
            # Resize to target size
            resized = ImageProcessor.resize_image(normalized, target_size, maintain_aspect_ratio=True)
            
            return resized
            
        except Exception as e:
            logger.error(f"Failed to preprocess image for AI: {e}")
            return image
