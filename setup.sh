#!/bin/bash

echo "========================================"
echo "  AI-Powered Safety Assistant Setup"
echo "========================================"
echo

echo "[1/5] Installing Node.js dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install dependencies"
    exit 1
fi

echo
echo "[2/5] Installing client dependencies..."
cd client
npm install
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install client dependencies"
    cd ..
    exit 1
fi

echo
echo "[3/5] Running code quality checks..."
npm run type-check
if [ $? -ne 0 ]; then
    echo "WARNING: TypeScript check failed, but continuing..."
fi

npm run lint
if [ $? -ne 0 ]; then
    echo "WARNING: ESLint check failed, attempting to fix..."
    npm run lint:fix
fi

echo
echo "[4/5] Setting up Python backend (optional)..."
cd ..
if [ -d "ai_backend" ]; then
    echo "Python backend found, setting up virtual environment..."
    python3 -m venv ai_backend/venv
    source ai_backend/venv/bin/activate
    pip install -r ai_backend/requirements.txt
    if [ $? -ne 0 ]; then
        echo "WARNING: Python backend setup failed, but continuing..."
    fi
else
    echo "Python backend not found, skipping..."
fi

echo
echo "[5/5] Setup complete!"
echo
echo "========================================"
echo "  Ready to run the application!"
echo "========================================"
echo
echo "To start the development server:"
echo "  npm run dev"
echo
echo "To start the backend (if available):"
echo "  cd ai_backend"
echo "  source venv/bin/activate"
echo "  uvicorn main:app --reload"
echo
