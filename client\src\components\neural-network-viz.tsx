import { useEffect, useRef, useState } from 'react';

interface Node {
  id: string;
  x: number;
  y: number;
  radius: number;
  color: string;
  activity: number;
  type: 'input' | 'hidden' | 'output';
}

interface Connection {
  from: string;
  to: string;
  weight: number;
  active: boolean;
}

interface NeuralNetworkVisualizationProps {
  modules: Array<{
    id: string;
    name: string;
    status: 'active' | 'processing' | 'idle';
    accuracy: number;
  }>;
}

export default function NeuralNetworkVisualization({ modules }: NeuralNetworkVisualizationProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const [nodes, setNodes] = useState<Node[]>([]);
  const [connections, setConnections] = useState<Connection[]>([]);

  useEffect(() => {
    initializeNetwork();
    animate();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [modules]);

  const initializeNetwork = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const width = canvas.width;
    const height = canvas.height;

    // Create input layer (sensors)
    const inputNodes: Node[] = [
      { id: 'camera', x: 50, y: height * 0.2, radius: 8, color: '#3B82F6', activity: 0.8, type: 'input' },
      { id: 'audio', x: 50, y: height * 0.4, radius: 8, color: '#8B5CF6', activity: 0.6, type: 'input' },
      { id: 'sensors', x: 50, y: height * 0.6, radius: 8, color: '#06B6D4', activity: 0.7, type: 'input' },
      { id: 'location', x: 50, y: height * 0.8, radius: 8, color: '#10B981', activity: 0.5, type: 'input' }
    ];

    // Create hidden layers (processing)
    const hiddenNodes: Node[] = [];
    for (let layer = 0; layer < 3; layer++) {
      for (let i = 0; i < 4; i++) {
        hiddenNodes.push({
          id: `hidden_${layer}_${i}`,
          x: 150 + layer * 80,
          y: height * 0.2 + i * (height * 0.6 / 3),
          radius: 6,
          color: '#A855F7',
          activity: Math.random(),
          type: 'hidden'
        });
      }
    }

    // Create output nodes (modules)
    const outputNodes: Node[] = modules.map((module, index) => ({
      id: module.id,
      x: width - 50,
      y: height * 0.15 + index * (height * 0.7 / modules.length),
      radius: 10,
      color: module.status === 'active' ? '#10B981' : 
             module.status === 'processing' ? '#F59E0B' : '#6B7280',
      activity: module.accuracy / 100,
      type: 'output'
    }));

    // Create connections
    const newConnections: Connection[] = [];
    
    // Input to first hidden layer
    inputNodes.forEach(input => {
      hiddenNodes.slice(0, 4).forEach(hidden => {
        newConnections.push({
          from: input.id,
          to: hidden.id,
          weight: Math.random(),
          active: Math.random() > 0.3
        });
      });
    });

    // Hidden layer connections
    for (let layer = 0; layer < 2; layer++) {
      const currentLayer = hiddenNodes.slice(layer * 4, (layer + 1) * 4);
      const nextLayer = hiddenNodes.slice((layer + 1) * 4, (layer + 2) * 4);
      
      currentLayer.forEach(current => {
        nextLayer.forEach(next => {
          newConnections.push({
            from: current.id,
            to: next.id,
            weight: Math.random(),
            active: Math.random() > 0.4
          });
        });
      });
    }

    // Last hidden layer to outputs
    hiddenNodes.slice(-4).forEach(hidden => {
      outputNodes.forEach(output => {
        newConnections.push({
          from: hidden.id,
          to: output.id,
          weight: Math.random(),
          active: Math.random() > 0.2
        });
      });
    });

    setNodes([...inputNodes, ...hiddenNodes, ...outputNodes]);
    setConnections(newConnections);
  };

  const animate = () => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Update node activities
    setNodes(prevNodes => 
      prevNodes.map(node => ({
        ...node,
        activity: node.type === 'input' ? 
          0.5 + 0.3 * Math.sin(Date.now() * 0.001 + Math.random()) :
          node.type === 'hidden' ?
          0.3 + 0.4 * Math.sin(Date.now() * 0.002 + Math.random()) :
          node.activity
      }))
    );

    // Update connection activities
    setConnections(prevConnections =>
      prevConnections.map(conn => ({
        ...conn,
        active: Math.random() > 0.6
      }))
    );

    // Draw connections
    connections.forEach(connection => {
      const fromNode = nodes.find(n => n.id === connection.from);
      const toNode = nodes.find(n => n.id === connection.to);
      
      if (fromNode && toNode) {
        ctx.beginPath();
        ctx.moveTo(fromNode.x, fromNode.y);
        ctx.lineTo(toNode.x, toNode.y);
        
        if (connection.active) {
          ctx.strokeStyle = `rgba(168, 85, 247, ${connection.weight * 0.8})`;
          ctx.lineWidth = 2;
          
          // Add flowing effect
          const gradient = ctx.createLinearGradient(fromNode.x, fromNode.y, toNode.x, toNode.y);
          gradient.addColorStop(0, 'rgba(168, 85, 247, 0.2)');
          gradient.addColorStop(0.5, 'rgba(168, 85, 247, 0.8)');
          gradient.addColorStop(1, 'rgba(168, 85, 247, 0.2)');
          ctx.strokeStyle = gradient;
        } else {
          ctx.strokeStyle = 'rgba(156, 163, 175, 0.3)';
          ctx.lineWidth = 1;
        }
        
        ctx.stroke();
      }
    });

    // Draw nodes
    nodes.forEach(node => {
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.radius, 0, 2 * Math.PI);
      
      // Node glow effect based on activity
      const glowRadius = node.radius + node.activity * 8;
      const gradient = ctx.createRadialGradient(
        node.x, node.y, 0,
        node.x, node.y, glowRadius
      );
      
      gradient.addColorStop(0, node.color);
      gradient.addColorStop(0.7, `${node.color}80`);
      gradient.addColorStop(1, 'transparent');
      
      ctx.fillStyle = gradient;
      ctx.fill();
      
      // Inner node
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.radius * 0.7, 0, 2 * Math.PI);
      ctx.fillStyle = node.color;
      ctx.fill();
      
      // Activity pulse
      if (node.activity > 0.7) {
        ctx.beginPath();
        ctx.arc(node.x, node.y, node.radius * 1.5, 0, 2 * Math.PI);
        ctx.strokeStyle = `${node.color}40`;
        ctx.lineWidth = 2;
        ctx.stroke();
      }
    });

    // Draw data flow particles
    drawDataFlowParticles(ctx);

    animationRef.current = requestAnimationFrame(animate);
  };

  const drawDataFlowParticles = (ctx: CanvasRenderingContext2D) => {
    const time = Date.now() * 0.003;
    
    connections.forEach((connection, index) => {
      if (!connection.active) return;
      
      const fromNode = nodes.find(n => n.id === connection.from);
      const toNode = nodes.find(n => n.id === connection.to);
      
      if (fromNode && toNode) {
        const progress = (Math.sin(time + index * 0.5) + 1) / 2;
        const x = fromNode.x + (toNode.x - fromNode.x) * progress;
        const y = fromNode.y + (toNode.y - fromNode.y) * progress;
        
        ctx.beginPath();
        ctx.arc(x, y, 3, 0, 2 * Math.PI);
        ctx.fillStyle = '#06B6D4';
        ctx.fill();
        
        // Particle trail
        ctx.beginPath();
        ctx.arc(x, y, 6, 0, 2 * Math.PI);
        ctx.fillStyle = 'rgba(6, 182, 212, 0.3)';
        ctx.fill();
      }
    });
  };

  return (
    <div className="relative w-full h-64 bg-gradient-to-r from-slate-900 via-purple-900 to-slate-900 rounded-lg overflow-hidden">
      <canvas
        ref={canvasRef}
        width={400}
        height={256}
        className="w-full h-full"
      />
      
      {/* Network Status Overlay */}
      <div className="absolute top-4 left-4 glass-dark rounded-lg p-3">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-ai-accent rounded-full animate-pulse"></div>
          <span className="text-white text-xs font-medium">Neural Network Active</span>
        </div>
        <div className="text-white text-xs opacity-75 mt-1">
          Processing {modules.filter(m => m.status === 'active').length} modules
        </div>
      </div>
      
      {/* Performance Metrics */}
      <div className="absolute top-4 right-4 glass-dark rounded-lg p-3">
        <div className="text-white text-xs">
          <div className="flex justify-between">
            <span>Throughput:</span>
            <span className="text-ai-accent">2.3k/sec</span>
          </div>
          <div className="flex justify-between">
            <span>Latency:</span>
            <span className="text-safety-green">12ms</span>
          </div>
        </div>
      </div>
      
      {/* Layer Labels */}
      <div className="absolute bottom-4 left-4 flex space-x-16 text-white text-xs">
        <div className="text-center">
          <div className="w-2 h-2 bg-blue-500 rounded-full mx-auto mb-1"></div>
          <span>Input</span>
        </div>
        <div className="text-center">
          <div className="w-2 h-2 bg-purple-500 rounded-full mx-auto mb-1"></div>
          <span>Processing</span>
        </div>
        <div className="text-center">
          <div className="w-2 h-2 bg-green-500 rounded-full mx-auto mb-1"></div>
          <span>Detection</span>
        </div>
      </div>
    </div>
  );
}
