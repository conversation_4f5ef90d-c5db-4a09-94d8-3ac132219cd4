"""
Advanced Logging Utilities
Structured logging for AI safety detection system
"""

import logging
import logging.handlers
import json
import time
import sys
from pathlib import Path
from typing import Dict, Any, Optional
import structlog
from datetime import datetime

def setup_logger(name: str, log_level: str = "INFO", log_file: Optional[str] = None) -> logging.Logger:
    """
    Setup structured logger with JSON formatting
    
    Args:
        name: Logger name
        log_level: Logging level
        log_file: Optional log file path
        
    Returns:
        Configured logger
    """
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, log_level.upper()))
    
    # JSON formatter for structured logging
    formatter = StructuredFormatter()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler if specified
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5
        )
        file_handler.setLevel(getattr(logging, log_level.upper()))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

class StructuredFormatter(logging.Formatter):
    """
    Custom formatter for structured JSON logging
    """
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        
        # Base log data
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields from record
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info']:
                log_data[key] = value
        
        return json.dumps(log_data, default=str)

class SafetyLogger:
    """
    Specialized logger for safety detection events
    """
    
    def __init__(self, logger_name: str = "safety_detection"):
        self.logger = setup_logger(logger_name)
        self.session_id = self._generate_session_id()
    
    def _generate_session_id(self) -> str:
        """Generate unique session ID"""
        return f"session_{int(time.time())}"
    
    def log_detection(self, detection_type: str, confidence: float, 
                     location: Optional[Dict] = None, metadata: Optional[Dict] = None):
        """
        Log safety detection event
        
        Args:
            detection_type: Type of detection
            confidence: Detection confidence
            location: Location information
            metadata: Additional metadata
        """
        log_data = {
            "event_type": "safety_detection",
            "session_id": self.session_id,
            "detection_type": detection_type,
            "confidence": confidence,
            "location": location or {},
            "metadata": metadata or {}
        }
        
        self.logger.info("Safety detection event", extra=log_data)
    
    def log_incident(self, incident_type: str, severity: str, description: str,
                    location: Optional[Dict] = None, response_actions: Optional[list] = None):
        """
        Log safety incident
        
        Args:
            incident_type: Type of incident
            severity: Incident severity
            description: Incident description
            location: Location information
            response_actions: Response actions taken
        """
        log_data = {
            "event_type": "safety_incident",
            "session_id": self.session_id,
            "incident_type": incident_type,
            "severity": severity,
            "description": description,
            "location": location or {},
            "response_actions": response_actions or []
        }
        
        self.logger.warning("Safety incident logged", extra=log_data)
    
    def log_model_performance(self, model_name: str, inference_time: float,
                            accuracy: Optional[float] = None, memory_usage: Optional[float] = None):
        """
        Log AI model performance metrics
        
        Args:
            model_name: Name of the model
            inference_time: Inference time in seconds
            accuracy: Model accuracy
            memory_usage: Memory usage in MB
        """
        log_data = {
            "event_type": "model_performance",
            "session_id": self.session_id,
            "model_name": model_name,
            "inference_time": inference_time,
            "accuracy": accuracy,
            "memory_usage": memory_usage
        }
        
        self.logger.info("Model performance metrics", extra=log_data)
    
    def log_system_event(self, event_type: str, message: str, 
                        system_info: Optional[Dict] = None):
        """
        Log system event
        
        Args:
            event_type: Type of system event
            message: Event message
            system_info: System information
        """
        log_data = {
            "event_type": "system_event",
            "session_id": self.session_id,
            "system_event_type": event_type,
            "system_info": system_info or {}
        }
        
        self.logger.info(message, extra=log_data)
    
    def log_user_action(self, user_id: str, action: str, 
                       details: Optional[Dict] = None):
        """
        Log user action
        
        Args:
            user_id: User identifier
            action: Action performed
            details: Action details
        """
        log_data = {
            "event_type": "user_action",
            "session_id": self.session_id,
            "user_id": user_id,
            "action": action,
            "details": details or {}
        }
        
        self.logger.info("User action logged", extra=log_data)
    
    def log_api_request(self, endpoint: str, method: str, status_code: int,
                       response_time: float, user_id: Optional[str] = None):
        """
        Log API request
        
        Args:
            endpoint: API endpoint
            method: HTTP method
            status_code: Response status code
            response_time: Response time in seconds
            user_id: User identifier
        """
        log_data = {
            "event_type": "api_request",
            "session_id": self.session_id,
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "response_time": response_time,
            "user_id": user_id
        }
        
        self.logger.info("API request processed", extra=log_data)
    
    def log_error(self, error_type: str, error_message: str, 
                 context: Optional[Dict] = None, exception: Optional[Exception] = None):
        """
        Log error event
        
        Args:
            error_type: Type of error
            error_message: Error message
            context: Error context
            exception: Exception object
        """
        log_data = {
            "event_type": "error",
            "session_id": self.session_id,
            "error_type": error_type,
            "context": context or {}
        }
        
        if exception:
            self.logger.error(error_message, extra=log_data, exc_info=exception)
        else:
            self.logger.error(error_message, extra=log_data)

class PerformanceLogger:
    """
    Performance monitoring and logging
    """
    
    def __init__(self, logger_name: str = "performance"):
        self.logger = setup_logger(logger_name)
        self.metrics = {}
    
    def start_timer(self, operation: str):
        """Start timing an operation"""
        self.metrics[operation] = {"start_time": time.time()}
    
    def end_timer(self, operation: str, metadata: Optional[Dict] = None):
        """End timing an operation and log the result"""
        if operation in self.metrics:
            end_time = time.time()
            duration = end_time - self.metrics[operation]["start_time"]
            
            log_data = {
                "event_type": "performance_metric",
                "operation": operation,
                "duration": duration,
                "metadata": metadata or {}
            }
            
            self.logger.info(f"Operation {operation} completed", extra=log_data)
            
            # Clean up
            del self.metrics[operation]
    
    def log_memory_usage(self, component: str, memory_mb: float):
        """Log memory usage"""
        log_data = {
            "event_type": "memory_usage",
            "component": component,
            "memory_mb": memory_mb
        }
        
        self.logger.info("Memory usage recorded", extra=log_data)
    
    def log_throughput(self, operation: str, items_processed: int, 
                      time_period: float):
        """Log throughput metrics"""
        throughput = items_processed / time_period if time_period > 0 else 0
        
        log_data = {
            "event_type": "throughput_metric",
            "operation": operation,
            "items_processed": items_processed,
            "time_period": time_period,
            "throughput": throughput
        }
        
        self.logger.info("Throughput metric recorded", extra=log_data)

class AuditLogger:
    """
    Audit logging for compliance and security
    """
    
    def __init__(self, logger_name: str = "audit"):
        self.logger = setup_logger(logger_name, log_file="logs/audit.log")
    
    def log_data_access(self, user_id: str, data_type: str, 
                       action: str, resource_id: Optional[str] = None):
        """Log data access event"""
        log_data = {
            "event_type": "data_access",
            "user_id": user_id,
            "data_type": data_type,
            "action": action,
            "resource_id": resource_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.logger.info("Data access event", extra=log_data)
    
    def log_model_inference(self, model_name: str, input_hash: str,
                          output_hash: str, user_id: Optional[str] = None):
        """Log model inference for audit trail"""
        log_data = {
            "event_type": "model_inference",
            "model_name": model_name,
            "input_hash": input_hash,
            "output_hash": output_hash,
            "user_id": user_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.logger.info("Model inference audit", extra=log_data)
    
    def log_configuration_change(self, user_id: str, component: str,
                                old_config: Dict, new_config: Dict):
        """Log configuration changes"""
        log_data = {
            "event_type": "configuration_change",
            "user_id": user_id,
            "component": component,
            "old_config": old_config,
            "new_config": new_config,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.logger.warning("Configuration changed", extra=log_data)

# Global logger instances
safety_logger = SafetyLogger()
performance_logger = PerformanceLogger()
audit_logger = AuditLogger()
