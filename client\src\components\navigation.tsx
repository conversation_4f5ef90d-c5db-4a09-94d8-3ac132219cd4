import { Link, useLocation } from 'wouter';
import { Home, Search, BarChart3, Settings } from 'lucide-react';
import { cn } from '@/lib/utils';

export default function Navigation() {
  const [location] = useLocation();

  const navItems = [
    { path: '/', icon: Home, label: 'Home' },
    { path: '/incidents', icon: Search, label: 'Inspect' },
    { path: '/reports', icon: BarChart3, label: 'Reports' },
    { path: '/settings', icon: Settings, label: 'Settings' }
  ];

  return (
    <nav className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-white border-t border-gray-200 px-4 py-2 z-30">
      <div className="flex justify-around">
        {navItems.map(({ path, icon: Icon, label }) => {
          const isActive = location === path;
          
          return (
            <Link key={path} href={path}>
              <button className={cn(
                "flex flex-col items-center space-y-1 py-2 px-3 transition-colors",
                isActive ? "text-safety-blue" : "text-gray-500"
              )}>
                <Icon className="w-5 h-5" />
                <span className="text-xs font-medium">{label}</span>
                {label === 'Reports' && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-safety-red rounded-full"></div>
                )}
              </button>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}
