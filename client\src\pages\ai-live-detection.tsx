import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import {
  Activity,
  ArrowLeft,
  Brain,
  Camera,
  Maximize,
  Square,
  Volume2,
  VolumeX,
} from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { Link } from 'wouter';

export default function AILiveDetection() {
  const [isRecording, setIsRecording] = useState(false);
  const [detections, setDetections] = useState<any[]>([]);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [aiModules, setAiModules] = useState({
    ppe: { active: true, confidence: 94.2, detections: 0 },
    hazard: { active: true, confidence: 91.8, detections: 0 },
    fall: { active: true, confidence: 96.5, detections: 0 },
    zone: { active: true, confidence: 89.3, detections: 0 },
    vehicle: { active: true, confidence: 92.1, detections: 0 },
  });
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  useEffect(() => {
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, [stream]);

  useEffect(() => {
    // Simulate real-time AI processing updates
    if (isRecording) {
      const interval = setInterval(() => {
        setAiModules(prev => ({
          ppe: { ...prev.ppe, confidence: 90 + Math.random() * 8 },
          hazard: { ...prev.hazard, confidence: 88 + Math.random() * 8 },
          fall: { ...prev.fall, confidence: 94 + Math.random() * 6 },
          zone: { ...prev.zone, confidence: 86 + Math.random() * 8 },
          vehicle: { ...prev.vehicle, confidence: 89 + Math.random() * 8 },
        }));
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [isRecording]);

  const startCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment',
          width: { ideal: 1920 },
          height: { ideal: 1080 },
        },
      });

      setStream(mediaStream);
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
      setIsRecording(true);

      // Start enhanced AI detection simulation
      startEnhancedDetection();

      toast({
        title: '🤖 AI Detection Started',
        description: 'Neural networks are now analyzing the video feed in real-time',
      });
    } catch (error) {
      toast({
        title: 'Camera Error',
        description: 'Could not access camera. Please check permissions.',
        variant: 'destructive',
      });
    }
  };

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    setIsRecording(false);
    setDetections([]);

    toast({
      title: 'AI Detection Stopped',
      description: 'Live analysis has been stopped',
    });
  };

  const startEnhancedDetection = () => {
    // Simulate advanced AI detections with more variety
    const detectionTypes = [
      {
        type: 'PPE_VIOLATION',
        descriptions: ['Missing safety helmet', 'No safety vest detected', 'Missing safety gloves'],
        severity: 'HIGH',
        color: 'safety-red',
      },
      {
        type: 'PPE_COMPLIANT',
        descriptions: ['Safety vest detected', 'Helmet properly worn', 'Complete PPE compliance'],
        severity: 'LOW',
        color: 'safety-green',
      },
      {
        type: 'HAZARD',
        descriptions: ['Spill detected on floor', 'Obstacle in walkway', 'Unstable stack detected'],
        severity: 'MEDIUM',
        color: 'safety-orange',
      },
      {
        type: 'ZONE_VIOLATION',
        descriptions: ['Unauthorized access to restricted area', 'Person in danger zone'],
        severity: 'HIGH',
        color: 'safety-blue',
      },
    ];

    const generateDetection = () => {
      const type = detectionTypes[Math.floor(Math.random() * detectionTypes.length)];
      const description = type.descriptions[Math.floor(Math.random() * type.descriptions.length)];

      return {
        id: Date.now() + Math.random(),
        type: type.type,
        description,
        confidence: 0.75 + Math.random() * 0.25,
        bbox: {
          x: Math.random() * 300,
          y: Math.random() * 200,
          width: 60 + Math.random() * 80,
          height: 80 + Math.random() * 100,
        },
        severity: type.severity,
        color: type.color,
        timestamp: new Date().toLocaleTimeString(),
      };
    };

    // Initial detections
    setTimeout(() => {
      setDetections([generateDetection(), generateDetection()]);
    }, 2000);

    // Continuous detection updates
    const detectionInterval = setInterval(() => {
      if (Math.random() > 0.7) {
        // 30% chance of new detection
        const newDetection = generateDetection();
        setDetections(prev => [newDetection, ...prev.slice(0, 4)]); // Keep last 5

        // Update module detection counts
        setAiModules(prev => {
          const moduleKey = newDetection.type.toLowerCase().includes('ppe')
            ? 'ppe'
            : newDetection.type.toLowerCase().includes('hazard')
              ? 'hazard'
              : newDetection.type.toLowerCase().includes('zone')
                ? 'zone'
                : 'ppe';

          return {
            ...prev,
            [moduleKey]: {
              ...prev[moduleKey as keyof typeof prev],
              detections: prev[moduleKey as keyof typeof prev].detections + 1,
            },
          };
        });

        // Audio alert for high severity
        if (audioEnabled && newDetection.severity === 'HIGH') {
          playAlertSound();
        }
      }
    }, 5000);

    return () => clearInterval(detectionInterval);
  };

  const playAlertSound = () => {
    // Create audio context for alert sound
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);

    oscillator.start();
    oscillator.stop(audioContext.currentTime + 0.2);
  };

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      containerRef.current?.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
    setIsFullscreen(!isFullscreen);
  };

  const toggleAiModule = (_module: string) => {
    // setAiModules(prev => ({
    //   ...prev,
    //   [module]: {
    //     ...prev[module as keyof typeof prev],
    //     active: !prev[module as keyof typeof prev].active,
    //   },
    // }));
  };

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"
      ref={containerRef}
    >
      <div className="p-4">
        {/* AI Header */}
        <div className="glass-card mb-6 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button variant="ghost" size="sm" className="text-white hover:bg-white/20">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
              </Link>
              <div className="w-10 h-10 bg-gradient-ai rounded-full flex items-center justify-center animate-glow">
                <Brain className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">AI Live Detection</h1>
                <p className="text-sm text-gray-300">
                  Real-time neural network analysis •{' '}
                  {Object.values(aiModules).filter(m => m.active).length} modules active
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setAudioEnabled(!audioEnabled)}
                className="text-white hover:bg-white/20"
              >
                {audioEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleFullscreen}
                className="text-white hover:bg-white/20"
              >
                <Maximize className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Camera Feed */}
          <div className="lg:col-span-3">
            <Card className="glass-card border-0">
              <CardContent className="p-0">
                <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    muted
                    className="w-full h-full object-cover"
                  />

                  {/* AI Detection Overlays */}
                  {isRecording &&
                    detections.map((detection, index) => (
                      <div
                        key={detection.id}
                        className={`absolute border-2 border-${detection.color} bg-${detection.color}/20 rounded animate-pulse ${
                          index === 0
                            ? 'top-20 left-20 w-20 h-24'
                            : index === 1
                              ? 'top-32 left-40 w-24 h-28'
                              : index === 2
                                ? 'top-16 left-60 w-18 h-22'
                                : index === 3
                                  ? 'top-40 left-32 w-22 h-26'
                                  : 'top-24 left-48 w-20 h-24'
                        }`}
                      >
                        <div
                          className={`absolute -top-6 left-0 bg-${detection.color} text-white text-xs px-2 py-1 rounded`}
                        >
                          {detection.description} ({Math.round(detection.confidence * 100)}%)
                        </div>
                      </div>
                    ))}

                  {/* AI Processing Overlay */}
                  {isRecording && (
                    <div className="absolute top-4 left-4 glass-dark rounded-lg p-3">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-ai-accent rounded-full animate-pulse"></div>
                        <span className="text-white text-xs font-medium">AI PROCESSING</span>
                      </div>
                      <div className="text-white text-xs opacity-75 mt-1">
                        {detections.length} active detections
                      </div>
                    </div>
                  )}

                  {!isRecording && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-slate-800 to-purple-800">
                      <div className="text-center text-white">
                        <div className="w-20 h-20 bg-gradient-ai rounded-full flex items-center justify-center mx-auto mb-4 animate-glow">
                          <Camera className="w-10 h-10" />
                        </div>
                        <p className="text-xl mb-2">AI Vision Ready</p>
                        <p className="text-sm text-gray-300 mb-6">
                          Start camera to begin real-time safety analysis
                        </p>
                        <Button
                          onClick={startCamera}
                          className="bg-gradient-ai hover:opacity-90 text-white px-6 py-3"
                        >
                          <Camera className="w-5 h-5 mr-2" />
                          Activate AI Detection
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                {isRecording && (
                  <div className="p-4 glass-dark border-t border-white/20">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-6">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-safety-red rounded-full animate-pulse"></div>
                          <span className="text-sm font-medium text-white">LIVE AI ANALYSIS</span>
                        </div>
                        <div className="text-sm text-gray-300">
                          Processing: {Math.round(Math.random() * 30 + 120)}ms
                        </div>
                        <div className="text-sm text-gray-300">
                          Accuracy:{' '}
                          {Math.round(
                            Object.values(aiModules).reduce((acc, m) => acc + m.confidence, 0) /
                              Object.values(aiModules).length
                          )}
                          %
                        </div>
                      </div>
                      <Button
                        onClick={stopCamera}
                        variant="outline"
                        size="sm"
                        className="border-white/30 text-white hover:bg-white/20"
                      >
                        <Square className="w-4 h-4 mr-2" />
                        Stop Analysis
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* AI Control Panel */}
          <div className="space-y-4">
            {/* AI Modules Status */}
            <Card className="glass-card border-0">
              <CardContent className="p-4">
                <h3 className="font-semibold text-white mb-3 flex items-center">
                  <Brain className="w-4 h-4 mr-2 text-ai-neural" />
                  AI Modules
                </h3>
                <div className="space-y-3">
                  {Object.entries(aiModules).map(([key, module]) => (
                    <div key={key} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div
                          className={`w-2 h-2 rounded-full ${module.active ? 'bg-safety-green animate-pulse' : 'bg-gray-500'}`}
                        ></div>
                        <span className="text-sm text-white capitalize">{key}</span>
                      </div>
                      <div className="text-xs text-gray-300">{module.confidence.toFixed(1)}%</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Detections */}
            <Card className="glass-card border-0">
              <CardContent className="p-4">
                <h3 className="font-semibold text-white mb-3 flex items-center">
                  <Activity className="w-4 h-4 mr-2 text-ai-accent" />
                  Live Detections
                </h3>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {detections.length === 0 ? (
                    <p className="text-sm text-gray-400">No detections yet</p>
                  ) : (
                    detections.map(detection => (
                      <div
                        key={detection.id}
                        className="p-2 rounded bg-white/10 border-l-4 border-ai-accent"
                      >
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium text-white">
                            {detection.description}
                          </span>
                          <span className="text-xs text-gray-300">
                            {Math.round(detection.confidence * 100)}%
                          </span>
                        </div>
                        <div className="text-xs text-gray-400">{detection.timestamp}</div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
