import { useState } from 'react';
import { ArrowLeft, HardHat, Camera } from 'lucide-react';
import { Link } from 'wouter';
import MobileLayout from '@/components/mobile-layout';
import CameraOverlay from '@/components/camera-overlay';
import IncidentModal from '@/components/incident-modal';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const MOCK_USER_ID = "mock-user-123";

const PPE_ITEMS = [
  { name: 'Safety Helmet', icon: '⛑️', required: true, detected: false },
  { name: 'Safety Vest', icon: '🦺', required: true, detected: true },
  { name: 'Safety Gloves', icon: '🧤', required: true, detected: false },
  { name: 'Safety Goggles', icon: '🥽', required: false, detected: true },
  { name: 'Safety Boots', icon: '🥾', required: true, detected: true },
  { name: 'Face Mask', icon: '😷', required: false, detected: false },
];

export default function PPEDetection() {
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [isIncidentModalOpen, setIsIncidentModalOpen] = useState(false);
  const [capturedImageData, setCapturedImageData] = useState<string>();
  const [analysisData, setAnalysisData] = useState<any>();

  const handleCameraCapture = (imageData: string, analysis?: any) => {
    setCapturedImageData(imageData);
    setAnalysisData(analysis);
    setIsCameraOpen(false);
    setIsIncidentModalOpen(true);
  };

  const handleQuickReport = () => {
    setIsCameraOpen(false);
    setIsIncidentModalOpen(true);
  };

  const requiredItems = PPE_ITEMS.filter(item => item.required);
  const compliantItems = requiredItems.filter(item => item.detected);
  const complianceRate = Math.round((compliantItems.length / requiredItems.length) * 100);

  return (
    <>
      <MobileLayout
        title="PPE Detection"
        subtitle="Personal Protective Equipment Check"
      >
        <div className="px-4 py-6">
          <div className="mb-6">
            <Link href="/">
              <Button variant="ghost" size="sm" className="mb-4">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>

          {/* Compliance Overview */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="text-center">
                <div className={`w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 ${
                  complianceRate >= 100 ? 'bg-safety-green' :
                  complianceRate >= 80 ? 'bg-safety-orange' :
                  'bg-safety-red'
                }`}>
                  <span className="text-white text-2xl font-bold">{complianceRate}%</span>
                </div>
                <h2 className="text-xl font-semibold mb-2">PPE Compliance Rate</h2>
                <p className="text-gray-600">
                  {compliantItems.length} of {requiredItems.length} required items detected
                </p>
              </div>
            </CardContent>
          </Card>

          {/* PPE Status List */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <h3 className="font-semibold mb-4">PPE Status Check</h3>
              <div className="space-y-3">
                {PPE_ITEMS.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{item.icon}</span>
                      <div>
                        <p className="font-medium text-sm">{item.name}</p>
                        <p className="text-xs text-gray-600">
                          {item.required ? 'Required' : 'Optional'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge 
                        variant={item.detected ? "default" : "destructive"}
                        className={
                          item.detected 
                            ? "bg-safety-green hover:bg-green-600" 
                            : "bg-safety-red hover:bg-red-600"
                        }
                      >
                        {item.detected ? 'Detected' : 'Missing'}
                      </Badge>
                      {item.detected ? (
                        <span className="text-safety-green text-lg">✓</span>
                      ) : (
                        <span className="text-safety-red text-lg">✗</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button
              onClick={() => setIsCameraOpen(true)}
              className="w-full bg-safety-blue hover:bg-blue-600 py-4"
            >
              <Camera className="w-5 h-5 mr-2" />
              Start PPE Detection
            </Button>

            {complianceRate < 100 && (
              <Button
                onClick={() => setIsIncidentModalOpen(true)}
                variant="outline"
                className="w-full border-safety-red text-safety-red hover:bg-red-50 py-4"
              >
                <HardHat className="w-5 h-5 mr-2" />
                Report PPE Violation
              </Button>
            )}
          </div>

          {/* Safety Guidelines */}
          <Card className="mt-6">
            <CardContent className="p-4">
              <h3 className="font-semibold mb-3">PPE Guidelines</h3>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-start space-x-2">
                  <span className="text-safety-green text-lg">•</span>
                  <p>Always wear required PPE in designated areas</p>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-safety-green text-lg">•</span>
                  <p>Inspect PPE before each use for damage</p>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-safety-green text-lg">•</span>
                  <p>Replace damaged or expired equipment immediately</p>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-safety-green text-lg">•</span>
                  <p>Report missing or inadequate PPE to supervisors</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </MobileLayout>

      <CameraOverlay
        isOpen={isCameraOpen}
        onClose={() => setIsCameraOpen(false)}
        onCapture={handleCameraCapture}
        onQuickReport={handleQuickReport}
      />

      <IncidentModal
        isOpen={isIncidentModalOpen}
        onClose={() => setIsIncidentModalOpen(false)}
        imageData={capturedImageData}
        analysis={analysisData}
        userId={MOCK_USER_ID}
      />
    </>
  );
}
