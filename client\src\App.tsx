import AIDashboard from '@/components/ai-dashboard';
import { Toaster } from '@/components/ui/toaster';
import { TooltipProvider } from '@/components/ui/tooltip';
import AboutPage from '@/pages/about';
import AILiveDetection from '@/pages/ai-live-detection';
import BlogPage from '@/pages/blog';
import CareersPage from '@/pages/careers';
import ContactPage from '@/pages/contact';
import Dashboard from '@/pages/dashboard';
import Documents from '@/pages/documents';
import FeaturesPage from '@/pages/features';
import Incidents from '@/pages/incidents';
import LandingPage from '@/pages/landing';
import LiveDetection from '@/pages/live-detection';
import NotFound from '@/pages/not-found';
import PhotoCheck from '@/pages/photo-check';
import PPEDetection from '@/pages/ppe-detection';
import PricingPage from '@/pages/pricing';
import Reports from '@/pages/reports';
import Settings from '@/pages/settings';
import ToolboxTalk from '@/pages/toolbox-talk';
import VoiceReport from '@/pages/voice-report';
import { QueryClientProvider } from '@tanstack/react-query';
import { Route, Switch } from 'wouter';
import { queryClient } from './lib/queryClient';

function Router() {
  return (
    <Switch>
      <Route path="/" component={LandingPage} />
      <Route path="/dashboard" component={Dashboard} />
      <Route path="/ai-dashboard" component={AIDashboard} />
      <Route path="/live" component={LiveDetection} />
      <Route path="/ai-live" component={AILiveDetection} />
      <Route path="/photo-check" component={PhotoCheck} />
      <Route path="/ppe-detection" component={PPEDetection} />
      <Route path="/voice-report" component={VoiceReport} />
      <Route path="/incidents" component={Incidents} />
      <Route path="/reports" component={Reports} />
      <Route path="/settings" component={Settings} />
      <Route path="/toolbox-talk" component={ToolboxTalk} />
      <Route path="/documents" component={Documents} />
      <Route path="/pricing" component={PricingPage} />
      <Route path="/about" component={AboutPage} />
      <Route path="/contact" component={ContactPage} />
      <Route path="/blog" component={BlogPage} />
      <Route path="/features" component={FeaturesPage} />
      <Route path="/careers" component={CareersPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
