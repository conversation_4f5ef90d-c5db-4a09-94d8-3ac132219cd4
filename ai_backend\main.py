"""
AI-Powered Mobile Safety Assistant Backend
Main FastAPI application for AI processing and model inference
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from pydantic import BaseModel

from ai_models.model_manager import ModelManager
from ai_models.safety_detector import SafetyDetector
from ai_models.ppe_detector import PPEDetector
from ai_models.voice_processor import VoiceProcessor
from ai_models.zone_monitor import ZoneMonitor
from ai_models.fall_detector import FallDetector
from ai_models.hazard_detector import HazardDetector
from utils.image_processor import ImageProcessor
from utils.video_processor import VideoProcessor
from utils.logger import setup_logger

# Setup logging
logger = setup_logger(__name__)

# Global model manager instance
model_manager: Optional[ModelManager] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for model loading/cleanup"""
    global model_manager
    
    logger.info("Starting AI Safety Assistant Backend...")
    
    # Initialize model manager and load all models
    model_manager = ModelManager()
    await model_manager.initialize_models()
    
    logger.info("All AI models loaded successfully")
    
    yield
    
    # Cleanup
    if model_manager:
        await model_manager.cleanup()
    logger.info("AI Backend shutdown complete")

# FastAPI app initialization
app = FastAPI(
    title="AI Safety Assistant Backend",
    description="Advanced AI-powered safety detection and monitoring system",
    version="2.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for API
class DetectionRequest(BaseModel):
    image_data: str  # Base64 encoded image
    detection_types: List[str] = ["ppe", "hazard", "zone", "fall"]
    confidence_threshold: float = 0.5
    location: Optional[dict] = None

class DetectionResult(BaseModel):
    type: str
    confidence: float
    bounding_box: dict
    description: str
    severity: str
    recommendations: List[str]

class AnalysisResponse(BaseModel):
    detections: List[DetectionResult]
    risk_level: str
    overall_confidence: float
    processing_time: float
    recommendations: List[str]
    ai_insights: dict

class VoiceRequest(BaseModel):
    audio_data: str  # Base64 encoded audio
    transcript: Optional[str] = None
    language: str = "en"

class VoiceResponse(BaseModel):
    hazard_type: str
    severity: str
    location: Optional[str]
    structured_report: dict
    confidence: float

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "AI Safety Assistant Backend",
        "version": "2.0.0",
        "status": "operational",
        "models_loaded": model_manager.is_initialized() if model_manager else False
    }

@app.get("/health")
async def health_check():
    """Detailed health check"""
    if not model_manager or not model_manager.is_initialized():
        raise HTTPException(status_code=503, detail="Models not loaded")
    
    return {
        "status": "healthy",
        "models": await model_manager.get_model_status(),
        "memory_usage": await model_manager.get_memory_usage()
    }

@app.post("/api/v2/analyze-image", response_model=AnalysisResponse)
async def analyze_image(request: DetectionRequest, background_tasks: BackgroundTasks):
    """
    Advanced image analysis with multiple AI models
    """
    if not model_manager:
        raise HTTPException(status_code=503, detail="AI models not available")
    
    try:
        # Process image
        image = ImageProcessor.decode_base64_image(request.image_data)
        
        # Run parallel detection across all requested models
        detection_tasks = []
        
        if "ppe" in request.detection_types:
            detection_tasks.append(
                model_manager.ppe_detector.detect(image, request.confidence_threshold)
            )
        
        if "hazard" in request.detection_types:
            detection_tasks.append(
                model_manager.hazard_detector.detect(image, request.confidence_threshold)
            )
        
        if "zone" in request.detection_types:
            detection_tasks.append(
                model_manager.zone_monitor.detect(image, request.confidence_threshold)
            )
        
        if "fall" in request.detection_types:
            detection_tasks.append(
                model_manager.fall_detector.detect(image, request.confidence_threshold)
            )
        
        # Execute all detections in parallel
        import time
        start_time = time.time()
        
        detection_results = await asyncio.gather(*detection_tasks, return_exceptions=True)
        
        processing_time = time.time() - start_time
        
        # Combine and process results
        all_detections = []
        for result in detection_results:
            if isinstance(result, list):
                all_detections.extend(result)
        
        # Calculate risk assessment
        risk_level, overall_confidence = model_manager.safety_detector.calculate_risk_assessment(all_detections)
        
        # Generate AI insights and recommendations
        ai_insights = await model_manager.safety_detector.generate_insights(all_detections, image)
        recommendations = model_manager.safety_detector.generate_recommendations(all_detections)
        
        # Log analysis for continuous learning
        background_tasks.add_task(
            model_manager.log_analysis,
            request.image_data,
            all_detections,
            request.location
        )
        
        return AnalysisResponse(
            detections=all_detections,
            risk_level=risk_level,
            overall_confidence=overall_confidence,
            processing_time=processing_time,
            recommendations=recommendations,
            ai_insights=ai_insights
        )
        
    except Exception as e:
        logger.error(f"Image analysis error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/api/v2/process-voice", response_model=VoiceResponse)
async def process_voice(request: VoiceRequest):
    """
    Advanced voice processing with NLP classification
    """
    if not model_manager:
        raise HTTPException(status_code=503, detail="AI models not available")
    
    try:
        result = await model_manager.voice_processor.process_voice_input(
            request.audio_data,
            request.transcript,
            request.language
        )
        
        return VoiceResponse(**result)
        
    except Exception as e:
        logger.error(f"Voice processing error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Voice processing failed: {str(e)}")

@app.post("/api/v2/live-stream")
async def process_live_stream(file: UploadFile = File(...)):
    """
    Process live video stream for real-time detection
    """
    if not model_manager:
        raise HTTPException(status_code=503, detail="AI models not available")
    
    try:
        # Process video frame
        video_data = await file.read()
        frame = VideoProcessor.extract_frame(video_data)
        
        # Run real-time detection
        detections = await model_manager.safety_detector.process_live_frame(frame)
        
        return {"detections": detections, "timestamp": asyncio.get_event_loop().time()}
        
    except Exception as e:
        logger.error(f"Live stream processing error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Stream processing failed: {str(e)}")

@app.get("/api/v2/models/status")
async def get_model_status():
    """Get status of all AI models"""
    if not model_manager:
        raise HTTPException(status_code=503, detail="Model manager not available")
    
    return await model_manager.get_detailed_status()

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
