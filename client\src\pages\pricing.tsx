import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Building, Check, Crown, Factory, Shield, X, Zap } from 'lucide-react';
import { useState } from 'react';
import { Link } from 'wouter';

export default function PricingPage() {
  const [isAnnual, setIsAnnual] = useState(false);

  const plans = [
    {
      name: 'Starter',
      icon: Shield,
      description: 'Perfect for small teams getting started with AI safety',
      monthlyPrice: 99,
      annualPrice: 990,
      features: [
        'Up to 10 users',
        'Basic AI detection',
        'Photo safety checks',
        'Email support',
        'Mobile app access',
        'Basic reporting',
      ],
      limitations: ['No real-time monitoring', 'No voice reports', 'No predictive analytics'],
      popular: false,
      cta: 'Start Free Trial',
    },
    {
      name: 'Professional',
      icon: Zap,
      description: 'Advanced AI features for growing safety teams',
      monthlyPrice: 299,
      annualPrice: 2990,
      features: [
        'Up to 50 users',
        'Advanced AI detection',
        'Real-time monitoring',
        'Voice reporting',
        'Live camera feeds',
        'Advanced analytics',
        'Priority support',
        'Custom integrations',
        'Incident management',
      ],
      limitations: ['No predictive analytics', 'No custom AI models'],
      popular: true,
      cta: 'Start Free Trial',
    },
    {
      name: 'Enterprise',
      icon: Building,
      description: 'Complete AI safety solution for large organizations',
      monthlyPrice: 999,
      annualPrice: 9990,
      features: [
        'Unlimited users',
        'Full AI suite',
        'Predictive analytics',
        'Custom AI models',
        '24/7 monitoring',
        'Dedicated support',
        'On-premise deployment',
        'API access',
        'Custom training',
        'SLA guarantee',
      ],
      limitations: [],
      popular: false,
      cta: 'Contact Sales',
    },
    {
      name: 'Industrial',
      icon: Factory,
      description: 'Heavy industry solution with specialized AI models',
      monthlyPrice: 2499,
      annualPrice: 24990,
      features: [
        'Everything in Enterprise',
        'Industrial AI models',
        'Hazmat detection',
        'Heavy machinery monitoring',
        'Environmental sensors',
        'Compliance automation',
        'White-label solution',
        'Custom hardware',
        'On-site training',
        'Regulatory reporting',
      ],
      limitations: [],
      popular: false,
      cta: 'Contact Sales',
    },
  ];

  const addOns = [
    {
      name: 'Additional Users',
      price: '$15/user/month',
      description: 'Add more team members to your plan',
    },
    {
      name: 'Custom AI Training',
      price: '$5,000 one-time',
      description: 'Train AI models for your specific workplace',
    },
    {
      name: 'Hardware Package',
      price: '$2,500/camera',
      description: 'Professional-grade cameras and sensors',
    },
    {
      name: 'Professional Services',
      price: '$200/hour',
      description: 'Implementation and consulting services',
    },
  ];

  const faqs = [
    {
      question: "What's included in the free trial?",
      answer:
        'All plans include a 14-day free trial with full access to features. No credit card required.',
    },
    {
      question: 'Can I change plans anytime?',
      answer:
        'Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.',
    },
    {
      question: 'Do you offer custom pricing?',
      answer: 'Yes, we offer custom pricing for large enterprises and specialized requirements.',
    },
    {
      question: 'What kind of support do you provide?',
      answer:
        'We provide email support for all plans, priority support for Professional+, and dedicated support for Enterprise.',
    },
    {
      question: 'Is there a setup fee?',
      answer:
        'No setup fees for Starter and Professional plans. Enterprise and Industrial may include implementation services.',
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/">
              <Button variant="ghost" className="text-white hover:bg-white/10">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Home
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <Shield className="h-8 w-8 text-blue-400" />
              <span className="text-xl font-bold text-white">SafeGuard AI</span>
            </div>
            <Link href="/dashboard">
              <Button className="bg-blue-600 hover:bg-blue-700">Launch App</Button>
            </Link>
          </div>
        </div>
      </nav>

      {/* Header */}
      <section className="pt-32 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Choose Your
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              {' '}
              Safety Plan
            </span>
          </h1>
          <p className="text-xl text-white/80 mb-8">
            Flexible pricing for teams of all sizes. Start free, scale as you grow.
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center space-x-4 mb-12">
            <span className={`text-lg ${!isAnnual ? 'text-white' : 'text-white/60'}`}>Monthly</span>
            <button
              type="button"
              onClick={() => setIsAnnual(!isAnnual)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                isAnnual ? 'bg-blue-600' : 'bg-gray-600'
              }`}
              aria-label={`Switch to ${isAnnual ? 'monthly' : 'annual'} billing`}
              title={`Switch to ${isAnnual ? 'monthly' : 'annual'} billing`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isAnnual ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-lg ${isAnnual ? 'text-white' : 'text-white/60'}`}>
              Annual
              <Badge className="ml-2 bg-green-600">Save 17%</Badge>
            </span>
          </div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {plans.map((plan, index) => (
              <Card
                key={index}
                className={`relative bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300 ${
                  plan.popular ? 'ring-2 ring-blue-400 scale-105' : ''
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-600 text-white px-4 py-1">
                      <Crown className="w-3 h-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-4">
                  <plan.icon className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                  <CardTitle className="text-2xl text-white">{plan.name}</CardTitle>
                  <p className="text-white/70 text-sm">{plan.description}</p>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-white">
                      ${isAnnual ? Math.floor(plan.annualPrice / 12) : plan.monthlyPrice}
                    </span>
                    <span className="text-white/60">/month</span>
                    {isAnnual && (
                      <div className="text-sm text-green-400">
                        Billed annually (${plan.annualPrice})
                      </div>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-3">
                        <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                        <span className="text-white/80 text-sm">{feature}</span>
                      </div>
                    ))}
                    {plan.limitations.map((limitation, limitIndex) => (
                      <div key={limitIndex} className="flex items-center space-x-3">
                        <X className="h-4 w-4 text-red-400 flex-shrink-0" />
                        <span className="text-white/50 text-sm">{limitation}</span>
                      </div>
                    ))}
                  </div>

                  <Button
                    className={`w-full mt-6 ${
                      plan.popular
                        ? 'bg-blue-600 hover:bg-blue-700'
                        : 'bg-white/10 hover:bg-white/20 border border-white/20'
                    }`}
                  >
                    {plan.cta}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Add-ons */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Add-ons & Services</h2>
            <p className="text-white/80">Enhance your plan with additional features and services</p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {addOns.map((addon, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10">
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-lg font-semibold text-white">{addon.name}</h3>
                    <span className="text-blue-400 font-semibold">{addon.price}</span>
                  </div>
                  <p className="text-white/70 text-sm">{addon.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Frequently Asked Questions</h2>
          </div>

          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-white mb-3">{faq.question}</h3>
                  <p className="text-white/70">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">Ready to Get Started?</h2>
          <p className="text-xl text-white/90 mb-8">
            Join hundreds of companies already using SafeGuard AI to prevent workplace accidents.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/dashboard">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                Start Free Trial
              </Button>
            </Link>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white/10"
            >
              Contact Sales
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
