<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Safety Assistant - Mobile Safety Management Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'safety-blue': '#1565C0',
                        'safety-red': '#D32F2F',
                        'safety-orange': '#F57C00',
                        'safety-green': '#388E3C',
                        'surface': '#FAFAFA',
                    },
                    fontFamily: {
                        'roboto': ['Roboto', 'sans-serif'],
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body class="bg-surface font-roboto text-gray-900">
    <!-- @COMPONENT: MobileAppContainer -->
    <div class="max-w-md mx-auto bg-white shadow-2xl min-h-screen relative overflow-hidden">
        
        <!-- Status Bar -->
        <div class="bg-safety-blue text-white px-4 py-2 flex justify-between items-center text-sm">
            <div class="flex items-center space-x-2">
                <i class="fas fa-signal"></i>
                <span>SafetyNet 5G</span>
            </div>
            <div class="flex items-center space-x-2">
                <span>14:32</span>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- Header -->
        <header class="bg-white shadow-md px-4 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-safety-blue rounded-full flex items-center justify-center">
                        <i class="fas fa-hard-hat text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-bold text-gray-900">AI Safety Assistant</h1>
                        <p class="text-xs text-gray-600" data-bind="user.location">Building A - Level 2</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Live Status Indicator -->
                    <div class="flex items-center space-x-1" data-mock="true">
                        <div class="w-2 h-2 bg-safety-green rounded-full animate-pulse"></div>
                        <span class="text-xs text-safety-green font-medium">LIVE</span>
                    </div>
                    <button class="p-2 rounded-lg hover:bg-gray-100" data-event="click:toggleNotifications">
                        <i class="fas fa-bell text-gray-600"></i>
                        <span class="absolute -top-1 -right-1 w-3 h-3 bg-safety-red rounded-full text-xs"></span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Alert Banner (when active) -->
        <div class="bg-safety-red text-white px-4 py-3 flex items-center space-x-3" data-state="alertActive:boolean = false">
            <i class="fas fa-exclamation-triangle text-xl"></i>
            <div class="flex-1">
                <p class="font-semibold">HAZARD DETECTED</p>
                <p class="text-sm opacity-90" data-mock="true">PPE Violation - Missing Helmet</p>
            </div>
            <button class="text-white hover:bg-red-600 p-1 rounded">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Main Content -->
        <main class="pb-20">
            <!-- Quick Stats Dashboard -->
            <section class="px-4 py-6 bg-gradient-to-r from-safety-blue to-blue-600 text-white">
                <h2 class="text-lg font-semibold mb-4">Today's Safety Overview</h2>
                <div class="grid grid-cols-3 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold" data-bind="stats.inspections">24</div>
                        <div class="text-xs opacity-90">Inspections</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-safety-orange" data-bind="stats.alerts">3</div>
                        <div class="text-xs opacity-90">Alerts</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-safety-green" data-bind="stats.compliance">96%</div>
                        <div class="text-xs opacity-90">Compliance</div>
                    </div>
                </div>
            </section>

            <!-- AI Safety Modules -->
            <section class="px-4 py-6">
                <h3 class="text-lg font-semibold mb-4 text-gray-900">Safety Modules</h3>
                
                <!-- @MAP: safetyModules.map(module => ( -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <!-- Live Camera Detection -->
                    <div class="bg-white rounded-xl shadow-md p-4 border-2 border-safety-green" data-event="click:openLiveMode">
                        <div class="flex flex-col items-center text-center space-y-2">
                            <div class="w-12 h-12 bg-safety-green rounded-full flex items-center justify-center">
                                <i class="fas fa-video text-white text-xl"></i>
                            </div>
                            <h4 class="font-semibold text-sm">Live Detection</h4>
                            <div class="flex items-center space-x-1">
                                <div class="w-2 h-2 bg-safety-green rounded-full animate-pulse"></div>
                                <span class="text-xs text-safety-green">Active</span>
                            </div>
                        </div>
                    </div>

                    <!-- Photo Safety Check -->
                    <div class="bg-white rounded-xl shadow-md p-4 border border-gray-200" data-event="click:openPhotoCheck">
                        <div class="flex flex-col items-center text-center space-y-2">
                            <div class="w-12 h-12 bg-safety-blue rounded-full flex items-center justify-center">
                                <i class="fas fa-camera text-white text-xl"></i>
                            </div>
                            <h4 class="font-semibold text-sm">Photo Check</h4>
                            <span class="text-xs text-gray-600">AI Analysis</span>
                        </div>
                    </div>

                    <!-- PPE Detection -->
                    <div class="bg-white rounded-xl shadow-md p-4 border border-gray-200" data-event="click:openPPECheck">
                        <div class="flex flex-col items-center text-center space-y-2">
                            <div class="w-12 h-12 bg-safety-orange rounded-full flex items-center justify-center">
                                <i class="fas fa-hard-hat text-white text-xl"></i>
                            </div>
                            <h4 class="font-semibold text-sm">PPE Detection</h4>
                            <span class="text-xs text-gray-600">Equipment Check</span>
                        </div>
                    </div>

                    <!-- Voice Report -->
                    <div class="bg-white rounded-xl shadow-md p-4 border border-gray-200" data-event="click:openVoiceReport">
                        <div class="flex flex-col items-center text-center space-y-2">
                            <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-microphone text-white text-xl"></i>
                            </div>
                            <h4 class="font-semibold text-sm">Voice Report</h4>
                            <span class="text-xs text-gray-600">Talk2Report</span>
                        </div>
                    </div>
                </div>
                <!-- @END_MAP )) -->

                <!-- Recent Incidents -->
                <div class="bg-white rounded-xl shadow-md p-4 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-semibold text-gray-900">Recent Incidents</h4>
                        <button class="text-safety-blue text-sm font-medium" data-event="click:viewAllIncidents">View All</button>
                    </div>
                    
                    <!-- @MAP: recentIncidents.map(incident => ( -->
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3 p-3 bg-red-50 rounded-lg border-l-4 border-safety-red" data-mock="true">
                            <div class="w-8 h-8 bg-safety-red rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-exclamation text-white text-sm"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">PPE Violation Detected</p>
                                <p class="text-xs text-gray-600">Zone A-1 • 10:42 AM</p>
                            </div>
                            <span class="text-xs bg-safety-red text-white px-2 py-1 rounded-full">HIGH</span>
                        </div>

                        <div class="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg border-l-4 border-safety-orange" data-mock="true">
                            <div class="w-8 h-8 bg-safety-orange rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-tools text-white text-sm"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">Tool Misplacement</p>
                                <p class="text-xs text-gray-600">Zone B-3 • 09:15 AM</p>
                            </div>
                            <span class="text-xs bg-safety-orange text-white px-2 py-1 rounded-full">MED</span>
                        </div>

                        <div class="flex items-center space-x-3 p-3 bg-green-50 rounded-lg border-l-4 border-safety-green" data-mock="true">
                            <div class="w-8 h-8 bg-safety-green rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-check text-white text-sm"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">Safety Check Completed</p>
                                <p class="text-xs text-gray-600">Zone C-2 • 08:30 AM</p>
                            </div>
                            <span class="text-xs bg-safety-green text-white px-2 py-1 rounded-full">OK</span>
                        </div>
                    </div>
                    <!-- @END_MAP )) -->
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-xl shadow-md p-4">
                    <h4 class="font-semibold text-gray-900 mb-4">Quick Actions</h4>
                    <div class="grid grid-cols-2 gap-3">
                        <button class="flex items-center justify-center space-x-2 bg-safety-blue text-white py-3 px-4 rounded-lg font-medium" data-event="click:emergencyReport">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>Emergency</span>
                        </button>
                        <button class="flex items-center justify-center space-x-2 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium" data-event="click:exportReports">
                            <i class="fas fa-download"></i>
                            <span>Export</span>
                        </button>
                        <button class="flex items-center justify-center space-x-2 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium" data-event="click:toolboxTalk">
                            <i class="fas fa-users"></i>
                            <span>Toolbox Talk</span>
                        </button>
                        <button class="flex items-center justify-center space-x-2 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium" data-event="click:documents">
                            <i class="fas fa-file-alt"></i>
                            <span>Documents</span>
                        </button>
                    </div>
                </div>
            </section>
        </main>

        <!-- Camera Overlay (Hidden by default) -->
        <div class="fixed inset-0 bg-black z-50 hidden" id="cameraOverlay" data-state="cameraActive:boolean = false">
            <!-- Camera Feed Area -->
            <div class="relative w-full h-full">
                <!-- Mock Camera Feed -->
                <!-- Industrial workplace with safety equipment visible -->
                <div class="w-full h-full bg-gray-900 flex items-center justify-center" style="background-image: url('https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600'); background-size: cover; background-position: center;">
                    
                    <!-- AI Detection Overlays -->
                    <div class="absolute inset-0">
                        <!-- PPE Detection Box -->
                        <div class="absolute top-32 left-8 w-24 h-32 border-2 border-safety-green rounded" data-mock="true">
                            <div class="bg-safety-green text-white text-xs px-2 py-1 rounded-bl">
                                ✓ Helmet
                            </div>
                        </div>
                        
                        <!-- Warning Detection Box -->
                        <div class="absolute top-40 right-12 w-20 h-28 border-2 border-safety-red rounded" data-mock="true">
                            <div class="bg-safety-red text-white text-xs px-2 py-1 rounded-bl">
                                ⚠ No Gloves
                            </div>
                        </div>
                    </div>

                    <!-- Camera Controls Overlay -->
                    <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
                        <!-- Detection Status -->
                        <div class="flex items-center justify-center space-x-4 mb-6">
                            <div class="flex items-center space-x-2 bg-safety-green/90 px-3 py-2 rounded-full">
                                <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                                <span class="text-white text-sm font-medium">AI Analyzing</span>
                            </div>
                        </div>

                        <!-- Control Buttons -->
                        <div class="flex items-center justify-between">
                            <button class="bg-white/20 backdrop-blur-sm text-white p-4 rounded-full" data-event="click:closeLiveMode">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                            
                            <div class="flex space-x-4">
                                <button class="bg-safety-red text-white p-4 rounded-full shadow-lg" data-event="click:captureIncident">
                                    <i class="fas fa-camera text-xl"></i>
                                </button>
                                <button class="bg-white/20 backdrop-blur-sm text-white p-4 rounded-full" data-event="click:toggleRecording">
                                    <i class="fas fa-video text-xl"></i>
                                </button>
                            </div>

                            <button class="bg-white/20 backdrop-blur-sm text-white p-4 rounded-full" data-event="click:toggleFlash">
                                <i class="fas fa-bolt text-xl"></i>
                            </button>
                        </div>

                        <!-- Quick Report -->
                        <div class="mt-4 flex space-x-2">
                            <button class="flex-1 bg-safety-orange text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center space-x-2" data-event="click:quickReport">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Quick Report</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <nav class="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-white border-t border-gray-200 px-4 py-2">
            <div class="flex justify-around">
                <button class="flex flex-col items-center space-y-1 py-2 px-3 text-safety-blue" data-event="click:navigateHome">
                    <i class="fas fa-home text-lg"></i>
                    <span class="text-xs font-medium">Home</span>
                </button>
                <button class="flex flex-col items-center space-y-1 py-2 px-3 text-gray-500" data-event="click:navigateInspections">
                    <i class="fas fa-search text-lg"></i>
                    <span class="text-xs">Inspect</span>
                </button>
                <button class="flex flex-col items-center space-y-1 py-2 px-3 text-gray-500 relative" data-event="click:navigateReports">
                    <i class="fas fa-chart-bar text-lg"></i>
                    <span class="text-xs">Reports</span>
                    <div class="absolute -top-1 -right-1 w-3 h-3 bg-safety-red rounded-full"></div>
                </button>
                <button class="flex flex-col items-center space-y-1 py-2 px-3 text-gray-500" data-event="click:navigateSettings">
                    <i class="fas fa-cog text-lg"></i>
                    <span class="text-xs">Settings</span>
                </button>
            </div>
        </nav>

        <!-- Floating Emergency Button -->
        <button class="fixed bottom-24 right-6 w-14 h-14 bg-safety-red text-white rounded-full shadow-lg flex items-center justify-center z-40 animate-pulse" data-event="click:emergencyAlert">
            <i class="fas fa-exclamation text-xl"></i>
        </button>

    </div>
    <!-- @END_COMPONENT: MobileAppContainer -->

    <!-- Modal Templates (Hidden by default) -->
    
    <!-- Incident Report Modal -->
    <div class="fixed inset-0 bg-black/50 z-50 hidden" id="incidentModal" data-state="modalOpen:boolean = false">
        <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-white rounded-t-3xl p-6">
            <div class="w-8 h-1 bg-gray-300 rounded-full mx-auto mb-6"></div>
            
            <h3 class="text-xl font-bold text-gray-900 mb-4">Report Incident</h3>
            
            <!-- Incident Photo -->
            <div class="bg-gray-100 rounded-xl h-48 flex items-center justify-center mb-4" data-mock-image="true">
                <!-- Construction worker without proper safety equipment -->
                <img src="https://images.unsplash.com/photo-1572981779307-38b8cabb2407?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300" alt="Safety incident captured" class="w-full h-full object-cover rounded-xl">
                <div class="absolute top-2 right-2 bg-safety-red text-white px-2 py-1 rounded text-xs font-medium">
                    PPE VIOLATION
                </div>
            </div>

            <!-- AI Analysis Results -->
            <div class="bg-red-50 border border-safety-red rounded-lg p-4 mb-4">
                <h4 class="font-semibold text-safety-red mb-2">AI Detection Results:</h4>
                <ul class="space-y-1 text-sm" data-mock="true">
                    <li class="flex items-center space-x-2">
                        <i class="fas fa-times text-safety-red text-xs"></i>
                        <span>Missing safety helmet</span>
                    </li>
                    <li class="flex items-center space-x-2">
                        <i class="fas fa-times text-safety-red text-xs"></i>
                        <span>No protective gloves detected</span>
                    </li>
                    <li class="flex items-center space-x-2">
                        <i class="fas fa-check text-safety-green text-xs"></i>
                        <span>Safety vest present</span>
                    </li>
                </ul>
            </div>

            <!-- Report Details -->
            <div class="space-y-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Severity Level</label>
                    <select class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-safety-blue focus:border-transparent" data-implementation="Should update risk assessment based on selection">
                        <option>High Risk</option>
                        <option>Medium Risk</option>
                        <option>Low Risk</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Additional Notes</label>
                    <textarea rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-safety-blue focus:border-transparent" placeholder="Add any additional details..." data-implementation="Should save notes to incident record"></textarea>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex space-x-3">
                <button class="flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium" data-event="click:closeModal">
                    Cancel
                </button>
                <button class="flex-1 bg-safety-red text-white py-3 px-4 rounded-lg font-medium" data-event="click:submitIncident" data-implementation="Should validate form and submit to backend">
                    Submit Report
                </button>
            </div>
        </div>
    </div>

    <script>
        // TODO: Implement real camera access, AI processing, and backend integration
        // TODO: Add WebRTC for real-time video streaming
        // TODO: Integrate TensorFlow.js for client-side AI processing
        // TODO: Implement Web Speech API for voice recognition
        // TODO: Add geolocation tracking and offline sync capabilities
        
        (function() {
            // Mock functionality for demonstration
            
            // Toggle camera overlay
            function toggleLiveMode() {
                const overlay = document.getElementById('cameraOverlay');
                overlay.classList.toggle('hidden');
                // TODO: Start actual camera feed and AI processing
            }

            // Mock incident reporting
            function showIncidentModal() {
                const modal = document.getElementById('incidentModal');
                modal.classList.remove('hidden');
                // TODO: Process captured image through AI models
            }

            // Close modals and overlays
            function closeModal() {
                document.querySelectorAll('.fixed.z-50').forEach(el => {
                    el.classList.add('hidden');
                });
            }

            // Emergency alert function
            function triggerEmergencyAlert() {
                // TODO: Send immediate alerts via SMS, WhatsApp, Email
                // TODO: Log GPS coordinates and timestamp
                alert('Emergency alert sent to safety team!');
            }

            // Mock voice recording
            function startVoiceReport() {
                // TODO: Implement Web Speech API
                // TODO: Process voice input through NLP for hazard classification
                console.log('Voice reporting started...');
            }

            // Export functionality
            function exportReports() {
                // TODO: Generate CSV/JSON/PDF reports
                // TODO: Implement data filtering and date range selection
                console.log('Exporting safety reports...');
            }

            // Basic event listeners for demo
            document.addEventListener('click', function(e) {
                const target = e.target.closest('[data-event]');
                if (!target) return;

                const eventData = target.getAttribute('data-event');
                const [event, handler] = eventData.split(':');

                if (event === 'click') {
                    switch(handler) {
                        case 'openLiveMode':
                            toggleLiveMode();
                            break;
                        case 'closeLiveMode':
                            closeModal();
                            break;
                        case 'captureIncident':
                            showIncidentModal();
                            break;
                        case 'closeModal':
                            closeModal();
                            break;
                        case 'emergencyAlert':
                        case 'emergencyReport':
                            triggerEmergencyAlert();
                            break;
                        case 'openVoiceReport':
                            startVoiceReport();
                            break;
                        case 'exportReports':
                            exportReports();
                            break;
                        default:
                            console.log(`Handler ${handler} not implemented yet`);
                    }
                }
            });

            // Mock real-time updates
            setInterval(function() {
                // TODO: Poll for new incidents and alerts
                // TODO: Update dashboard statistics
                // TODO: Sync offline data when connection is restored
            }, 5000);

        })();
    </script>
</body>
</html>