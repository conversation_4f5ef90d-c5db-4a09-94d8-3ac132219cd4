"""
Voice Processing Model for Talk2Report
Advanced speech recognition and NLP for hazard classification
"""

import asyncio
import logging
import time
import base64
import io
from typing import Dict, List, Optional, Any
from pathlib import Path
import numpy as np

# Audio processing
import librosa
import soundfile as sf
from pydub import AudioSegment

# Speech recognition
import speech_recognition as sr

# NLP
from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
import spacy
import nltk
from nltk.sentiment import SentimentIntensityAnalyzer

logger = logging.getLogger(__name__)

class VoiceProcessor:
    """
    Advanced voice processing for safety incident reporting
    """
    
    def __init__(self, model_path: Path):
        self.model_path = model_path
        self.model_path.mkdir(exist_ok=True)
        
        # Speech recognition
        self.recognizer = sr.Recognizer()
        
        # NLP models
        self.nlp_model = None
        self.tokenizer = None
        self.classifier = None
        self.sentiment_analyzer = None
        
        # Hazard classification categories
        self.hazard_categories = {
            "spill": ["spill", "leak", "liquid", "oil", "water", "chemical"],
            "fall": ["fall", "fell", "trip", "slip", "stumble", "collapse"],
            "fire": ["fire", "smoke", "burn", "flame", "heat", "explosion"],
            "electrical": ["electrical", "shock", "wire", "cable", "power", "voltage"],
            "chemical": ["chemical", "gas", "fume", "toxic", "acid", "base"],
            "mechanical": ["machine", "equipment", "broken", "malfunction", "jam"],
            "structural": ["crack", "damage", "collapse", "unstable", "break"],
            "ppe": ["helmet", "gloves", "vest", "goggles", "mask", "protection"],
            "vehicle": ["forklift", "truck", "vehicle", "collision", "hit"],
            "environmental": ["noise", "dust", "temperature", "ventilation", "air"]
        }
        
        # Urgency keywords
        self.urgency_keywords = {
            "high": ["emergency", "urgent", "immediate", "critical", "danger", "injury", "hurt", "bleeding"],
            "medium": ["concern", "issue", "problem", "unsafe", "risk", "hazard"],
            "low": ["notice", "observe", "minor", "small", "suggestion"]
        }
        
        self.performance_metrics = {
            "total_processed": 0,
            "avg_processing_time": 0.0,
            "accuracy_score": 0.0
        }
    
    async def load_model(self):
        """Load voice processing models"""
        try:
            logger.info("Loading voice processing models...")
            
            # Load NLP model for text classification
            await self._load_nlp_models()
            
            # Download required NLTK data
            await self._setup_nltk()
            
            # Load spaCy model
            await self._load_spacy_model()
            
            logger.info("Voice processor loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load voice models: {e}")
            raise
    
    async def _load_nlp_models(self):
        """Load NLP models for text classification"""
        try:
            # Load pre-trained classification model
            model_name = "distilbert-base-uncased-finetuned-sst-2-english"
            self.classifier = pipeline("text-classification", model=model_name)
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            
        except Exception as e:
            logger.warning(f"Could not load advanced NLP models: {e}")
            # Fallback to basic classification
            self.classifier = None
    
    async def _setup_nltk(self):
        """Setup NLTK components"""
        try:
            nltk.download('vader_lexicon', quiet=True)
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
            
            self.sentiment_analyzer = SentimentIntensityAnalyzer()
            
        except Exception as e:
            logger.warning(f"NLTK setup failed: {e}")
    
    async def _load_spacy_model(self):
        """Load spaCy model for NER"""
        try:
            self.nlp_model = spacy.load("en_core_web_sm")
        except OSError:
            logger.warning("spaCy model not found, using basic processing")
            self.nlp_model = None
    
    async def process_voice_input(self, audio_data: str, transcript: Optional[str] = None, 
                                language: str = "en") -> Dict[str, Any]:
        """
        Process voice input and classify safety hazards
        
        Args:
            audio_data: Base64 encoded audio data
            transcript: Optional pre-transcribed text
            language: Language code
            
        Returns:
            Structured hazard classification result
        """
        start_time = time.time()
        
        try:
            # Step 1: Speech to text (if transcript not provided)
            if not transcript:
                transcript = await self._speech_to_text(audio_data, language)
            
            if not transcript:
                raise ValueError("Could not transcribe audio")
            
            # Step 2: Text preprocessing
            cleaned_text = self._preprocess_text(transcript)
            
            # Step 3: Hazard classification
            hazard_type = await self._classify_hazard(cleaned_text)
            
            # Step 4: Severity assessment
            severity = await self._assess_severity(cleaned_text)
            
            # Step 5: Location extraction
            location = await self._extract_location(cleaned_text)
            
            # Step 6: Generate structured report
            structured_report = await self._generate_structured_report(
                cleaned_text, hazard_type, severity, location
            )
            
            # Step 7: Calculate confidence score
            confidence = await self._calculate_confidence(cleaned_text, hazard_type)
            
            # Update performance metrics
            processing_time = time.time() - start_time
            self.performance_metrics["total_processed"] += 1
            self.performance_metrics["avg_processing_time"] = (
                (self.performance_metrics["avg_processing_time"] + processing_time) / 2
            )
            
            return {
                "hazard_type": hazard_type,
                "severity": severity,
                "location": location,
                "structured_report": structured_report,
                "confidence": confidence,
                "original_transcript": transcript,
                "processing_time": processing_time
            }
            
        except Exception as e:
            logger.error(f"Voice processing error: {e}")
            return {
                "hazard_type": "unknown",
                "severity": "MEDIUM",
                "location": None,
                "structured_report": {"error": str(e)},
                "confidence": 0.0,
                "original_transcript": transcript or "",
                "processing_time": time.time() - start_time
            }
    
    async def _speech_to_text(self, audio_data: str, language: str) -> str:
        """Convert speech to text"""
        try:
            # Decode base64 audio data
            audio_bytes = base64.b64decode(audio_data)
            
            # Convert to AudioSegment for processing
            audio_segment = AudioSegment.from_file(io.BytesIO(audio_bytes))
            
            # Convert to WAV format for speech recognition
            wav_io = io.BytesIO()
            audio_segment.export(wav_io, format="wav")
            wav_io.seek(0)
            
            # Use speech recognition
            with sr.AudioFile(wav_io) as source:
                audio = self.recognizer.record(source)
                
            # Try multiple recognition engines
            transcript = None
            
            # Try Google Speech Recognition
            try:
                transcript = self.recognizer.recognize_google(audio, language=language)
            except sr.UnknownValueError:
                logger.warning("Google Speech Recognition could not understand audio")
            except sr.RequestError as e:
                logger.warning(f"Google Speech Recognition error: {e}")
            
            # Fallback to offline recognition
            if not transcript:
                try:
                    transcript = self.recognizer.recognize_sphinx(audio)
                except sr.UnknownValueError:
                    logger.warning("Sphinx could not understand audio")
                except sr.RequestError as e:
                    logger.warning(f"Sphinx error: {e}")
            
            return transcript or ""
            
        except Exception as e:
            logger.error(f"Speech to text error: {e}")
            return ""
    
    def _preprocess_text(self, text: str) -> str:
        """Preprocess text for better classification"""
        # Convert to lowercase
        text = text.lower()
        
        # Remove extra whitespace
        text = " ".join(text.split())
        
        # Basic cleaning
        import re
        text = re.sub(r'[^\w\s]', ' ', text)
        
        return text
    
    async def _classify_hazard(self, text: str) -> str:
        """Classify the type of hazard from text"""
        # Count keyword matches for each category
        category_scores = {}
        
        for category, keywords in self.hazard_categories.items():
            score = sum(1 for keyword in keywords if keyword in text)
            if score > 0:
                category_scores[category] = score
        
        # Return category with highest score
        if category_scores:
            return max(category_scores, key=category_scores.get)
        
        # Use advanced NLP if available
        if self.classifier:
            try:
                # This would be replaced with a custom safety classifier
                result = self.classifier(text)
                # Map generic sentiment to hazard categories
                if result[0]['label'] == 'NEGATIVE' and result[0]['score'] > 0.8:
                    return "general_hazard"
            except Exception as e:
                logger.warning(f"NLP classification failed: {e}")
        
        return "general_concern"
    
    async def _assess_severity(self, text: str) -> str:
        """Assess severity level from text"""
        # Check for urgency keywords
        for level, keywords in self.urgency_keywords.items():
            if any(keyword in text for keyword in keywords):
                return level.upper()
        
        # Use sentiment analysis if available
        if self.sentiment_analyzer:
            try:
                scores = self.sentiment_analyzer.polarity_scores(text)
                compound_score = scores['compound']
                
                if compound_score <= -0.5:
                    return "HIGH"
                elif compound_score <= -0.1:
                    return "MEDIUM"
                else:
                    return "LOW"
            except Exception as e:
                logger.warning(f"Sentiment analysis failed: {e}")
        
        return "MEDIUM"
    
    async def _extract_location(self, text: str) -> Optional[str]:
        """Extract location information from text"""
        # Common location keywords
        location_keywords = [
            "room", "floor", "building", "area", "zone", "section",
            "warehouse", "office", "lab", "factory", "shop", "yard"
        ]
        
        # Use spaCy for named entity recognition if available
        if self.nlp_model:
            try:
                doc = self.nlp_model(text)
                
                # Look for location entities
                for ent in doc.ents:
                    if ent.label_ in ["GPE", "LOC", "FAC"]:  # Geopolitical, location, facility
                        return ent.text
                
                # Look for location patterns
                for token in doc:
                    if token.text.lower() in location_keywords:
                        # Try to get the full location phrase
                        location_phrase = []
                        for i in range(max(0, token.i - 2), min(len(doc), token.i + 3)):
                            if doc[i].pos_ in ["NOUN", "PROPN", "NUM"]:
                                location_phrase.append(doc[i].text)
                        
                        if location_phrase:
                            return " ".join(location_phrase)
                
            except Exception as e:
                logger.warning(f"NER location extraction failed: {e}")
        
        # Fallback to simple keyword matching
        words = text.split()
        for i, word in enumerate(words):
            if word in location_keywords:
                # Get surrounding context
                start = max(0, i - 2)
                end = min(len(words), i + 3)
                return " ".join(words[start:end])
        
        return None
    
    async def _generate_structured_report(self, text: str, hazard_type: str, 
                                        severity: str, location: Optional[str]) -> Dict[str, Any]:
        """Generate structured incident report"""
        # Extract action items based on hazard type
        action_items = self._get_action_items(hazard_type, severity)
        
        # Generate summary
        summary = f"{hazard_type.replace('_', ' ').title()} reported"
        if location:
            summary += f" at {location}"
        
        return {
            "type": hazard_type,
            "urgency": severity,
            "summary": summary,
            "action_items": action_items,
            "requires_immediate_attention": severity == "HIGH",
            "estimated_response_time": self._get_response_time(severity),
            "safety_protocols": self._get_safety_protocols(hazard_type)
        }
    
    def _get_action_items(self, hazard_type: str, severity: str) -> List[str]:
        """Get action items based on hazard type and severity"""
        base_actions = {
            "spill": ["Secure area", "Clean spill", "Identify source"],
            "fall": ["Check for injuries", "Secure area", "Investigate cause"],
            "fire": ["Evacuate area", "Contact fire department", "Use extinguisher if safe"],
            "electrical": ["De-energize circuit", "Contact electrician", "Secure area"],
            "chemical": ["Evacuate area", "Contact hazmat team", "Ensure ventilation"],
            "mechanical": ["Stop equipment", "Tag out", "Schedule repair"],
            "structural": ["Evacuate area", "Contact engineer", "Secure perimeter"],
            "ppe": ["Provide proper PPE", "Conduct training", "Enforce compliance"],
            "vehicle": ["Stop vehicle", "Check for injuries", "Investigate"],
            "environmental": ["Monitor conditions", "Adjust controls", "Notify management"]
        }
        
        actions = base_actions.get(hazard_type, ["Investigate incident", "Take appropriate action"])
        
        if severity == "HIGH":
            actions.insert(0, "IMMEDIATE ACTION REQUIRED")
            actions.append("Notify emergency services if needed")
        
        return actions
    
    def _get_response_time(self, severity: str) -> str:
        """Get estimated response time based on severity"""
        response_times = {
            "HIGH": "Immediate (0-15 minutes)",
            "MEDIUM": "Priority (15-60 minutes)",
            "LOW": "Standard (1-4 hours)"
        }
        return response_times.get(severity, "Standard (1-4 hours)")
    
    def _get_safety_protocols(self, hazard_type: str) -> List[str]:
        """Get relevant safety protocols"""
        protocols = {
            "spill": ["Spill Response Protocol", "Chemical Handling Procedures"],
            "fall": ["Fall Protection Protocol", "Incident Response Procedures"],
            "fire": ["Fire Emergency Protocol", "Evacuation Procedures"],
            "electrical": ["Electrical Safety Protocol", "Lockout/Tagout Procedures"],
            "chemical": ["Chemical Emergency Protocol", "Hazmat Response Procedures"],
            "mechanical": ["Machine Safety Protocol", "Maintenance Procedures"],
            "structural": ["Structural Safety Protocol", "Emergency Evacuation"],
            "ppe": ["PPE Requirements", "Safety Training Protocols"],
            "vehicle": ["Vehicle Safety Protocol", "Traffic Management"],
            "environmental": ["Environmental Monitoring", "Exposure Control"]
        }
        return protocols.get(hazard_type, ["General Safety Protocols"])
    
    async def _calculate_confidence(self, text: str, hazard_type: str) -> float:
        """Calculate confidence score for the classification"""
        # Base confidence on keyword matches
        keywords = self.hazard_categories.get(hazard_type, [])
        matches = sum(1 for keyword in keywords if keyword in text)
        
        if not keywords:
            return 0.5
        
        keyword_confidence = min(matches / len(keywords), 1.0)
        
        # Adjust based on text length and clarity
        text_length_factor = min(len(text.split()) / 10, 1.0)
        
        # Combine factors
        confidence = (keyword_confidence * 0.7 + text_length_factor * 0.3)
        
        return round(confidence, 2)
    
    async def get_performance_metrics(self) -> Dict[str, float]:
        """Get performance metrics"""
        return self.performance_metrics.copy()
    
    async def get_memory_usage(self) -> float:
        """Get memory usage"""
        # Estimate memory usage of loaded models
        memory_usage = 0.0
        
        if self.classifier:
            memory_usage += 100.0  # Approximate MB for transformer model
        
        if self.nlp_model:
            memory_usage += 50.0   # Approximate MB for spaCy model
        
        return memory_usage
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.classifier:
            del self.classifier
        
        if self.nlp_model:
            del self.nlp_model
        
        logger.info("Voice processor cleanup complete")
