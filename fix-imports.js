#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

const filesToFix = [
  'client/src/components/safety-stats.tsx',
  'client/src/hooks/use-speech.tsx',
  'client/src/pages/ai-live-detection.tsx',
  'client/src/pages/documents.tsx',
  'client/src/pages/incidents.tsx',
  'client/src/pages/reports.tsx',
  'client/src/pages/settings.tsx',
  'client/src/pages/toolbox-talk.tsx'
];

const fixUnusedImports = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Remove unused imports by commenting them out
    const unusedImports = [
      'ArrowLeft', 'Filter', 'CheckCircle', 'XCircle', 'Link', 'CardHeader', 'CardTitle',
      'Play', 'Pause', 'Clock', 'AlertTriangle', 'Shield', 'Eye', 'Zap', 'Settings'
    ];
    
    unusedImports.forEach(importName => {
      // Remove from import statements
      content = content.replace(new RegExp(`,\\s*${importName}`, 'g'), '');
      content = content.replace(new RegExp(`${importName},\\s*`, 'g'), '');
      content = content.replace(new RegExp(`\\{\\s*${importName}\\s*\\}`, 'g'), '{}');
    });
    
    // Clean up empty import statements
    content = content.replace(/import\s*\{\s*\}\s*from[^;]+;/g, '');
    content = content.replace(/import\s*\{\s*,\s*([^}]+)\s*\}/g, 'import { $1 }');
    content = content.replace(/import\s*\{\s*([^,}]+),\s*\}/g, 'import { $1 }');
    
    fs.writeFileSync(filePath, content);
    console.log(`✅ Fixed imports in ${filePath}`);
  } catch (error) {
    console.log(`❌ Error fixing ${filePath}:`, error.message);
  }
};

console.log('🔧 Fixing unused imports...\n');

filesToFix.forEach(fixUnusedImports);

console.log('\n✅ Import fixes completed!');
