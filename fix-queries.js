#!/usr/bin/env node

import fs from 'fs';

const files = [
  'src/pages/documents.tsx',
  'src/pages/incidents.tsx', 
  'src/pages/reports.tsx',
  'src/pages/toolbox-talk.tsx'
];

files.forEach(file => {
  try {
    let content = fs.readFileSync(file, 'utf8');
    
    // Fix queryParams to be part of queryKey
    content = content.replace(
      /queryKey:\s*\[([^\]]+)\],\s*queryParams:\s*\{([^}]+)\}/g,
      'queryKey: [$1, $2]'
    );
    
    // Fix type issues with unknown data
    content = content.replace(/(\w+)\.length/g, '($1 as any[]).length');
    content = content.replace(/(\w+)\.map\(/g, '($1 as any[]).map(');
    content = content.replace(/(\w+)\.filter\(/g, '($1 as any[]).filter(');
    content = content.replace(/(\w+)\.reduce\(/g, '($1 as any[]).reduce(');
    content = content.replace(/(\w+)\.slice\(/g, '($1 as any[]).slice(');
    
    fs.writeFileSync(file, content);
    console.log(`✅ Fixed ${file}`);
  } catch (error) {
    console.log(`❌ Error fixing ${file}:`, error.message);
  }
});

console.log('✅ Query fixes completed!');
