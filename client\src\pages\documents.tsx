import MobileLayout from '@/components/mobile-layout';
import Navigation from '@/components/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AlertTriangle, Clock, Download, Eye, FileText, Search, Upload } from 'lucide-react';
import { useState } from 'react';

const MOCK_USER_ID = 'mock-user-123';

const DOCUMENT_TYPES = [
  { value: 'SOP', label: 'Standard Operating Procedure' },
  { value: 'MSDS', label: 'Material Safety Data Sheet' },
  { value: 'PERMIT', label: 'Work Permit' },
  { value: 'CHECKLIST', label: 'Safety Checklist' },
  { value: 'TRAINING', label: 'Training Material' },
  { value: 'POLICY', label: 'Safety Policy' },
  { value: 'EMERGENCY', label: 'Emergency Procedure' },
];

export default function Documents() {
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<any>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Upload form state
  const [fileName, setFileName] = useState('');
  const [documentType, setDocumentType] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: documents = [], isLoading } = useQuery({
    queryKey: ['/api/documents', MOCK_USER_ID],
  });

  const uploadDocumentMutation = useMutation({
    mutationFn: async (data: any) => {
      return await apiRequest('POST', '/api/documents', data);
    },
    onSuccess: () => {
      toast({
        title: 'Document Uploaded',
        description: 'Your safety document has been uploaded successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/documents'] });
      setIsUploadModalOpen(false);
      resetUploadForm();
    },
    onError: () => {
      toast({
        title: 'Upload Failed',
        description: 'Failed to upload document. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const updateDocumentStatusMutation = useMutation({
    mutationFn: async ({ id, status }: { id: string; status: string }) => {
      return await apiRequest('PATCH', `/api/documents/${id}/status`, { status });
    },
    onSuccess: () => {
      toast({
        title: 'Status Updated',
        description: 'Document status has been updated successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/documents'] });
      setIsDetailModalOpen(false);
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to update document status.',
        variant: 'destructive',
      });
    },
  });

  const resetUploadForm = () => {
    setFileName('');
    setDocumentType('');
    setSelectedFile(null);
    setUploadProgress(0);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setFileName(file.name);
    }
  };

  const handleUploadDocument = () => {
    if (!selectedFile || !documentType) {
      toast({
        title: 'Missing Information',
        description: 'Please select a file and document type.',
        variant: 'destructive',
      });
      return;
    }

    // Simulate file upload progress
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + 10;
      });
    }, 200);

    const documentData = {
      userId: MOCK_USER_ID,
      name: fileName,
      type: documentType,
      fileUrl: `uploaded/${fileName}`, // In real app, this would be actual file URL
      status: 'active',
      tags: [documentType.toLowerCase()],
    };

    setTimeout(() => {
      uploadDocumentMutation.mutate(documentData);
      clearInterval(progressInterval);
    }, 2000);
  };

  const filteredDocuments = (documents as any[]).filter((doc: any) => {
    const matchesSearch =
      doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.type.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType = typeFilter === 'all' || doc.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || doc.status === statusFilter;

    return matchesSearch && matchesType && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-safety-green text-white hover:bg-green-600';
      case 'expired':
        return 'bg-safety-red text-white hover:bg-red-600';
      case 'pending':
        return 'bg-safety-orange text-white hover:bg-orange-600';
      default:
        return 'bg-gray-500 text-white hover:bg-gray-600';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'SOP':
        return '📋';
      case 'MSDS':
        return '⚗️';
      case 'PERMIT':
        return '📜';
      case 'CHECKLIST':
        return '✅';
      case 'TRAINING':
        return '🎓';
      case 'POLICY':
        return '📖';
      case 'EMERGENCY':
        return '🚨';
      default:
        return '📄';
    }
  };

  const handleViewDocument = (document: any) => {
    setSelectedDocument(document);
    setIsDetailModalOpen(true);
  };

  const handleStatusChange = (status: string) => {
    if (selectedDocument) {
      updateDocumentStatusMutation.mutate({ id: selectedDocument.id, status });
    }
  };

  // Gap analysis - check for missing critical documents
  const criticalDocuments = ['SOP', 'MSDS', 'EMERGENCY'];
  const availableTypes = (documents as any[]).map((doc: any) => doc.type);
  const missingCritical = (criticalDocuments as any[]).filter(
    type => !availableTypes.includes(type)
  );

  return (
    <>
      <MobileLayout title="Safety Documents" subtitle="Document management and compliance">
        <div className="px-4 py-6">
          {/* Gap Analysis Alert */}
          {(missingCritical as any[]).length > 0 && (
            <Card className="mb-6 border-safety-orange bg-orange-50">
              <CardContent className="p-4">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="w-5 h-5 text-safety-orange mt-0.5" />
                  <div>
                    <h3 className="font-semibold text-safety-orange mb-1">Document Gap Analysis</h3>
                    <p className="text-sm text-gray-700 mb-2">Missing critical safety documents:</p>
                    <div className="flex flex-wrap gap-2">
                      {(missingCritical as any[]).map(type => (
                        <Badge
                          key={type}
                          variant="outline"
                          className="border-safety-orange text-safety-orange"
                        >
                          {DOCUMENT_TYPES.find(dt => dt.value === type)?.label || type}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Upload Button */}
          <div className="mb-6">
            <Button
              onClick={() => setIsUploadModalOpen(true)}
              className="w-full bg-safety-blue hover:bg-blue-600 py-4"
            >
              <Upload className="w-5 h-5 mr-2" />
              Upload Document
            </Button>
          </div>

          {/* Search and Filters */}
          <div className="space-y-4 mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                placeholder="Search documents..."
                className="pl-10"
              />
            </div>

            <div className="grid grid-cols-2 gap-3">
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Document Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {(DOCUMENT_TYPES as any[]).map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Documents List */}
          {isLoading ? (
            <div className="space-y-4">
              {[1, 2, 3].map(i => (
                <Card key={i}>
                  <CardContent className="p-4">
                    <div className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (filteredDocuments as any[]).length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Documents Found</h3>
                <p className="text-gray-600 mb-4">
                  {searchQuery || typeFilter !== 'all' || statusFilter !== 'all'
                    ? 'No documents match your current filters.'
                    : 'Start by uploading your first safety document.'}
                </p>
                <Button
                  onClick={() => setIsUploadModalOpen(true)}
                  className="bg-safety-blue hover:bg-blue-600"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Document
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {(filteredDocuments as any[]).map((document: any) => (
                <Card key={document.id}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-start space-x-3 flex-1">
                        <span className="text-2xl">{getTypeIcon(document.type)}</span>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-gray-900 text-sm leading-tight">
                            {document.name}
                          </h3>
                          <p className="text-xs text-gray-600 mt-1">
                            {DOCUMENT_TYPES.find(dt => dt.value === document.type)?.label ||
                              document.type}
                          </p>
                          {document.expiryDate && (
                            <p className="text-xs text-safety-orange mt-1">
                              Expires: {new Date(document.expiryDate).toLocaleDateString()}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex flex-col space-y-2">
                        <Badge className={getStatusColor(document.status)}>
                          {document.status?.toUpperCase()}
                        </Badge>
                        <Button
                          onClick={() => handleViewDocument(document)}
                          variant="ghost"
                          size="sm"
                          className="p-1"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>
                        Uploaded:{' '}
                        {new Date(document.createdAt).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric',
                        })}
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-safety-blue hover:text-blue-600 p-0"
                      >
                        <Download className="w-3 h-3 mr-1" />
                        Download
                      </Button>
                    </div>

                    {document.tags && (document.tags as any[]).length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {(document.tags as any[]).map((tag: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </MobileLayout>

      <Navigation />

      {/* Upload Document Modal */}
      <Dialog open={isUploadModalOpen} onOpenChange={setIsUploadModalOpen}>
        <DialogContent className="w-full max-w-md mx-auto bottom-0 fixed translate-y-0 translate-x-[-50%] left-1/2 rounded-t-3xl rounded-b-none border-0 p-0">
          <div className="p-6">
            <div className="w-8 h-1 bg-gray-300 rounded-full mx-auto mb-6"></div>

            <DialogHeader>
              <DialogTitle className="text-xl font-bold text-gray-900 mb-4">
                Upload Document
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Document Type *
                </label>
                <Select value={documentType} onValueChange={setDocumentType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select document type" />
                  </SelectTrigger>
                  <SelectContent>
                    {(DOCUMENT_TYPES as any[]).map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select File *
                </label>
                <div className="relative">
                  <input
                    type="file"
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                    onChange={handleFileSelect}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    aria-label="Select file to upload"
                    title="Select a document file to upload"
                  />
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-safety-blue transition-colors">
                    <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">
                      {selectedFile ? selectedFile.name : 'Click to upload or drag and drop'}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">PDF, DOC, XLS, PPT (max 10MB)</p>
                  </div>
                </div>
              </div>

              {fileName && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">File Name</label>
                  <Input
                    value={fileName}
                    onChange={e => setFileName(e.target.value)}
                    placeholder="Document name"
                  />
                </div>
              )}

              {uploadProgress > 0 && uploadProgress < 100 && (
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Uploading...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-safety-blue h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` } as React.CSSProperties}
                    ></div>
                  </div>
                </div>
              )}
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setIsUploadModalOpen(false)}
                className="flex-1"
                disabled={uploadDocumentMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                onClick={handleUploadDocument}
                disabled={uploadDocumentMutation.isPending || !selectedFile || !documentType}
                className="flex-1 bg-safety-blue hover:bg-blue-600"
              >
                {uploadDocumentMutation.isPending ? 'Uploading...' : 'Upload'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Document Details Modal */}
      <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
        <DialogContent className="w-full max-w-md mx-auto bottom-0 fixed translate-y-0 translate-x-[-50%] left-1/2 rounded-t-3xl rounded-b-none border-0 p-0 max-h-[90vh] overflow-y-auto">
          {selectedDocument && (
            <div className="p-6">
              <div className="w-8 h-1 bg-gray-300 rounded-full mx-auto mb-6"></div>

              <DialogHeader>
                <DialogTitle className="text-xl font-bold text-gray-900 mb-4">
                  Document Details
                </DialogTitle>
              </DialogHeader>

              <div className="space-y-4 mb-6">
                <div className="flex items-center space-x-3">
                  <span className="text-3xl">{getTypeIcon(selectedDocument.type)}</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">{selectedDocument.name}</h3>
                    <p className="text-sm text-gray-600">
                      {DOCUMENT_TYPES.find(dt => dt.value === selectedDocument.type)?.label}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Status</label>
                    <div className="mt-1">
                      <Badge className={getStatusColor(selectedDocument.status)}>
                        {selectedDocument.status?.toUpperCase()}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Type</label>
                    <p className="text-sm text-gray-900 mt-1">{selectedDocument.type}</p>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-600">Upload Date</label>
                  <p className="text-sm text-gray-900 mt-1">
                    {new Date(selectedDocument.createdAt).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}
                  </p>
                </div>

                {selectedDocument.expiryDate && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Expiry Date</label>
                    <p
                      className={`text-sm mt-1 ${
                        new Date(selectedDocument.expiryDate) < new Date()
                          ? 'text-safety-red font-semibold'
                          : 'text-gray-900'
                      }`}
                    >
                      {new Date(selectedDocument.expiryDate).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      })}
                    </p>
                  </div>
                )}

                {selectedDocument.tags && (selectedDocument.tags as any[]).length > 0 && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Tags</label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {(selectedDocument.tags as any[]).map((tag: string, index: number) => (
                        <Badge key={index} variant="outline">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-3">
                <Button className="w-full bg-safety-blue hover:bg-blue-600">
                  <Download className="w-4 h-4 mr-2" />
                  Download Document
                </Button>

                {selectedDocument.status === 'active' && (
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      onClick={() => handleStatusChange('pending')}
                      disabled={updateDocumentStatusMutation.isPending}
                      variant="outline"
                      size="sm"
                    >
                      <Clock className="w-4 h-4 mr-1" />
                      Mark Pending
                    </Button>
                    <Button
                      onClick={() => handleStatusChange('expired')}
                      disabled={updateDocumentStatusMutation.isPending}
                      variant="outline"
                      size="sm"
                      className="border-safety-red text-safety-red hover:bg-red-50"
                    >
                      <AlertTriangle className="w-4 h-4 mr-1" />
                      Mark Expired
                    </Button>
                  </div>
                )}

                <Button
                  onClick={() => setIsDetailModalOpen(false)}
                  variant="outline"
                  className="w-full"
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
