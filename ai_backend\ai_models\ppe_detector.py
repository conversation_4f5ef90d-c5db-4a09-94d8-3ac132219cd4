"""
PPE Detection Model using YOLOv8
Advanced Personal Protective Equipment detection with real-time inference
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
import cv2
import numpy as np
from ultralytics import YOLO
import torch

logger = logging.getLogger(__name__)

class PPEDetector:
    """
    Advanced PPE detection using YOLOv8 with custom training for safety equipment
    """
    
    def __init__(self, model_path: Path):
        self.model_path = model_path
        self.model_path.mkdir(exist_ok=True)
        
        self.model: Optional[YOLO] = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # PPE class mappings
        self.ppe_classes = {
            0: "helmet",
            1: "safety_vest", 
            2: "gloves",
            3: "safety_goggles",
            4: "face_mask",
            5: "safety_boots",
            6: "ear_protection",
            7: "respirator"
        }
        
        # Required PPE by zone type
        self.zone_requirements = {
            "construction": ["helmet", "safety_vest", "safety_boots"],
            "chemical": ["helmet", "safety_vest", "gloves", "face_mask", "safety_goggles"],
            "welding": ["helmet", "safety_vest", "gloves", "safety_goggles"],
            "general": ["helmet", "safety_vest"],
            "laboratory": ["gloves", "safety_goggles", "face_mask"]
        }
        
        self.performance_metrics = {
            "total_detections": 0,
            "avg_inference_time": 0.0,
            "accuracy_score": 0.0
        }
    
    async def load_model(self):
        """Load YOLOv8 model for PPE detection"""
        try:
            model_file = self.model_path / "ppe_yolov8.pt"
            
            if not model_file.exists():
                logger.info("PPE model not found, downloading pre-trained model...")
                # In production, this would download from model registry
                self.model = YOLO("yolov8n.pt")  # Start with base model
                await self._setup_custom_ppe_model()
            else:
                self.model = YOLO(str(model_file))
            
            self.model.to(self.device)
            logger.info(f"PPE detector loaded on {self.device}")
            
        except Exception as e:
            logger.error(f"Failed to load PPE model: {e}")
            raise
    
    async def _setup_custom_ppe_model(self):
        """Setup custom PPE detection model"""
        # This would typically involve:
        # 1. Loading a pre-trained model
        # 2. Fine-tuning on PPE dataset
        # 3. Saving the custom model
        
        logger.info("Setting up custom PPE detection model...")
        
        # For demo purposes, we'll use the base model
        # In production, this would be replaced with actual PPE training
        custom_model_path = self.model_path / "ppe_yolov8.pt"
        self.model.save(str(custom_model_path))
    
    async def detect(self, image: np.ndarray, confidence_threshold: float = 0.5) -> List[Dict[str, Any]]:
        """
        Detect PPE in image
        
        Args:
            image: Input image as numpy array
            confidence_threshold: Minimum confidence for detections
            
        Returns:
            List of detection results
        """
        if not self.model:
            raise RuntimeError("PPE model not loaded")
        
        start_time = time.time()
        
        try:
            # Run inference
            results = self.model(image, conf=confidence_threshold, verbose=False)
            
            detections = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Extract detection data
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = float(box.conf[0].cpu().numpy())
                        class_id = int(box.cls[0].cpu().numpy())
                        
                        if class_id in self.ppe_classes:
                            ppe_type = self.ppe_classes[class_id]
                            
                            detection = {
                                "type": "PPE_EQUIPMENT",
                                "ppe_type": ppe_type,
                                "confidence": confidence,
                                "bounding_box": {
                                    "x": int(x1),
                                    "y": int(y1),
                                    "width": int(x2 - x1),
                                    "height": int(y2 - y1)
                                },
                                "description": f"{ppe_type.replace('_', ' ').title()} detected",
                                "severity": "LOW",
                                "recommendations": [f"Verify {ppe_type.replace('_', ' ')} is properly worn"]
                            }
                            
                            detections.append(detection)
            
            # Check for PPE violations
            violations = await self._check_ppe_compliance(image, detections)
            detections.extend(violations)
            
            # Update performance metrics
            inference_time = time.time() - start_time
            self.performance_metrics["total_detections"] += len(detections)
            self.performance_metrics["avg_inference_time"] = (
                (self.performance_metrics["avg_inference_time"] + inference_time) / 2
            )
            
            return detections
            
        except Exception as e:
            logger.error(f"PPE detection error: {e}")
            return []
    
    async def _check_ppe_compliance(self, image: np.ndarray, detections: List[Dict]) -> List[Dict]:
        """Check for PPE compliance violations"""
        violations = []
        
        # Detect people in the image first
        people_detections = await self._detect_people(image)
        
        for person in people_detections:
            person_bbox = person["bounding_box"]
            
            # Find PPE items associated with this person
            person_ppe = self._find_ppe_for_person(person_bbox, detections)
            
            # Check against requirements (assuming construction zone for demo)
            required_ppe = self.zone_requirements["construction"]
            detected_ppe_types = [ppe["ppe_type"] for ppe in person_ppe]
            
            for required in required_ppe:
                if required not in detected_ppe_types:
                    violation = {
                        "type": "PPE_VIOLATION",
                        "ppe_type": required,
                        "confidence": 0.9,
                        "bounding_box": person_bbox,
                        "description": f"Missing {required.replace('_', ' ')}",
                        "severity": "HIGH",
                        "recommendations": [
                            f"Worker must wear {required.replace('_', ' ')}",
                            "Stop work until PPE is properly worn",
                            "Conduct safety briefing"
                        ]
                    }
                    violations.append(violation)
        
        return violations
    
    async def _detect_people(self, image: np.ndarray) -> List[Dict]:
        """Detect people in the image"""
        # Use YOLO to detect people (class 0 in COCO dataset)
        results = self.model(image, classes=[0], verbose=False)
        
        people = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0].cpu().numpy())
                    
                    person = {
                        "type": "PERSON",
                        "confidence": confidence,
                        "bounding_box": {
                            "x": int(x1),
                            "y": int(y1),
                            "width": int(x2 - x1),
                            "height": int(y2 - y1)
                        }
                    }
                    people.append(person)
        
        return people
    
    def _find_ppe_for_person(self, person_bbox: Dict, ppe_detections: List[Dict]) -> List[Dict]:
        """Find PPE items associated with a person based on spatial proximity"""
        person_ppe = []
        
        px, py, pw, ph = person_bbox["x"], person_bbox["y"], person_bbox["width"], person_bbox["height"]
        person_center_x = px + pw // 2
        person_center_y = py + ph // 2
        
        for ppe in ppe_detections:
            if ppe["type"] != "PPE_EQUIPMENT":
                continue
                
            ppe_bbox = ppe["bounding_box"]
            ppe_x, ppe_y, ppe_w, ppe_h = ppe_bbox["x"], ppe_bbox["y"], ppe_bbox["width"], ppe_bbox["height"]
            ppe_center_x = ppe_x + ppe_w // 2
            ppe_center_y = ppe_y + ppe_h // 2
            
            # Check if PPE is within person's bounding box or nearby
            if (px <= ppe_center_x <= px + pw and py <= ppe_center_y <= py + ph) or \
               (abs(person_center_x - ppe_center_x) < pw and abs(person_center_y - ppe_center_y) < ph):
                person_ppe.append(ppe)
        
        return person_ppe
    
    async def get_performance_metrics(self) -> Dict[str, float]:
        """Get performance metrics for the PPE detector"""
        return self.performance_metrics.copy()
    
    async def get_memory_usage(self) -> float:
        """Get memory usage of the model"""
        if self.model and torch.cuda.is_available():
            return torch.cuda.memory_allocated() / 1024**2  # MB
        return 0.0
    
    async def cleanup(self):
        """Cleanup model resources"""
        if self.model:
            del self.model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        logger.info("PPE detector cleanup complete")
