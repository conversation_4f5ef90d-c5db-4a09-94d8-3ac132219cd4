import { useState, useEffect, useRef } from 'react';
import { CameraStream } from '@/types/safety';

export function useCamera() {
  const [cameraState, setCameraState] = useState<CameraStream>({
    stream: null,
    isActive: false,
    error: null
  });
  
  const videoRef = useRef<HTMLVideoElement>(null);

  const startCamera = async () => {
    try {
      setCameraState(prev => ({ ...prev, error: null }));
      
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment',
          width: { ideal: 1280 },
          height: { ideal: 720 }
        },
        audio: false
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }

      setCameraState({
        stream,
        isActive: true,
        error: null
      });
    } catch (error) {
      setCameraState({
        stream: null,
        isActive: false,
        error: 'Failed to access camera. Please check permissions.'
      });
    }
  };

  const stopCamera = () => {
    if (cameraState.stream) {
      cameraState.stream.getTracks().forEach(track => track.stop());
    }
    
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    setCameraState({
      stream: null,
      isActive: false,
      error: null
    });
  };

  const captureImage = (): string | null => {
    if (!videoRef.current || !cameraState.isActive) return null;

    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    
    canvas.width = videoRef.current.videoWidth;
    canvas.height = videoRef.current.videoHeight;
    
    if (context) {
      context.drawImage(videoRef.current, 0, 0);
      return canvas.toDataURL('image/jpeg', 0.8);
    }
    
    return null;
  };

  useEffect(() => {
    return () => {
      if (cameraState.stream) {
        cameraState.stream.getTracks().forEach(track => track.stop());
      }
    };
  }, [cameraState.stream]);

  return {
    videoRef,
    cameraState,
    startCamera,
    stopCamera,
    captureImage
  };
}
