import { <PERSON> } from 'wouter';
import { 
  Shield, 
  ArrowLeft,
  MapPin,
  Clock,
  DollarSign,
  Users,
  Briefcase,
  Heart,
  Zap,
  Globe,
  ArrowRight
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function CareersPage() {
  const benefits = [
    {
      icon: Heart,
      title: "Health & Wellness",
      description: "Comprehensive health insurance, mental health support, and wellness programs"
    },
    {
      icon: DollarSign,
      title: "Competitive Compensation",
      description: "Top-tier salaries, equity packages, and performance bonuses"
    },
    {
      icon: Clock,
      title: "Work-Life Balance",
      description: "Flexible hours, remote work options, and unlimited PTO"
    },
    {
      icon: Zap,
      title: "Growth Opportunities",
      description: "Learning budget, conference attendance, and career development programs"
    },
    {
      icon: Globe,
      title: "Global Impact",
      description: "Work on technology that saves lives and makes workplaces safer worldwide"
    },
    {
      icon: Users,
      title: "Amazing Team",
      description: "Collaborate with world-class engineers, researchers, and safety experts"
    }
  ];

  const openPositions = [
    {
      id: 1,
      title: "Senior AI Engineer",
      department: "Engineering",
      location: "San Francisco, CA",
      type: "Full-time",
      experience: "5+ years",
      description: "Lead the development of our next-generation AI detection models using computer vision and deep learning.",
      requirements: [
        "PhD/MS in Computer Science, AI, or related field",
        "5+ years experience with PyTorch/TensorFlow",
        "Experience with computer vision and object detection",
        "Knowledge of YOLO, R-CNN, or similar architectures"
      ],
      salary: "$180k - $250k"
    },
    {
      id: 2,
      title: "Frontend Engineer (React)",
      department: "Engineering",
      location: "Remote",
      type: "Full-time",
      experience: "3+ years",
      description: "Build beautiful, responsive user interfaces for our AI safety platform using React and TypeScript.",
      requirements: [
        "3+ years React/TypeScript experience",
        "Experience with modern CSS frameworks",
        "Mobile-first development approach",
        "Understanding of accessibility standards"
      ],
      salary: "$120k - $180k"
    },
    {
      id: 3,
      title: "Safety Technology Specialist",
      department: "Product",
      location: "New York, NY",
      type: "Full-time",
      experience: "7+ years",
      description: "Bridge the gap between AI technology and real-world safety applications in industrial environments.",
      requirements: [
        "Certified Safety Professional (CSP) preferred",
        "7+ years in industrial safety",
        "Experience with safety management systems",
        "Understanding of OSHA regulations"
      ],
      salary: "$100k - $140k"
    },
    {
      id: 4,
      title: "DevOps Engineer",
      department: "Engineering",
      location: "San Francisco, CA",
      type: "Full-time",
      experience: "4+ years",
      description: "Scale our AI infrastructure to handle millions of safety detections per day across global deployments.",
      requirements: [
        "4+ years DevOps/SRE experience",
        "Kubernetes and Docker expertise",
        "AWS/GCP/Azure cloud platforms",
        "Experience with ML model deployment"
      ],
      salary: "$140k - $200k"
    },
    {
      id: 5,
      title: "Customer Success Manager",
      department: "Customer Success",
      location: "Remote",
      type: "Full-time",
      experience: "3+ years",
      description: "Help enterprise customers maximize value from our AI safety platform and drive adoption.",
      requirements: [
        "3+ years in customer success or account management",
        "Experience with enterprise software",
        "Strong communication and presentation skills",
        "Background in safety or industrial sectors preferred"
      ],
      salary: "$80k - $120k"
    },
    {
      id: 6,
      title: "Data Scientist",
      department: "AI Research",
      location: "San Francisco, CA",
      type: "Full-time",
      experience: "4+ years",
      description: "Analyze safety data to improve AI model performance and develop new predictive analytics capabilities.",
      requirements: [
        "MS/PhD in Data Science, Statistics, or related field",
        "4+ years experience with Python, R, SQL",
        "Experience with machine learning pipelines",
        "Knowledge of statistical analysis and modeling"
      ],
      salary: "$130k - $190k"
    }
  ];

  const departments = [
    { name: "Engineering", count: 12, description: "Build the future of AI safety technology" },
    { name: "AI Research", count: 8, description: "Advance the state of AI in workplace safety" },
    { name: "Product", count: 6, description: "Design products that save lives" },
    { name: "Sales", count: 4, description: "Help companies transform their safety programs" },
    { name: "Customer Success", count: 5, description: "Ensure customer success and satisfaction" },
    { name: "Operations", count: 3, description: "Scale our global operations efficiently" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/">
              <Button variant="ghost" className="text-white hover:bg-white/10">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Home
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <Shield className="h-8 w-8 text-blue-400" />
              <span className="text-xl font-bold text-white">SafeGuard AI</span>
            </div>
            <Link href="/dashboard">
              <Button className="bg-blue-600 hover:bg-blue-700">
                Launch App
              </Button>
            </Link>
          </div>
        </div>
      </nav>

      {/* Hero */}
      <section className="pt-32 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Join Our
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              {" "}Mission
            </span>
          </h1>
          <p className="text-xl text-white/80 mb-8">
            Help us build the future of workplace safety. Join a team of world-class engineers, 
            researchers, and safety experts working to prevent workplace accidents.
          </p>
          <div className="flex items-center justify-center space-x-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-400">50+</div>
              <div className="text-white/60">Team Members</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-400">15+</div>
              <div className="text-white/60">Countries</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-400">$50M</div>
              <div className="text-white/60">Series B Funding</div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">Why Work With Us</h2>
            <p className="text-xl text-white/80">
              We offer more than just a job - we offer the opportunity to make a real impact
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 text-center">
                <CardContent className="p-6">
                  <benefit.icon className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-3">{benefit.title}</h3>
                  <p className="text-white/70">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Departments */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">Our Departments</h2>
            <p className="text-xl text-white/80">
              Find your place in our growing team
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {departments.map((dept, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-lg font-semibold text-white">{dept.name}</h3>
                    <Badge className="bg-blue-600">{dept.count} open</Badge>
                  </div>
                  <p className="text-white/70 text-sm">{dept.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Open Positions */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">Open Positions</h2>
            <p className="text-xl text-white/80">
              Join us in building the future of AI-powered workplace safety
            </p>
          </div>
          <div className="space-y-6">
            {openPositions.map((position) => (
              <Card key={position.id} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300">
                <CardHeader>
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div>
                      <CardTitle className="text-2xl text-white mb-2">{position.title}</CardTitle>
                      <div className="flex flex-wrap items-center gap-4 text-white/60">
                        <div className="flex items-center space-x-1">
                          <Briefcase className="h-4 w-4" />
                          <span>{position.department}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-4 w-4" />
                          <span>{position.location}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span>{position.type}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Users className="h-4 w-4" />
                          <span>{position.experience}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-400">{position.salary}</div>
                      <div className="text-white/60 text-sm">Annual salary</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-white/80 mb-6">{position.description}</p>
                  <div className="mb-6">
                    <h4 className="text-white font-semibold mb-3">Requirements:</h4>
                    <ul className="space-y-2">
                      {position.requirements.map((req, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-white/70 text-sm">{req}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button className="bg-blue-600 hover:bg-blue-700">
                      Apply Now
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                    <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
                      Learn More
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Culture */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">Our Culture</h2>
            <p className="text-xl text-white/80">
              What it's like to work at SafeGuard AI
            </p>
          </div>
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-2xl font-bold text-white mb-6">Innovation-Driven Environment</h3>
              <div className="space-y-4">
                <p className="text-white/80">
                  We foster a culture of innovation where every team member is encouraged to push 
                  the boundaries of what's possible in AI and workplace safety.
                </p>
                <p className="text-white/80">
                  Our flat organizational structure means your ideas can quickly become reality, 
                  and your contributions directly impact the safety of workers worldwide.
                </p>
                <p className="text-white/80">
                  We believe in continuous learning, experimentation, and the power of diverse 
                  perspectives to solve complex challenges.
                </p>
              </div>
              <div className="mt-8 grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-400">4.9/5</div>
                  <div className="text-white/60">Employee Rating</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-400">95%</div>
                  <div className="text-white/60">Retention Rate</div>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="aspect-square bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-xl flex items-center justify-center">
                  <div className="text-4xl">🚀</div>
                </div>
                <div className="aspect-square bg-gradient-to-br from-green-600/20 to-blue-600/20 rounded-xl flex items-center justify-center">
                  <div className="text-4xl">🤝</div>
                </div>
              </div>
              <div className="space-y-4 mt-8">
                <div className="aspect-square bg-gradient-to-br from-purple-600/20 to-pink-600/20 rounded-xl flex items-center justify-center">
                  <div className="text-4xl">💡</div>
                </div>
                <div className="aspect-square bg-gradient-to-br from-orange-600/20 to-red-600/20 rounded-xl flex items-center justify-center">
                  <div className="text-4xl">🎯</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Application Process */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">Application Process</h2>
            <p className="text-xl text-white/80">
              Our streamlined hiring process designed to find the best talent
            </p>
          </div>
          <div className="grid md:grid-cols-4 gap-8">
            {[
              { step: "1", title: "Apply", description: "Submit your application and resume online" },
              { step: "2", title: "Screen", description: "Initial phone/video screening with our team" },
              { step: "3", title: "Interview", description: "Technical and cultural fit interviews" },
              { step: "4", title: "Offer", description: "Reference checks and job offer" }
            ].map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold">{step.step}</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">{step.title}</h3>
                <p className="text-white/70 text-sm">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Make an Impact?
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Join us in building technology that saves lives and transforms workplace safety.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
              View Open Positions
            </Button>
            <Link href="/contact">
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                Contact HR
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
