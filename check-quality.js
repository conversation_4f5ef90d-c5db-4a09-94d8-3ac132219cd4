#!/usr/bin/env node

import { execSync } from 'child_process';

console.log('🔍 Running Code Quality Checks...\n');

const checks = [
  {
    name: 'TypeScript Compilation',
    command: 'cd client && npm run type-check',
    description: 'Checking TypeScript types and compilation'
  },
  {
    name: 'ESLint Analysis',
    command: 'cd client && npm run lint',
    description: 'Checking code style and potential issues'
  },
  {
    name: 'Prettier Formatting',
    command: 'cd client && npx prettier --check "src/**/*.{ts,tsx,js,jsx,json,css,md}"',
    description: 'Checking code formatting consistency'
  }
];

let allPassed = true;
const results = [];

for (const check of checks) {
  console.log(`📋 ${check.name}...`);
  console.log(`   ${check.description}`);
  
  try {
    const output = execSync(check.command, { 
      encoding: 'utf8', 
      stdio: 'pipe' 
    });
    
    console.log(`   ✅ PASSED\n`);
    results.push({ name: check.name, status: 'PASSED', output });
    
  } catch (error) {
    console.log(`   ❌ FAILED`);
    console.log(`   Error: ${error.message}\n`);
    results.push({ name: check.name, status: 'FAILED', error: error.message });
    allPassed = false;
  }
}

console.log('='.repeat(50));

if (allPassed) {
  console.log('🎉 All quality checks passed!');
  console.log('\n✨ Your code is ready for production:');
  console.log('   • TypeScript types are correct');
  console.log('   • Code follows ESLint rules');
  console.log('   • Formatting is consistent');
  
} else {
  console.log('❌ Some quality checks failed.');
  console.log('\n🔧 To fix issues automatically:');
  console.log('   cd client');
  console.log('   npm run lint:fix        # Fix ESLint issues');
  console.log('   npm run format          # Fix formatting');
  console.log('   npm run type-check      # Check types again');
}

console.log('\n📊 Summary:');
results.forEach(result => {
  const status = result.status === 'PASSED' ? '✅' : '❌';
  console.log(`   ${status} ${result.name}`);
});

if (!allPassed) {
  process.exit(1);
}
