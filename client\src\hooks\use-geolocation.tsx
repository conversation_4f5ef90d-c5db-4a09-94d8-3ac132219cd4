import { useState, useEffect } from 'react';
import { GeolocationCoords } from '@/types/safety';

interface GeolocationState {
  coords: GeolocationCoords | null;
  error: string | null;
  isLoading: boolean;
}

export function useGeolocation() {
  const [state, setState] = useState<GeolocationState>({
    coords: null,
    error: null,
    isLoading: false
  });

  const getCurrentPosition = () => {
    if (!navigator.geolocation) {
      setState({
        coords: null,
        error: 'Geolocation is not supported by this browser',
        isLoading: false
      });
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    navigator.geolocation.getCurrentPosition(
      (position) => {
        setState({
          coords: {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy
          },
          error: null,
          isLoading: false
        });
      },
      (error) => {
        let errorMessage = 'Failed to get location';
        
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied by user';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information unavailable';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out';
            break;
        }

        setState({
          coords: null,
          error: errorMessage,
          isLoading: false
        });
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  };

  useEffect(() => {
    getCurrentPosition();
  }, []);

  return {
    ...state,
    refreshLocation: getCurrentPosition
  };
}
