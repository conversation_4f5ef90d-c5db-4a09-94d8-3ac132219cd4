import { Button } from '@/components/ui/button';
import { useCamera } from '@/hooks/use-camera';
import { AIDetectionService } from '@/services/ai-detection';
import { AIDetection } from '@/types/safety';
import { Camera, Video, X, Zap } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface CameraOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  onCapture: (imageData: string, analysis?: any) => void;
  onQuickReport: () => void;
}

export default function CameraOverlay({
  isOpen,
  onClose,
  onCapture,
  onQuickReport,
}: CameraOverlayProps) {
  const { videoRef, isActive, error, startCamera, stopCamera, capturePhoto, startRecording, stopRecording } = useCamera();
  const [detections, setDetections] = useState<AIDetection[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (isOpen) {
      startCamera();
      // Start real-time analysis
      intervalRef.current = setInterval(async () => {
        if (videoRef.current && isActive) {
          setIsAnalyzing(true);
          try {
            const frameDetections = await AIDetectionService.processLiveFrame(videoRef.current);
            setDetections(frameDetections);
          } catch (error) {
            console.error('Real-time analysis error:', error);
          }
          setIsAnalyzing(false);
        }
      }, 2000); // Analyze every 2 seconds
    } else {
      stopCamera();
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isOpen, isActive]);

  const handleCapture = async () => {
    const imageData = capturePhoto();
    if (imageData) {
      try {
        const analysis = await AIDetectionService.analyzeImage(imageData, 'INCIDENT_CAPTURE');
        onCapture(imageData, analysis);
      } catch (error) {
        console.error('Image analysis error:', error);
        onCapture(imageData);
      }
    }
  };

  const handleRecording = () => {
    setIsRecording(!isRecording);
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black z-50">
      <div className="relative w-full h-full">
        {/* Camera Feed */}
        <video ref={videoRef} autoPlay playsInline muted className="w-full h-full object-cover" />

        {/* Error State */}
        {error && (
          <div className="absolute inset-0 bg-gray-900 flex items-center justify-center">
            <div className="text-center text-white p-4">
              <p className="text-lg font-semibold mb-2">Camera Error</p>
              <p className="text-sm opacity-80">{error}</p>
              <Button onClick={startCamera} className="mt-4 bg-safety-blue hover:bg-blue-600">
                Retry
              </Button>
            </div>
          </div>
        )}

        {/* AI Detection Overlays */}
        {detections.map((detection, index) => (
          <div
            key={index}
            className={`detection-box absolute border-2 rounded animate-pulse ${
              detection.type === 'PPE_VIOLATION'
                ? 'border-red-500 bg-red-500/20 top-20 left-20 w-24 h-32'
                : detection.type === 'SAFETY_EQUIPMENT'
                  ? 'border-green-500 bg-green-500/20 top-32 left-40 w-28 h-36'
                  : 'border-orange-500 bg-orange-500/20 top-16 left-60 w-20 h-28'
            } ${index === 1 ? 'top-40 left-32' : index === 2 ? 'top-24 left-48' : ''}`}
          >
            <div
              className={`px-2 py-1 text-xs font-medium rounded-bl ${
                detection.type === 'PPE_VIOLATION'
                  ? 'bg-safety-red text-white'
                  : detection.type === 'SAFETY_EQUIPMENT'
                    ? 'bg-safety-green text-white'
                    : 'bg-safety-orange text-white'
              }`}
            >
              {detection.type === 'PPE_VIOLATION'
                ? '⚠'
                : detection.type === 'SAFETY_EQUIPMENT'
                  ? '✓'
                  : '⚠'}
              {detection.description}
            </div>
          </div>
        ))}

        {/* Camera Controls Overlay */}
        <div className="absolute bottom-0 left-0 right-0 camera-overlay p-6">
          {/* Detection Status */}
          <div className="flex items-center justify-center space-x-4 mb-6">
            <div
              className={`flex items-center space-x-2 px-3 py-2 rounded-full ${
                isAnalyzing ? 'bg-safety-blue/90' : 'bg-safety-green/90'
              }`}
            >
              <div
                className={`w-2 h-2 rounded-full ${
                  isAnalyzing ? 'bg-white animate-pulse' : 'bg-white animate-pulse-green'
                }`}
              ></div>
              <span className="text-white text-sm font-medium">
                {isAnalyzing ? 'AI Analyzing' : 'AI Ready'}
              </span>
            </div>
          </div>

          {/* Control Buttons */}
          <div className="flex items-center justify-between">
            <Button
              onClick={onClose}
              variant="secondary"
              size="lg"
              className="bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 rounded-full w-12 h-12 p-0"
            >
              <X className="w-6 h-6" />
            </Button>

            <div className="flex space-x-4">
              <Button
                onClick={handleCapture}
                className="bg-safety-red text-white hover:bg-red-600 rounded-full w-16 h-16 p-0 shadow-lg"
              >
                <Camera className="w-8 h-8" />
              </Button>
              <Button
                onClick={handleRecording}
                variant="secondary"
                size="lg"
                className={`backdrop-blur-sm text-white rounded-full w-12 h-12 p-0 ${
                  isRecording
                    ? 'bg-safety-red/80 hover:bg-safety-red'
                    : 'bg-white/20 hover:bg-white/30'
                }`}
              >
                <Video className="w-6 h-6" />
              </Button>
            </div>

            <Button
              variant="secondary"
              size="lg"
              className="bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 rounded-full w-12 h-12 p-0"
            >
              <Zap className="w-6 h-6" />
            </Button>
          </div>

          {/* Quick Report Button */}
          <div className="mt-4">
            <Button
              onClick={onQuickReport}
              className="w-full bg-safety-orange text-white hover:bg-orange-600 py-3 rounded-lg font-medium flex items-center justify-center space-x-2"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z" />
              </svg>
              <span>Quick Report</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
