import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>2, Send } from 'lucide-react';
import { <PERSON> } from 'wouter';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import MobileLayout from '@/components/mobile-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useSpeechRecognition } from '@/hooks/use-speech';
import { useGeolocation } from '@/hooks/use-geolocation';
import { VoiceRecognitionService } from '@/services/voice-recognition';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

const MOCK_USER_ID = "mock-user-123";

export default function VoiceReport() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [classification, setClassification] = useState<any>(null);
  const [manualNotes, setManualNotes] = useState('');
  const [selectedSeverity, setSelectedSeverity] = useState('');
  
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { coords } = useGeolocation();
  const {
    isListening,
    transcript,
    error,
    isSupported,
    startListening,
    stopListening,
    resetTranscript
  } = useSpeechRecognition();

  const createIncidentMutation = useMutation({
    mutationFn: async (data: any) => {
      return await apiRequest('POST', '/api/incidents', data);
    },
    onSuccess: () => {
      toast({
        title: "Voice Report Submitted",
        description: "Your safety report has been processed and submitted.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/incidents'] });
      queryClient.invalidateQueries({ queryKey: ['/api/dashboard'] });
      resetForm();
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to submit voice report. Please try again.",
        variant: "destructive",
      });
    }
  });

  const resetForm = () => {
    resetTranscript();
    setClassification(null);
    setManualNotes('');
    setSelectedSeverity('');
  };

  const handleProcessVoice = async () => {
    if (!transcript.trim()) {
      toast({
        title: "No Speech Detected",
        description: "Please record your voice message first.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    try {
      const result = await VoiceRecognitionService.processVoiceInput(transcript);
      setClassification(result);
      
      // Auto-set severity based on classification
      if (result.severity === 'HIGH') {
        setSelectedSeverity('high');
      } else if (result.severity === 'MEDIUM') {
        setSelectedSeverity('medium');
      } else {
        setSelectedSeverity('low');
      }
    } catch (error) {
      toast({
        title: "Processing Failed",
        description: "Failed to process voice input. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSubmitReport = () => {
    if (!classification && !manualNotes.trim()) {
      toast({
        title: "Missing Information",
        description: "Please record a voice message or add manual notes.",
        variant: "destructive",
      });
      return;
    }

    if (!selectedSeverity) {
      toast({
        title: "Missing Severity",
        description: "Please select a severity level.",
        variant: "destructive",
      });
      return;
    }

    const incidentData = {
      userId: MOCK_USER_ID,
      type: classification?.hazardType || 'GENERAL_HAZARD',
      severity: selectedSeverity.toUpperCase(),
      description: classification?.structuredReport?.type || 'Voice reported incident',
      notes: `Voice transcript: "${transcript}"\n\nAdditional notes: ${manualNotes}`,
      location: classification?.location || 'Voice reported location',
      gpsCoordinates: coords ? { 
        latitude: coords.latitude, 
        longitude: coords.longitude 
      } : null,
      status: 'open'
    };

    createIncidentMutation.mutate(incidentData);
  };

  if (!isSupported) {
    return (
      <MobileLayout
        title="Voice Report"
        subtitle="Voice recognition not supported"
      >
        <div className="px-4 py-6">
          <Link href="/">
            <Button variant="ghost" size="sm" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-gray-400 rounded-full flex items-center justify-center mx-auto mb-4">
                <MicOff className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-lg font-semibold mb-2">Voice Recognition Unavailable</h2>
              <p className="text-gray-600">
                Your browser doesn't support voice recognition. Please use another device or browser.
              </p>
            </CardContent>
          </Card>
        </div>
      </MobileLayout>
    );
  }

  return (
    <MobileLayout
      title="Voice Report"
      subtitle="Talk2Report - Hands-free reporting"
    >
      <div className="px-4 py-6">
        <div className="mb-6">
          <Link href="/">
            <Button variant="ghost" size="sm" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
        </div>

        {/* Voice Recording Section */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="text-center">
              <div className={`w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 transition-all ${
                isListening ? 'bg-safety-red animate-pulse' : 'bg-safety-blue'
              }`}>
                {isListening ? (
                  <Mic className="w-10 h-10 text-white" />
                ) : (
                  <MicOff className="w-10 h-10 text-white" />
                )}
              </div>
              
              <h2 className="text-xl font-bold text-gray-900 mb-2">
                {isListening ? 'Recording...' : 'Voice Safety Report'}
              </h2>
              
              <p className="text-gray-600 mb-6">
                {isListening 
                  ? 'Speak clearly about the safety concern' 
                  : 'Tap the microphone to start recording your safety report'
                }
              </p>

              {!isListening ? (
                <Button
                  onClick={startListening}
                  className="w-full bg-safety-blue hover:bg-blue-600 py-4"
                  disabled={isProcessing}
                >
                  <Mic className="w-5 h-5 mr-2" />
                  Start Recording
                </Button>
              ) : (
                <Button
                  onClick={stopListening}
                  className="w-full bg-safety-red hover:bg-red-600 py-4"
                >
                  <MicOff className="w-5 h-5 mr-2" />
                  Stop Recording
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Live Transcript */}
        {transcript && (
          <Card className="mb-6">
            <CardContent className="p-4">
              <h3 className="font-semibold mb-3">Live Transcript</h3>
              <div className="bg-gray-50 p-3 rounded-lg border-l-4 border-safety-blue">
                <p className="text-gray-800">{transcript}</p>
              </div>
              
              <div className="flex space-x-2 mt-4">
                <Button
                  onClick={handleProcessVoice}
                  disabled={isProcessing || isListening}
                  className="flex-1 bg-safety-green hover:bg-green-600"
                >
                  {isProcessing ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Send className="w-4 h-4 mr-2" />
                  )}
                  Process Voice
                </Button>
                <Button
                  onClick={resetTranscript}
                  variant="outline"
                  disabled={isListening}
                >
                  Clear
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* AI Classification Results */}
        {classification && (
          <Card className="mb-6">
            <CardContent className="p-4">
              <h3 className="font-semibold mb-3">AI Analysis Results</h3>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span className="font-medium">Hazard Type:</span>
                  <span className="text-safety-blue font-semibold">{classification.hazardType}</span>
                </div>
                
                <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                  <span className="font-medium">Severity:</span>
                  <span className={`font-semibold ${
                    classification.severity === 'HIGH' ? 'text-safety-red' :
                    classification.severity === 'MEDIUM' ? 'text-safety-orange' :
                    'text-safety-green'
                  }`}>
                    {classification.severity}
                  </span>
                </div>
                
                {classification.location && (
                  <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                    <span className="font-medium">Location:</span>
                    <span className="text-safety-green font-semibold">{classification.location}</span>
                  </div>
                )}
              </div>

              {classification.structuredReport?.actionItems && (
                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <h4 className="font-medium mb-2">Recommended Actions:</h4>
                  <ul className="text-sm space-y-1">
                    {classification.structuredReport.actionItems.map((item: string, index: number) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-safety-blue">•</span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Manual Input Section */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <h3 className="font-semibold mb-4">Additional Details</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Severity Level *
                </label>
                <Select value={selectedSeverity} onValueChange={setSelectedSeverity}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high">High Risk</SelectItem>
                    <SelectItem value="medium">Medium Risk</SelectItem>
                    <SelectItem value="low">Low Risk</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Notes
                </label>
                <Textarea
                  value={manualNotes}
                  onChange={(e) => setManualNotes(e.target.value)}
                  placeholder="Add any additional details not captured in voice..."
                  className="resize-none"
                  rows={4}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <Button
          onClick={handleSubmitReport}
          disabled={createIncidentMutation.isPending || (!classification && !manualNotes.trim())}
          className="w-full bg-safety-red hover:bg-red-600 py-4"
        >
          {createIncidentMutation.isPending ? (
            <Loader2 className="w-5 h-5 mr-2 animate-spin" />
          ) : (
            <Send className="w-5 h-5 mr-2" />
          )}
          Submit Voice Report
        </Button>

        {/* Error Display */}
        {error && (
          <Card className="mt-4">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2 text-safety-red">
                <MicOff className="w-5 h-5" />
                <p className="text-sm">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Voice Tips */}
        <Card className="mt-6">
          <CardContent className="p-4">
            <h3 className="font-semibold mb-3">Voice Recording Tips</h3>
            <ul className="text-sm text-gray-600 space-y-2">
              <li className="flex items-start space-x-2">
                <span className="text-safety-green">•</span>
                <span>Speak clearly and at normal pace</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-safety-green">•</span>
                <span>Include location details (e.g., "near boiler room")</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-safety-green">•</span>
                <span>Describe the hazard type (spill, equipment, PPE, etc.)</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-safety-green">•</span>
                <span>Mention urgency level if critical</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </MobileLayout>
  );
}
