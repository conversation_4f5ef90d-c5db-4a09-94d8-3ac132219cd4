import { useEffect, useRef, useState } from 'react';

// interface SpeechRecognitionResult {
//   transcript: string;
//   confidence: number;
//   isFinal: boolean;
// }

interface SpeechState {
  isListening: boolean;
  transcript: string;
  error: string | null;
  isSupported: boolean;
}

export function useSpeechRecognition() {
  const [speechState, setSpeechState] = useState<SpeechState>({
    isListening: false,
    transcript: '',
    error: null,
    isSupported: 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window,
  });

  const recognitionRef = useRef<any>(null);

  useEffect(() => {
    if (!speechState.isSupported) return;

    const SpeechRecognition =
      (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    recognitionRef.current = new SpeechRecognition();

    const recognition = recognitionRef.current;
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';

    recognition.onstart = () => {
      setSpeechState(prev => ({ ...prev, isListening: true, error: null }));
    };

    recognition.onresult = (event: any) => {
      let transcript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        transcript += event.results[i][0].transcript;
      }

      setSpeechState(prev => ({ ...prev, transcript }));
    };

    recognition.onerror = (event: any) => {
      setSpeechState(prev => ({
        ...prev,
        error: `Speech recognition error: ${event.error}`,
        isListening: false,
      }));
    };

    recognition.onend = () => {
      setSpeechState(prev => ({ ...prev, isListening: false }));
    };

    return () => {
      if (recognition) {
        recognition.stop();
      }
    };
  }, [speechState.isSupported]);

  const startListening = () => {
    if (!speechState.isSupported || !recognitionRef.current) return;

    try {
      recognitionRef.current.start();
    } catch (error) {
      setSpeechState(prev => ({
        ...prev,
        error: 'Failed to start speech recognition',
      }));
    }
  };

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
  };

  const resetTranscript = () => {
    setSpeechState(prev => ({ ...prev, transcript: '', error: null }));
  };

  return {
    ...speechState,
    startListening,
    stopListening,
    resetTranscript,
  };
}
