export interface AIDetection {
  type: string;
  confidence: number;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  description: string;
}

export interface AIAnalysis {
  detections: AIDetection[];
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  recommendations: string[];
}

export interface DashboardStats {
  todayInspections: number;
  todayAlerts: number;
  complianceScore: number;
  recentIncidents: any[];
}

export interface VoiceClassification {
  hazardType: string;
  severity: string;
  location: string;
  structuredReport: {
    type: string;
    urgency: string;
    actionItems: string[];
  };
}

export interface GeolocationCoords {
  latitude: number;
  longitude: number;
  accuracy?: number;
}

export interface CameraStream {
  stream: MediaStream | null;
  isActive: boolean;
  error: string | null;
}

export interface SafetyModule {
  id: string;
  name: string;
  icon: string;
  color: string;
  description: string;
  route: string;
  isActive?: boolean;
}
