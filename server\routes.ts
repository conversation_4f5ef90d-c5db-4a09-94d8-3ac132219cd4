import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertIncidentSchema, insertSafetyCheckSchema, insertToolboxTalkSchema, insertDocumentSchema, insertAlertSchema } from "@shared/schema";
import { z } from "zod";

export async function registerRoutes(app: Express): Promise<Server> {
  
  // Dashboard stats
  app.get("/api/dashboard/:userId", async (req, res) => {
    try {
      const { userId } = req.params;
      const stats = await storage.getDashboardStats(userId);
      res.json(stats);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch dashboard stats" });
    }
  });

  // Incidents
  app.post("/api/incidents", async (req, res) => {
    try {
      const incident = insertIncidentSchema.parse(req.body);
      const newIncident = await storage.createIncident(incident);
      res.json(newIncident);
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Invalid incident data", details: error.errors });
      } else {
        res.status(500).json({ error: "Failed to create incident" });
      }
    }
  });

  app.get("/api/incidents", async (req, res) => {
    try {
      const { userId, limit } = req.query;
      const incidents = await storage.getIncidents(
        userId as string, 
        limit ? parseInt(limit as string) : undefined
      );
      res.json(incidents);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch incidents" });
    }
  });

  app.get("/api/incidents/:id", async (req, res) => {
    try {
      const { id } = req.params;
      const incident = await storage.getIncident(id);
      if (!incident) {
        return res.status(404).json({ error: "Incident not found" });
      }
      res.json(incident);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch incident" });
    }
  });

  app.patch("/api/incidents/:id/status", async (req, res) => {
    try {
      const { id } = req.params;
      const { status } = req.body;
      await storage.updateIncidentStatus(id, status);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to update incident status" });
    }
  });

  // Safety checks
  app.post("/api/safety-checks", async (req, res) => {
    try {
      const check = insertSafetyCheckSchema.parse(req.body);
      const newCheck = await storage.createSafetyCheck(check);
      res.json(newCheck);
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Invalid safety check data", details: error.errors });
      } else {
        res.status(500).json({ error: "Failed to create safety check" });
      }
    }
  });

  app.get("/api/safety-checks", async (req, res) => {
    try {
      const { userId, type, limit } = req.query;
      
      if (type) {
        const checks = await storage.getSafetyChecksByType(type as string);
        res.json(checks);
      } else {
        const checks = await storage.getSafetyChecks(
          userId as string, 
          limit ? parseInt(limit as string) : undefined
        );
        res.json(checks);
      }
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch safety checks" });
    }
  });

  // Toolbox talks
  app.post("/api/toolbox-talks", async (req, res) => {
    try {
      const talk = insertToolboxTalkSchema.parse(req.body);
      const newTalk = await storage.createToolboxTalk(talk);
      res.json(newTalk);
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Invalid toolbox talk data", details: error.errors });
      } else {
        res.status(500).json({ error: "Failed to create toolbox talk" });
      }
    }
  });

  app.get("/api/toolbox-talks", async (req, res) => {
    try {
      const { userId } = req.query;
      const talks = await storage.getToolboxTalks(userId as string);
      res.json(talks);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch toolbox talks" });
    }
  });

  app.patch("/api/toolbox-talks/:id/attendees", async (req, res) => {
    try {
      const { id } = req.params;
      const { attendees } = req.body;
      await storage.updateToolboxTalkAttendees(id, attendees);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to update attendees" });
    }
  });

  // Documents
  app.post("/api/documents", async (req, res) => {
    try {
      const document = insertDocumentSchema.parse(req.body);
      const newDocument = await storage.createDocument(document);
      res.json(newDocument);
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Invalid document data", details: error.errors });
      } else {
        res.status(500).json({ error: "Failed to create document" });
      }
    }
  });

  app.get("/api/documents", async (req, res) => {
    try {
      const { userId, type } = req.query;
      
      if (type) {
        const documents = await storage.getDocumentsByType(type as string);
        res.json(documents);
      } else {
        const documents = await storage.getDocuments(userId as string);
        res.json(documents);
      }
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch documents" });
    }
  });

  app.patch("/api/documents/:id/status", async (req, res) => {
    try {
      const { id } = req.params;
      const { status } = req.body;
      await storage.updateDocumentStatus(id, status);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to update document status" });
    }
  });

  // Alerts
  app.post("/api/alerts", async (req, res) => {
    try {
      const alert = insertAlertSchema.parse(req.body);
      const newAlert = await storage.createAlert(alert);
      res.json(newAlert);
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Invalid alert data", details: error.errors });
      } else {
        res.status(500).json({ error: "Failed to create alert" });
      }
    }
  });

  app.get("/api/alerts/unsent", async (req, res) => {
    try {
      const alerts = await storage.getUnsentAlerts();
      res.json(alerts);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch unsent alerts" });
    }
  });

  app.patch("/api/alerts/:id/sent", async (req, res) => {
    try {
      const { id } = req.params;
      await storage.markAlertAsSent(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to mark alert as sent" });
    }
  });

  // AI Detection endpoints
  app.post("/api/ai/analyze-image", async (req, res) => {
    try {
      const { imageData, type } = req.body;
      
      // Simulate AI processing - in real implementation, this would call
      // TensorFlow.js models or external AI services
      const mockAnalysis = {
        detections: [
          {
            type: "PPE_VIOLATION",
            confidence: 0.89,
            boundingBox: { x: 100, y: 150, width: 120, height: 200 },
            description: "Missing safety helmet"
          },
          {
            type: "SAFETY_EQUIPMENT",
            confidence: 0.95,
            boundingBox: { x: 200, y: 180, width: 80, height: 120 },
            description: "Safety vest detected"
          }
        ],
        riskLevel: "HIGH",
        recommendations: [
          "Ensure worker wears appropriate helmet",
          "Verify compliance with safety protocols"
        ]
      };

      res.json(mockAnalysis);
    } catch (error) {
      res.status(500).json({ error: "Failed to analyze image" });
    }
  });

  app.post("/api/ai/process-voice", async (req, res) => {
    try {
      const { audioData, transcript } = req.body;
      
      // Simulate NLP processing for hazard classification
      const mockClassification = {
        hazardType: "SPILL_HAZARD",
        severity: "MEDIUM",
        location: "Boiler room",
        structuredReport: {
          type: "Chemical spill",
          urgency: "Immediate attention required",
          actionItems: [
            "Isolate affected area",
            "Deploy spill response team",
            "Notify environmental safety officer"
          ]
        }
      };

      res.json(mockClassification);
    } catch (error) {
      res.status(500).json({ error: "Failed to process voice input" });
    }
  });

  // Export functionality
  app.get("/api/reports/export", async (req, res) => {
    try {
      const { format, startDate, endDate, type } = req.query;
      
      let incidents = [];
      if (startDate && endDate) {
        incidents = await storage.getIncidentsByDateRange(
          new Date(startDate as string),
          new Date(endDate as string)
        );
      } else {
        incidents = await storage.getIncidents();
      }

      if (format === 'csv') {
        const csvHeader = 'ID,Type,Severity,Description,Location,Created At,Status\n';
        const csvData = incidents.map(incident => 
          `${incident.id},${incident.type},${incident.severity},"${incident.description}",${incident.location || ''},${incident.createdAt},${incident.status}`
        ).join('\n');
        
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=safety-report.csv');
        res.send(csvHeader + csvData);
      } else {
        res.json(incidents);
      }
    } catch (error) {
      res.status(500).json({ error: "Failed to export reports" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
