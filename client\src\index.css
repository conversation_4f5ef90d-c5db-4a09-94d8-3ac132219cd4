@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(60, 4.8%, 95.9%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(60, 4.8%, 95.9%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;

  /* Modern AI Safety Design System */
  --ai-primary: hsl(220, 100%, 60%);
  --ai-secondary: hsl(280, 100%, 70%);
  --ai-accent: hsl(180, 100%, 50%);
  --ai-neural: hsl(260, 100%, 65%);

  /* Safety-specific color variables - Enhanced */
  --safety-blue: hsl(207, 90%, 44%);
  --safety-red: hsl(0, 76%, 50%);
  --safety-orange: hsl(36, 100%, 47%);
  --safety-green: hsl(134, 41%, 44%);
  --safety-purple: hsl(280, 70%, 55%);
  --safety-cyan: hsl(180, 70%, 45%);

  /* Glassmorphism variables */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* Neural network colors */
  --neural-node: hsl(260, 100%, 65%);
  --neural-connection: hsl(280, 80%, 60%);
  --neural-active: hsl(180, 100%, 50%);

  /* Gradient backgrounds */
  --gradient-ai: linear-gradient(135deg, hsl(220, 100%, 60%) 0%, hsl(280, 100%, 70%) 100%);
  --gradient-safety: linear-gradient(135deg, hsl(134, 41%, 44%) 0%, hsl(207, 90%, 44%) 100%);
  --gradient-neural: linear-gradient(135deg, hsl(260, 100%, 65%) 0%, hsl(180, 100%, 50%) 100%);

  --surface: hsl(60, 4.8%, 98%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
  
  /* Dark mode safety colors */
  --safety-blue: hsl(207, 90%, 64%);
  --safety-red: hsl(0, 76%, 60%);
  --safety-orange: hsl(36, 100%, 57%);
  --safety-green: hsl(134, 41%, 54%);
  --surface: hsl(240, 10%, 6%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }
}

@layer utilities {
  /* Modern AI Safety Color System */
  .ai-primary { color: var(--ai-primary); }
  .bg-ai-primary { background-color: var(--ai-primary); }
  .ai-secondary { color: var(--ai-secondary); }
  .bg-ai-secondary { background-color: var(--ai-secondary); }
  .ai-accent { color: var(--ai-accent); }
  .bg-ai-accent { background-color: var(--ai-accent); }
  .ai-neural { color: var(--ai-neural); }
  .bg-ai-neural { background-color: var(--ai-neural); }

  /* Enhanced Safety Colors */
  .safety-blue { color: var(--safety-blue); }
  .bg-safety-blue { background-color: var(--safety-blue); }
  .safety-red { color: var(--safety-red); }
  .bg-safety-red { background-color: var(--safety-red); }
  .safety-orange { color: var(--safety-orange); }
  .bg-safety-orange { background-color: var(--safety-orange); }
  .safety-green { color: var(--safety-green); }
  .bg-safety-green { background-color: var(--safety-green); }
  .safety-purple { color: var(--safety-purple); }
  .bg-safety-purple { background-color: var(--safety-purple); }
  .safety-cyan { color: var(--safety-cyan); }
  .bg-safety-cyan { background-color: var(--safety-cyan); }

  /* Gradient Backgrounds */
  .bg-gradient-ai { background: var(--gradient-ai); }
  .bg-gradient-safety { background: var(--gradient-safety); }
  .bg-gradient-neural { background: var(--gradient-neural); }

  /* Glassmorphism Effects */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .glass-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    border-radius: 16px;
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .bg-surface { background-color: var(--surface); }

  /* Enhanced Animations */
  .animate-pulse-green {
    animation: pulse-green 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-pulse-ai {
    animation: pulse-ai 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-neural-flow {
    animation: neural-flow 4s linear infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes pulse-green {
    0%, 100% {
      opacity: 1;
      background-color: var(--safety-green);
    }
    50% {
      opacity: .5;
      background-color: var(--safety-green);
    }
  }

  @keyframes pulse-ai {
    0%, 100% {
      opacity: 1;
      box-shadow: 0 0 20px var(--ai-primary);
    }
    50% {
      opacity: 0.8;
      box-shadow: 0 0 40px var(--ai-secondary);
    }
  }

  @keyframes neural-flow {
    0% { transform: translateX(-100%); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(100%); opacity: 0; }
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 10px var(--ai-accent);
    }
    to {
      box-shadow: 0 0 20px var(--ai-accent), 0 0 30px var(--ai-accent);
    }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Mobile-specific optimizations */
  @media (max-width: 768px) {
    .mobile-safe-area {
      padding-top: env(safe-area-inset-top);
      padding-bottom: env(safe-area-inset-bottom);
    }
  }

  /* Enhanced Camera overlay styles */
  .camera-overlay {
    backdrop-filter: blur(8px);
    background: rgba(0, 0, 0, 0.85);
  }

  .ai-camera-overlay {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(31, 38, 135, 0.6) 100%);
    backdrop-filter: blur(12px);
  }

  /* Enhanced Detection Boxes */
  .detection-box {
    border: 3px solid;
    border-radius: 8px;
    position: absolute;
    pointer-events: none;
    animation: detection-pulse 2s infinite;
  }

  .detection-box.ppe-violation {
    border-color: var(--safety-red);
    background: rgba(211, 47, 47, 0.15);
    box-shadow: 0 0 15px rgba(211, 47, 47, 0.5);
  }

  .detection-box.ppe-compliant {
    border-color: var(--safety-green);
    background: rgba(56, 142, 60, 0.15);
    box-shadow: 0 0 15px rgba(56, 142, 60, 0.5);
  }

  .detection-box.hazard {
    border-color: var(--safety-orange);
    background: rgba(245, 124, 0, 0.15);
    box-shadow: 0 0 15px rgba(245, 124, 0, 0.5);
  }

  .detection-box.ai-enhanced {
    border-image: linear-gradient(45deg, var(--ai-primary), var(--ai-secondary)) 1;
    box-shadow: 0 0 20px var(--ai-accent);
  }

  @keyframes detection-pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.02); }
  }

  /* Dynamic width utilities */
  .progress-bar {
    transition: width 1s ease-in-out;
  }

  .progress-bar[data-width="0"] { width: 0%; }
  .progress-bar[data-width="10"] { width: 10%; }
  .progress-bar[data-width="20"] { width: 20%; }
  .progress-bar[data-width="30"] { width: 30%; }
  .progress-bar[data-width="40"] { width: 40%; }
  .progress-bar[data-width="50"] { width: 50%; }
  .progress-bar[data-width="60"] { width: 60%; }
  .progress-bar[data-width="70"] { width: 70%; }
  .progress-bar[data-width="80"] { width: 80%; }
  .progress-bar[data-width="90"] { width: 90%; }
  .progress-bar[data-width="100"] { width: 100%; }

  /* Dynamic width for any percentage */
  .progress-bar {
    width: calc(var(--progress-width, 0) * 1%);
  }


}
