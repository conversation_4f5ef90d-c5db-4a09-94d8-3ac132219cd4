import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { AIAnalysis } from '@/types/safety';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';

interface IncidentModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageData?: string;
  analysis?: AIAnalysis;
  userId: string;
}

export default function IncidentModal({
  isOpen,
  onClose,
  imageData,
  analysis,
  userId,
}: IncidentModalProps) {
  const [severity, setSeverity] = useState<string>('');
  const [description, setDescription] = useState('');
  const [notes, setNotes] = useState('');

  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { coords } = useGeolocation();

  const createIncidentMutation = useMutation({
    mutationFn: async (data: any) => {
      return await apiRequest('POST', '/api/incidents', data);
    },
    onSuccess: () => {
      toast({
        title: 'Incident Reported',
        description: 'Your incident report has been submitted successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/incidents'] });
      queryClient.invalidateQueries({ queryKey: ['/api/dashboard'] });
      onClose();
      resetForm();
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to submit incident report. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const resetForm = () => {
    setSeverity('');
    setDescription('');
    setNotes('');
  };

  const handleSubmit = () => {
    if (!severity || !description) {
      toast({
        title: 'Missing Information',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    const incidentData = {
      userId,
      type: analysis?.detections?.[0]?.type || 'GENERAL_HAZARD',
      severity: severity.toUpperCase(),
      description,
      notes,
      location: 'Current Location', // You could get this from a location service
      gpsCoordinates: coords
        ? {
            latitude: coords.latitude,
            longitude: coords.longitude,
          }
        : null,
      imageUrl: imageData,
      aiAnalysis: analysis,
      status: 'open',
    };

    createIncidentMutation.mutate(incidentData);
  };

  // Auto-populate description based on AI analysis
  useState(() => {
    if (analysis && analysis.detections.length > 0) {
      const primaryDetection = analysis.detections[0];
      setDescription(primaryDetection.description);

      // Auto-set severity based on AI risk level
      if (analysis.riskLevel === 'HIGH') {
        setSeverity('high');
      } else if (analysis.riskLevel === 'MEDIUM') {
        setSeverity('medium');
      } else {
        setSeverity('low');
      }
    }
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-full max-w-md mx-auto bottom-0 fixed translate-y-0 translate-x-[-50%] left-1/2 rounded-t-3xl rounded-b-none border-0 p-0">
        <div className="p-6">
          <div className="w-8 h-1 bg-gray-300 rounded-full mx-auto mb-6"></div>

          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-gray-900 mb-4">
              Report Incident
            </DialogTitle>
          </DialogHeader>

          {/* Incident Photo */}
          {imageData && (
            <div className="relative bg-gray-100 rounded-xl h-48 mb-4 overflow-hidden">
              <img src={imageData} alt="Incident capture" className="w-full h-full object-cover" />
              {analysis?.riskLevel && (
                <div
                  className={`absolute top-2 right-2 px-2 py-1 rounded text-xs font-medium ${
                    analysis.riskLevel === 'HIGH'
                      ? 'bg-safety-red text-white'
                      : analysis.riskLevel === 'MEDIUM'
                        ? 'bg-safety-orange text-white'
                        : 'bg-safety-green text-white'
                  }`}
                >
                  {analysis.riskLevel} RISK
                </div>
              )}
            </div>
          )}

          {/* AI Analysis Results */}
          {analysis && analysis.detections.length > 0 && (
            <div className="bg-red-50 border border-safety-red rounded-lg p-4 mb-4">
              <h4 className="font-semibold text-safety-red mb-2">AI Detection Results:</h4>
              <ul className="space-y-1 text-sm">
                {analysis.detections.map((detection, index) => (
                  <li key={index} className="flex items-center space-x-2">
                    <span
                      className={`text-xs ${
                        detection.type === 'PPE_VIOLATION' ? 'text-safety-red' : 'text-safety-green'
                      }`}
                    >
                      {detection.type === 'PPE_VIOLATION' ? '✗' : '✓'}
                    </span>
                    <span>{detection.description}</span>
                    <span className="text-gray-500">
                      ({Math.round(detection.confidence * 100)}%)
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Report Form */}
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Severity Level *
              </label>
              <Select value={severity} onValueChange={setSeverity}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select severity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">High Risk</SelectItem>
                  <SelectItem value="medium">Medium Risk</SelectItem>
                  <SelectItem value="low">Low Risk</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Description *</label>
              <Textarea
                value={description}
                onChange={e => setDescription(e.target.value)}
                placeholder="Describe the safety incident..."
                className="resize-none"
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Additional Notes
              </label>
              <Textarea
                value={notes}
                onChange={e => setNotes(e.target.value)}
                placeholder="Add any additional details..."
                className="resize-none"
                rows={3}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={createIncidentMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              className="flex-1 bg-safety-red hover:bg-red-600"
              disabled={createIncidentMutation.isPending}
            >
              {createIncidentMutation.isPending ? 'Submitting...' : 'Submit Report'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
