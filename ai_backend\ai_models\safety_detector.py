"""
Main Safety Detector
Coordinates all safety detection models and provides unified analysis
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import numpy as np
import cv2

logger = logging.getLogger(__name__)

class SafetyDetector:
    """
    Main safety detection coordinator that combines results from all specialized models
    """
    
    def __init__(self, model_path: Path):
        self.model_path = model_path
        self.model_path.mkdir(exist_ok=True)
        
        # Risk assessment weights
        self.risk_weights = {
            "PPE_VIOLATION": 0.8,
            "FALL_DETECTED": 1.0,
            "HAZARD": 0.7,
            "ZONE_VIOLATION": 0.6,
            "VEHICLE_PROXIMITY": 0.9,
            "TOOLBOX_HAZARD": 0.5
        }
        
        # Severity multipliers
        self.severity_multipliers = {
            "HIGH": 1.0,
            "MEDIUM": 0.6,
            "LOW": 0.3
        }
        
        self.performance_metrics = {
            "total_analyses": 0,
            "avg_processing_time": 0.0,
            "risk_assessments": []
        }
    
    async def load_model(self):
        """Load main safety detection model"""
        logger.info("Safety detector coordinator initialized")
    
    def calculate_risk_assessment(self, detections: List[Dict]) -> Tuple[str, float]:
        """
        Calculate overall risk level and confidence from all detections
        
        Args:
            detections: List of detection results from all models
            
        Returns:
            Tuple of (risk_level, overall_confidence)
        """
        if not detections:
            return "LOW", 1.0
        
        # Calculate weighted risk score
        total_risk_score = 0.0
        total_weight = 0.0
        confidence_scores = []
        
        for detection in detections:
            detection_type = detection.get("type", "UNKNOWN")
            severity = detection.get("severity", "MEDIUM")
            confidence = detection.get("confidence", 0.5)
            
            # Get weights
            type_weight = self.risk_weights.get(detection_type, 0.5)
            severity_multiplier = self.severity_multipliers.get(severity, 0.6)
            
            # Calculate weighted score
            detection_score = confidence * type_weight * severity_multiplier
            total_risk_score += detection_score
            total_weight += type_weight
            confidence_scores.append(confidence)
        
        # Normalize risk score
        if total_weight > 0:
            normalized_risk = total_risk_score / total_weight
        else:
            normalized_risk = 0.0
        
        # Calculate overall confidence
        overall_confidence = np.mean(confidence_scores) if confidence_scores else 0.0
        
        # Determine risk level
        if normalized_risk >= 0.7:
            risk_level = "HIGH"
        elif normalized_risk >= 0.4:
            risk_level = "MEDIUM"
        else:
            risk_level = "LOW"
        
        # Store for analytics
        self.performance_metrics["risk_assessments"].append({
            "risk_score": normalized_risk,
            "risk_level": risk_level,
            "detection_count": len(detections),
            "timestamp": time.time()
        })
        
        return risk_level, overall_confidence
    
    async def generate_insights(self, detections: List[Dict], image: np.ndarray) -> Dict[str, Any]:
        """
        Generate AI insights from detection results
        
        Args:
            detections: List of detection results
            image: Original image for context analysis
            
        Returns:
            Dictionary of AI insights and recommendations
        """
        insights = {
            "detection_summary": self._generate_detection_summary(detections),
            "risk_factors": self._identify_risk_factors(detections),
            "spatial_analysis": await self._analyze_spatial_distribution(detections, image),
            "temporal_patterns": self._analyze_temporal_patterns(),
            "predictive_insights": await self._generate_predictive_insights(detections),
            "compliance_score": self._calculate_compliance_score(detections)
        }
        
        return insights
    
    def generate_recommendations(self, detections: List[Dict]) -> List[str]:
        """
        Generate actionable recommendations based on detections
        
        Args:
            detections: List of detection results
            
        Returns:
            List of prioritized recommendations
        """
        recommendations = []
        
        # Categorize detections
        ppe_violations = [d for d in detections if d.get("type") == "PPE_VIOLATION"]
        hazards = [d for d in detections if d.get("type") == "HAZARD"]
        falls = [d for d in detections if d.get("type") == "FALL_DETECTED"]
        zone_violations = [d for d in detections if d.get("type") == "ZONE_VIOLATION"]
        
        # Priority 1: Immediate safety threats
        if falls:
            recommendations.extend([
                "🚨 IMMEDIATE: Respond to fall incident - check for injuries",
                "📞 Contact emergency services if person is unconscious",
                "🚫 Do not move injured person unless in immediate danger"
            ])
        
        # Priority 2: High-risk hazards
        high_risk_hazards = [h for h in hazards if h.get("severity") == "HIGH"]
        if high_risk_hazards:
            recommendations.extend([
                "⚠️ URGENT: Address high-risk hazards immediately",
                "🔒 Secure affected areas and restrict access",
                "👥 Notify all personnel in the vicinity"
            ])
        
        # Priority 3: PPE compliance
        if ppe_violations:
            recommendations.extend([
                "🦺 Ensure all workers wear required PPE",
                "📋 Conduct immediate PPE compliance check",
                "🎓 Provide additional safety training if needed"
            ])
        
        # Priority 4: Zone violations
        if zone_violations:
            recommendations.extend([
                "🚧 Enforce zone access restrictions",
                "📍 Verify proper signage and barriers",
                "🔍 Review access control procedures"
            ])
        
        # General recommendations
        if detections:
            recommendations.extend([
                "📊 Document all incidents for safety records",
                "🔄 Review and update safety procedures",
                "📈 Schedule follow-up safety inspection"
            ])
        
        return recommendations[:10]  # Limit to top 10 recommendations
    
    async def process_live_frame(self, frame: np.ndarray) -> List[Dict]:
        """
        Process a single frame for live detection
        This would typically call all detection models in parallel
        """
        # This is a placeholder - in the full implementation,
        # this would coordinate with all detection models
        return []
    
    def _generate_detection_summary(self, detections: List[Dict]) -> Dict[str, Any]:
        """Generate summary of detections"""
        summary = {
            "total_detections": len(detections),
            "by_type": {},
            "by_severity": {"HIGH": 0, "MEDIUM": 0, "LOW": 0},
            "average_confidence": 0.0
        }
        
        confidences = []
        
        for detection in detections:
            # Count by type
            detection_type = detection.get("type", "UNKNOWN")
            summary["by_type"][detection_type] = summary["by_type"].get(detection_type, 0) + 1
            
            # Count by severity
            severity = detection.get("severity", "MEDIUM")
            if severity in summary["by_severity"]:
                summary["by_severity"][severity] += 1
            
            # Collect confidences
            confidence = detection.get("confidence", 0.0)
            confidences.append(confidence)
        
        # Calculate average confidence
        if confidences:
            summary["average_confidence"] = np.mean(confidences)
        
        return summary
    
    def _identify_risk_factors(self, detections: List[Dict]) -> List[Dict[str, Any]]:
        """Identify key risk factors from detections"""
        risk_factors = []
        
        # Analyze detection patterns
        detection_types = [d.get("type") for d in detections]
        
        # Multiple PPE violations indicate systemic issue
        ppe_count = detection_types.count("PPE_VIOLATION")
        if ppe_count >= 3:
            risk_factors.append({
                "factor": "Systemic PPE Non-Compliance",
                "severity": "HIGH",
                "description": f"{ppe_count} PPE violations detected",
                "recommendation": "Immediate safety training and enforcement required"
            })
        
        # Multiple hazards in same area
        hazard_count = detection_types.count("HAZARD")
        if hazard_count >= 2:
            risk_factors.append({
                "factor": "Multiple Environmental Hazards",
                "severity": "MEDIUM",
                "description": f"{hazard_count} hazards in proximity",
                "recommendation": "Comprehensive area safety review needed"
            })
        
        # Fall with other violations
        if "FALL_DETECTED" in detection_types and len(detections) > 1:
            risk_factors.append({
                "factor": "Fall in Unsafe Environment",
                "severity": "HIGH",
                "description": "Fall occurred with other safety violations present",
                "recommendation": "Investigate contributing factors to fall"
            })
        
        return risk_factors
    
    async def _analyze_spatial_distribution(self, detections: List[Dict], image: np.ndarray) -> Dict[str, Any]:
        """Analyze spatial distribution of detections"""
        if not detections:
            return {"clusters": [], "hotspots": []}
        
        # Extract detection centers
        centers = []
        for detection in detections:
            bbox = detection.get("bounding_box", {})
            if bbox:
                center_x = bbox.get("x", 0) + bbox.get("width", 0) // 2
                center_y = bbox.get("y", 0) + bbox.get("height", 0) // 2
                centers.append([center_x, center_y])
        
        if len(centers) < 2:
            return {"clusters": [], "hotspots": []}
        
        # Simple clustering analysis
        centers = np.array(centers)
        
        # Find clusters using distance threshold
        clusters = []
        cluster_threshold = 100  # pixels
        
        for i, center in enumerate(centers):
            # Find nearby detections
            distances = np.linalg.norm(centers - center, axis=1)
            nearby_indices = np.where(distances < cluster_threshold)[0]
            
            if len(nearby_indices) > 1:
                cluster_detections = [detections[j] for j in nearby_indices]
                clusters.append({
                    "center": center.tolist(),
                    "detection_count": len(nearby_indices),
                    "detections": cluster_detections
                })
        
        return {
            "clusters": clusters,
            "hotspots": [c for c in clusters if c["detection_count"] >= 3]
        }
    
    def _analyze_temporal_patterns(self) -> Dict[str, Any]:
        """Analyze temporal patterns in risk assessments"""
        recent_assessments = self.performance_metrics["risk_assessments"][-10:]
        
        if len(recent_assessments) < 2:
            return {"trend": "insufficient_data"}
        
        # Calculate trend
        risk_scores = [a["risk_score"] for a in recent_assessments]
        
        if len(risk_scores) >= 3:
            # Simple trend analysis
            recent_avg = np.mean(risk_scores[-3:])
            earlier_avg = np.mean(risk_scores[:-3]) if len(risk_scores) > 3 else risk_scores[0]
            
            if recent_avg > earlier_avg * 1.2:
                trend = "increasing"
            elif recent_avg < earlier_avg * 0.8:
                trend = "decreasing"
            else:
                trend = "stable"
        else:
            trend = "stable"
        
        return {
            "trend": trend,
            "recent_average_risk": np.mean(risk_scores),
            "assessment_count": len(recent_assessments)
        }
    
    async def _generate_predictive_insights(self, detections: List[Dict]) -> Dict[str, Any]:
        """Generate predictive insights based on current detections"""
        insights = {
            "incident_probability": 0.0,
            "risk_escalation": "low",
            "preventive_actions": []
        }
        
        # Calculate incident probability based on detection patterns
        high_severity_count = len([d for d in detections if d.get("severity") == "HIGH"])
        total_detections = len(detections)
        
        if total_detections > 0:
            severity_ratio = high_severity_count / total_detections
            
            # Simple probability model
            if severity_ratio >= 0.5:
                insights["incident_probability"] = 0.8
                insights["risk_escalation"] = "high"
            elif severity_ratio >= 0.3:
                insights["incident_probability"] = 0.5
                insights["risk_escalation"] = "medium"
            else:
                insights["incident_probability"] = 0.2
                insights["risk_escalation"] = "low"
        
        # Generate preventive actions
        if insights["incident_probability"] > 0.6:
            insights["preventive_actions"] = [
                "Increase safety supervision",
                "Implement additional safety measures",
                "Consider temporary work stoppage"
            ]
        elif insights["incident_probability"] > 0.3:
            insights["preventive_actions"] = [
                "Enhanced safety monitoring",
                "Additional safety briefings",
                "Review current procedures"
            ]
        
        return insights
    
    def _calculate_compliance_score(self, detections: List[Dict]) -> float:
        """Calculate overall safety compliance score"""
        if not detections:
            return 100.0
        
        # Start with perfect score
        score = 100.0
        
        # Deduct points for violations
        for detection in detections:
            detection_type = detection.get("type", "")
            severity = detection.get("severity", "MEDIUM")
            
            # Deduction based on type and severity
            if detection_type == "PPE_VIOLATION":
                deduction = 15 if severity == "HIGH" else 10 if severity == "MEDIUM" else 5
            elif detection_type == "FALL_DETECTED":
                deduction = 25
            elif detection_type == "HAZARD":
                deduction = 20 if severity == "HIGH" else 12 if severity == "MEDIUM" else 6
            elif detection_type == "ZONE_VIOLATION":
                deduction = 10 if severity == "HIGH" else 6 if severity == "MEDIUM" else 3
            else:
                deduction = 8 if severity == "HIGH" else 5 if severity == "MEDIUM" else 2
            
            score -= deduction
        
        return max(0.0, score)
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return self.performance_metrics.copy()
    
    async def cleanup(self):
        """Cleanup resources"""
        logger.info("Safety detector cleanup complete")
