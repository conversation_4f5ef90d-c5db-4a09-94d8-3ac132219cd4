# AI-Powered Mobile Safety Assistant

## Overview

This is a full-stack mobile web application designed to help safety managers and inspectors detect, log, and report safety hazards in real-time using AI-powered detection capabilities. The application enables users to perform live hazard detection through mobile camera feeds, conduct photo-based safety checks, manage PPE compliance, record voice reports, and maintain comprehensive safety documentation.

The system focuses on proactive safety management through multiple AI modules including PPE detection, fall detection, zone violation monitoring, and toolbox/tool management tracking. Users can capture incidents with photo, video, and voice input while the AI provides real-time analysis and risk assessment.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript, built using Vite for fast development and optimized production builds
- **Styling**: Tailwind CSS with custom safety-themed color palette and shadcn/ui component library
- **Mobile-First Design**: Responsive layout optimized for mobile devices with touch-friendly interfaces
- **State Management**: TanStack Query (React Query) for server state management and caching
- **Routing**: Wouter for lightweight client-side routing
- **UI Components**: Radix UI primitives with custom styling for accessibility

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript for type safety across the stack
- **API Design**: RESTful API with structured endpoints for incidents, safety checks, reports, and user management
- **File Structure**: Modular architecture with separate routing, storage, and database layers
- **Development Setup**: Hot reloading with Vite integration for seamless development experience

### Data Storage Solutions
- **Primary Database**: PostgreSQL via Neon serverless database
- **ORM**: Drizzle ORM for type-safe database operations and schema management
- **Schema Design**: Comprehensive safety-focused data models including users, incidents, safety checks, toolbox talks, documents, and alerts
- **Migration Management**: Drizzle-kit for database schema migrations and version control

### Authentication and Authorization
- **User System**: Role-based access control with inspector, manager, and admin roles
- **Session Management**: Express sessions with PostgreSQL session store (connect-pg-simple)
- **User Context**: Mock user system currently implemented for development purposes

### External Service Integrations
- **AI Detection Services**: Placeholder services for image analysis, voice processing, and real-time hazard detection
- **Geolocation**: Browser-based GPS coordinate capture for incident location tracking
- **Media Capture**: WebRTC APIs for camera access, photo capture, and video recording
- **Voice Recognition**: Web Speech API integration for voice-to-text incident reporting

### Key Features and Modules
- **Live Detection**: Real-time camera-based hazard detection with AI analysis
- **Photo Check**: Single-image safety analysis and compliance checking  
- **PPE Detection**: Personal Protective Equipment compliance monitoring
- **Voice Reporting**: Speech-to-text incident reporting with AI classification
- **Incident Management**: Comprehensive incident tracking with status management
- **Document Management**: Safety document storage and categorization
- **Toolbox Talks**: Safety briefing scheduling and attendance tracking
- **Reporting System**: Dashboard analytics and exportable safety reports

### Mobile-Specific Design Decisions
- **Camera Integration**: Custom camera overlay component with real-time detection feedback
- **Touch Interfaces**: Large, touch-friendly buttons and intuitive gesture support
- **Offline Considerations**: Local storage capabilities for continued operation without connectivity
- **Performance Optimization**: Image compression and efficient API calls for mobile data usage
- **Progressive Web App**: Service worker ready for offline functionality and app-like experience

## External Dependencies

### Core Runtime Dependencies
- **@neondatabase/serverless**: Neon PostgreSQL serverless database client
- **drizzle-orm**: TypeScript ORM for database operations
- **express**: Web application framework for Node.js
- **react**: UI library for building user interfaces
- **@tanstack/react-query**: Data fetching and caching library

### UI and Styling
- **@radix-ui/***: Comprehensive set of accessible UI primitives
- **tailwindcss**: Utility-first CSS framework
- **class-variance-authority**: Utility for creating variant-based component APIs
- **lucide-react**: Icon library with consistent design

### Development and Build Tools
- **vite**: Fast build tool and development server
- **typescript**: Static type checking
- **tsx**: TypeScript execution environment
- **esbuild**: JavaScript bundler for production builds

### Mobile and Media APIs
- **Web APIs**: MediaDevices API for camera access, Geolocation API for GPS, Web Speech API for voice recognition
- **PWA Support**: Service workers and manifest for app-like mobile experience

### Database and Session Management
- **connect-pg-simple**: PostgreSQL session store for Express
- **drizzle-kit**: Database migration and schema management tools
- **drizzle-zod**: Zod integration for runtime type validation

### Form Handling and Validation
- **react-hook-form**: Performant forms library
- **@hookform/resolvers**: Validation resolvers for react-hook-form
- **zod**: TypeScript-first schema validation