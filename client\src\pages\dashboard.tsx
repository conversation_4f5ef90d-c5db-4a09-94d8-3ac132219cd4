import CameraOverlay from '@/components/camera-overlay';
import IncidentModal from '@/components/incident-modal';
import MobileLayout from '@/components/mobile-layout';
import Navigation from '@/components/navigation';
import SafetyStats from '@/components/safety-stats';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { SafetyModule } from '@/types/safety';
import { useQuery } from '@tanstack/react-query';
import {
  AlertTriangle,
  Brain,
  Camera,
  Download,
  ExternalLink,
  FileText,
  HardHat,
  Mic,
  Users,
  Video,
} from 'lucide-react';
import { useState } from 'react';
import { Link } from 'wouter';

// Mock user ID - in a real app, this would come from auth context
const MOCK_USER_ID = 'mock-user-123';

export default function Dashboard() {
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [isIncidentModalOpen, setIsIncidentModalOpen] = useState(false);
  const [capturedImageData, setCapturedImageData] = useState<string>();
  const [analysisData, setAnalysisData] = useState<any>();

  const { toast } = useToast();

  const { data: incidents = [] } = useQuery({
    queryKey: ['/api/incidents', MOCK_USER_ID, 3],
  });

  const safetyModules: SafetyModule[] = [
    {
      id: 'ai-dashboard',
      name: 'AI Command Center',
      icon: 'brain',
      color: 'ai-primary',
      description: 'Neural network control center',
      route: '/ai-dashboard',
      isActive: true,
    },
    {
      id: 'live-detection',
      name: 'Live Detection',
      icon: 'video',
      color: 'safety-green',
      description: 'Real-time hazard detection',
      route: '/live',
      isActive: true,
    },
    {
      id: 'photo-check',
      name: 'Photo Check',
      icon: 'camera',
      color: 'safety-blue',
      description: 'AI Analysis',
      route: '/photo-check',
    },
    {
      id: 'ppe-detection',
      name: 'PPE Detection',
      icon: 'hard-hat',
      color: 'safety-orange',
      description: 'Equipment Check',
      route: '/ppe-detection',
    },
    {
      id: 'voice-report',
      name: 'Voice Report',
      icon: 'microphone',
      color: 'purple-500',
      description: 'Talk2Report',
      route: '/voice-report',
    },
  ];

  const handleCameraCapture = (imageData: string, analysis?: any) => {
    setCapturedImageData(imageData);
    setAnalysisData(analysis);
    setIsCameraOpen(false);
    setIsIncidentModalOpen(true);
  };

  const handleQuickReport = () => {
    setIsCameraOpen(false);
    setIsIncidentModalOpen(true);
  };

  const handleEmergencyAlert = () => {
    toast({
      title: 'Emergency Alert Sent',
      description: 'Safety team has been notified of the emergency situation.',
      variant: 'destructive',
    });
  };

  const getSeverityColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'high':
        return 'border-safety-red bg-red-50';
      case 'medium':
        return 'border-safety-orange bg-orange-50';
      case 'low':
        return 'border-safety-green bg-green-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getSeverityBadgeColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'high':
        return 'bg-safety-red text-white';
      case 'medium':
        return 'bg-safety-orange text-white';
      case 'low':
        return 'bg-safety-green text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getModuleIcon = (iconName: string) => {
    switch (iconName) {
      case 'video':
        return Video;
      case 'camera':
        return Camera;
      case 'hard-hat':
        return HardHat;
      case 'microphone':
        return Mic;
      case 'brain':
        return Brain;
      default:
        return Camera;
    }
  };

  return (
    <>
      <MobileLayout
        title="AI Safety Assistant"
        subtitle="Building A - Level 2"
        showLiveIndicator={true}
        onEmergencyAlert={handleEmergencyAlert}
      >
        {/* Safety Stats */}
        <SafetyStats userId={MOCK_USER_ID} />

        {/* AI Safety Modules */}
        <section className="px-4 py-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900">Safety Modules</h3>

          <div className="grid grid-cols-2 gap-4 mb-6">
            {safetyModules.map(module => {
              const IconComponent = getModuleIcon(module.icon);
              const isLiveDetection = module.id === 'live-detection';

              if (isLiveDetection) {
                return (
                  <button
                    key={module.id}
                    onClick={() => setIsCameraOpen(true)}
                    className={`bg-white rounded-xl shadow-md p-4 border-2 border-safety-green transition-all hover:shadow-lg`}
                  >
                    <div className="flex flex-col items-center text-center space-y-2">
                      <div className="w-12 h-12 bg-safety-green rounded-full flex items-center justify-center">
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <h4 className="font-semibold text-sm">{module.name}</h4>
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-safety-green rounded-full animate-pulse"></div>
                        <span className="text-xs text-safety-green">Active</span>
                      </div>
                    </div>
                  </button>
                );
              }

              return (
                <Link key={module.id} href={module.route}>
                  <button className="bg-white rounded-xl shadow-md p-4 border border-gray-200 transition-all hover:shadow-lg w-full">
                    <div className="flex flex-col items-center text-center space-y-2">
                      <div
                        className={`w-12 h-12 bg-${module.color} rounded-full flex items-center justify-center`}
                      >
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <h4 className="font-semibold text-sm">{module.name}</h4>
                      <span className="text-xs text-gray-600">{module.description}</span>
                    </div>
                  </button>
                </Link>
              );
            })}
          </div>

          {/* Recent Incidents */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-semibold text-gray-900">Recent Incidents</h4>
                <Link href="/incidents">
                  <Button variant="link" size="sm" className="text-safety-blue p-0">
                    View All <ExternalLink className="w-4 h-4 ml-1" />
                  </Button>
                </Link>
              </div>

              <div className="space-y-3">
                {(incidents as any[]).length === 0 ? (
                  <div className="text-center py-6 text-gray-500">
                    <AlertTriangle className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                    <p className="text-sm">No recent incidents</p>
                  </div>
                ) : (
                  (incidents as any[]).map((incident: any) => (
                    <div
                      key={incident.id}
                      className={`flex items-center space-x-3 p-3 rounded-lg border-l-4 ${getSeverityColor(incident.severity)}`}
                    >
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                          incident.severity === 'high'
                            ? 'bg-safety-red'
                            : incident.severity === 'medium'
                              ? 'bg-safety-orange'
                              : 'bg-safety-green'
                        }`}
                      >
                        <AlertTriangle className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {incident.description}
                        </p>
                        <p className="text-xs text-gray-600">
                          {incident.location || 'Unknown Location'} •{' '}
                          {new Date(incident.createdAt).toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit',
                          })}
                        </p>
                      </div>
                      <span
                        className={`text-xs px-2 py-1 rounded-full font-medium ${getSeverityBadgeColor(incident.severity)}`}
                      >
                        {incident.severity?.toUpperCase()}
                      </span>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardContent className="p-4">
              <h4 className="font-semibold text-gray-900 mb-4">Quick Actions</h4>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  onClick={handleEmergencyAlert}
                  className="flex items-center justify-center space-x-2 bg-safety-blue hover:bg-blue-600 text-white py-3 px-4 rounded-lg"
                >
                  <AlertTriangle className="w-5 h-5" />
                  <span>Emergency</span>
                </Button>
                <Link href="/reports">
                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center space-x-2 py-3 px-4"
                  >
                    <Download className="w-5 h-5" />
                    <span>Export</span>
                  </Button>
                </Link>
                <Link href="/toolbox-talk">
                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center space-x-2 py-3 px-4"
                  >
                    <Users className="w-5 h-5" />
                    <span>Toolbox Talk</span>
                  </Button>
                </Link>
                <Link href="/documents">
                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center space-x-2 py-3 px-4"
                  >
                    <FileText className="w-5 h-5" />
                    <span>Documents</span>
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </section>
      </MobileLayout>

      <Navigation />

      <CameraOverlay
        isOpen={isCameraOpen}
        onClose={() => setIsCameraOpen(false)}
        onCapture={handleCameraCapture}
        onQuickReport={handleQuickReport}
      />

      <IncidentModal
        isOpen={isIncidentModalOpen}
        onClose={() => setIsIncidentModalOpen(false)}
        imageData={capturedImageData}
        analysis={analysisData}
        userId={MOCK_USER_ID}
      />
    </>
  );
}
