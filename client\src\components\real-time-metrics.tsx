import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Activity, 
  Cpu, 
  Database, 
  Wifi, 
  Zap,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';

interface MetricData {
  id: string;
  name: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  change: number;
  color: string;
  icon: string;
  history: number[];
}

export default function RealTimeMetrics() {
  const [metrics, setMetrics] = useState<MetricData[]>([]);
  const [isConnected, setIsConnected] = useState(true);

  useEffect(() => {
    initializeMetrics();
    
    // Simulate real-time updates
    const interval = setInterval(updateMetrics, 2000);
    
    // Simulate connection status
    const connectionInterval = setInterval(() => {
      setIsConnected(Math.random() > 0.1); // 90% uptime
    }, 10000);
    
    return () => {
      clearInterval(interval);
      clearInterval(connectionInterval);
    };
  }, []);

  const initializeMetrics = () => {
    const initialMetrics: MetricData[] = [
      {
        id: 'cpu',
        name: 'AI Processing',
        value: 67.3,
        unit: '%',
        trend: 'stable',
        change: 0.2,
        color: 'ai-primary',
        icon: 'cpu',
        history: [65, 66, 67, 68, 67]
      },
      {
        id: 'memory',
        name: 'Memory Usage',
        value: 4.2,
        unit: 'GB',
        trend: 'up',
        change: 0.3,
        color: 'safety-orange',
        icon: 'database',
        history: [3.8, 3.9, 4.0, 4.1, 4.2]
      },
      {
        id: 'throughput',
        name: 'Detection Rate',
        value: 2847,
        unit: '/min',
        trend: 'up',
        change: 12.5,
        color: 'safety-green',
        icon: 'activity',
        history: [2650, 2720, 2780, 2820, 2847]
      },
      {
        id: 'latency',
        name: 'Response Time',
        value: 156,
        unit: 'ms',
        trend: 'down',
        change: -8.2,
        color: 'safety-cyan',
        icon: 'zap',
        history: [180, 175, 170, 165, 156]
      },
      {
        id: 'accuracy',
        name: 'Model Accuracy',
        value: 94.7,
        unit: '%',
        trend: 'up',
        change: 0.8,
        color: 'safety-purple',
        icon: 'activity',
        history: [93.2, 93.8, 94.1, 94.4, 94.7]
      },
      {
        id: 'bandwidth',
        name: 'Network Usage',
        value: 234,
        unit: 'Mbps',
        trend: 'stable',
        change: -1.2,
        color: 'ai-secondary',
        icon: 'wifi',
        history: [240, 238, 236, 235, 234]
      }
    ];

    setMetrics(initialMetrics);
  };

  const updateMetrics = () => {
    setMetrics(prevMetrics => 
      prevMetrics.map(metric => {
        // Simulate realistic value changes
        const variation = (Math.random() - 0.5) * 0.1;
        let newValue = metric.value + (metric.value * variation);
        
        // Apply constraints based on metric type
        if (metric.id === 'cpu' || metric.id === 'accuracy') {
          newValue = Math.max(0, Math.min(100, newValue));
        } else if (metric.id === 'memory') {
          newValue = Math.max(0, Math.min(8, newValue));
        } else if (metric.id === 'latency') {
          newValue = Math.max(50, Math.min(500, newValue));
        } else if (metric.id === 'throughput') {
          newValue = Math.max(1000, Math.min(5000, newValue));
        } else if (metric.id === 'bandwidth') {
          newValue = Math.max(100, Math.min(1000, newValue));
        }

        // Calculate trend
        const oldValue = metric.value;
        const change = ((newValue - oldValue) / oldValue) * 100;
        let trend: 'up' | 'down' | 'stable' = 'stable';
        
        if (Math.abs(change) > 1) {
          trend = change > 0 ? 'up' : 'down';
        }

        // Update history
        const newHistory = [...metric.history.slice(1), newValue];

        return {
          ...metric,
          value: newValue,
          trend,
          change,
          history: newHistory
        };
      })
    );
  };

  const getIcon = (iconName: string) => {
    const icons = {
      cpu: Cpu,
      database: Database,
      activity: Activity,
      zap: Zap,
      wifi: Wifi
    };
    return icons[iconName as keyof typeof icons] || Activity;
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return TrendingUp;
      case 'down': return TrendingDown;
      default: return Minus;
    }
  };

  const getTrendColor = (trend: string, metricId: string) => {
    // For some metrics, "down" is good (like latency)
    const invertedMetrics = ['latency'];
    const isInverted = invertedMetrics.includes(metricId);
    
    if (trend === 'up') {
      return isInverted ? 'text-safety-red' : 'text-safety-green';
    } else if (trend === 'down') {
      return isInverted ? 'text-safety-green' : 'text-safety-red';
    }
    return 'text-gray-500';
  };

  const renderMiniChart = (history: number[], color: string) => {
    const max = Math.max(...history);
    const min = Math.min(...history);
    const range = max - min || 1;
    
    return (
      <div className="flex items-end space-x-1 h-8">
        {history.map((value, index) => {
          const height = ((value - min) / range) * 100;
          const heightClass = height >= 90 ? 'h-full' :
                             height >= 80 ? 'h-5/6' :
                             height >= 70 ? 'h-4/5' :
                             height >= 60 ? 'h-3/5' :
                             height >= 50 ? 'h-1/2' :
                             height >= 40 ? 'h-2/5' :
                             height >= 30 ? 'h-1/3' :
                             height >= 20 ? 'h-1/5' :
                             height >= 10 ? 'h-1/6' : 'h-2';
          return (
            <div
              key={index}
              className={`w-1 bg-${color} opacity-70 rounded-t ${heightClass}`}
            />
          );
        })}
      </div>
    );
  };

  return (
    <Card className="glass-card border-0">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold flex items-center">
            <Activity className="w-5 h-5 mr-2 text-ai-primary" />
            Real-time System Metrics
          </h3>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-safety-green animate-pulse' : 'bg-safety-red'}`}></div>
            <span className={`text-xs font-medium ${isConnected ? 'text-safety-green' : 'text-safety-red'}`}>
              {isConnected ? 'CONNECTED' : 'DISCONNECTED'}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {metrics.map((metric) => {
            const IconComponent = getIcon(metric.icon);
            const TrendIcon = getTrendIcon(metric.trend);
            
            return (
              <div
                key={metric.id}
                className="p-4 bg-white/50 rounded-lg border border-gray-200 hover:bg-white/80 transition-all duration-200"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className={`w-8 h-8 bg-${metric.color}/20 rounded-full flex items-center justify-center`}>
                    <IconComponent className={`w-4 h-4 text-${metric.color}`} />
                  </div>
                  <div className={`flex items-center space-x-1 ${getTrendColor(metric.trend, metric.id)}`}>
                    <TrendIcon className="w-3 h-3" />
                    <span className="text-xs font-medium">
                      {metric.change > 0 ? '+' : ''}{metric.change.toFixed(1)}%
                    </span>
                  </div>
                </div>
                
                <div className="mb-2">
                  <div className="text-lg font-bold text-gray-900">
                    {metric.value.toFixed(metric.id === 'throughput' ? 0 : 1)}
                    <span className="text-sm font-normal text-gray-600 ml-1">
                      {metric.unit}
                    </span>
                  </div>
                  <div className="text-xs text-gray-600">{metric.name}</div>
                </div>
                
                {/* Mini Chart */}
                <div className="mb-2">
                  {renderMiniChart(metric.history, metric.color)}
                </div>
                
                {/* Status Bar */}
                <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className={`h-full bg-${metric.color} transition-all duration-1000 ${
                      (() => {
                        const percentage = metric.id === 'accuracy' || metric.id === 'cpu' ? metric.value :
                                         metric.id === 'memory' ? (metric.value / 8) * 100 :
                                         metric.id === 'latency' ? Math.max(0, 100 - (metric.value / 5)) :
                                         metric.id === 'throughput' ? (metric.value / 5000) * 100 :
                                         (metric.value / 1000) * 100;
                        return percentage >= 90 ? 'w-full' :
                               percentage >= 80 ? 'w-5/6' :
                               percentage >= 70 ? 'w-4/5' :
                               percentage >= 60 ? 'w-3/5' :
                               percentage >= 50 ? 'w-1/2' :
                               percentage >= 40 ? 'w-2/5' :
                               percentage >= 30 ? 'w-1/3' :
                               percentage >= 20 ? 'w-1/5' :
                               percentage >= 10 ? 'w-1/6' : 'w-1/12';
                      })()
                    }`}
                  />
                </div>
              </div>
            );
          })}
        </div>

        {/* System Health Summary */}
        <div className="mt-6 p-4 bg-gradient-to-r from-ai-primary/10 to-ai-secondary/10 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="font-medium text-gray-900">System Health Score</span>
            <span className="text-2xl font-bold text-ai-primary">
              {(metrics.reduce((acc, m) => {
                let score = 0;
                if (m.id === 'accuracy') score = m.value;
                else if (m.id === 'cpu') score = 100 - m.value;
                else if (m.id === 'memory') score = Math.max(0, 100 - (m.value / 8) * 100);
                else if (m.id === 'latency') score = Math.max(0, 100 - (m.value / 500) * 100);
                else if (m.id === 'throughput') score = Math.min(100, (m.value / 3000) * 100);
                else if (m.id === 'bandwidth') score = Math.min(100, (m.value / 500) * 100);
                return acc + score;
              }, 0) / metrics.length).toFixed(0)}%
            </span>
          </div>
          <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className={`h-full bg-gradient-to-r from-ai-primary to-ai-secondary transition-all duration-1000 ${
                (() => {
                  const healthScore = (metrics.reduce((acc, m) => {
                    let score = 0;
                    if (m.id === 'accuracy') score = m.value;
                    else if (m.id === 'cpu') score = 100 - m.value;
                    else if (m.id === 'memory') score = Math.max(0, 100 - (m.value / 8) * 100);
                    else if (m.id === 'latency') score = Math.max(0, 100 - (m.value / 500) * 100);
                    else if (m.id === 'throughput') score = Math.min(100, (m.value / 3000) * 100);
                    else if (m.id === 'bandwidth') score = Math.min(100, (m.value / 500) * 100);
                    return acc + score;
                  }, 0) / metrics.length);
                  return healthScore >= 90 ? 'w-full' :
                         healthScore >= 80 ? 'w-5/6' :
                         healthScore >= 70 ? 'w-4/5' :
                         healthScore >= 60 ? 'w-3/5' :
                         healthScore >= 50 ? 'w-1/2' :
                         healthScore >= 40 ? 'w-2/5' :
                         healthScore >= 30 ? 'w-1/3' :
                         healthScore >= 20 ? 'w-1/5' :
                         healthScore >= 10 ? 'w-1/6' : 'w-1/12';
                })()
              }`}
            />
          </div>
          <div className="text-xs text-gray-600 mt-1">
            All systems operating within normal parameters
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
