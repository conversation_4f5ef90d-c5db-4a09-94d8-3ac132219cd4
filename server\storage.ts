import { 
  users, incidents, safetyChecks, toolboxTalks, documents, alerts,
  type User, type InsertUser, type Incident, type InsertIncident,
  type SafetyCheck, type InsertSafetyCheck, type ToolboxTalk, type InsertToolboxTalk,
  type Document, type InsertDocument, type Alert, type InsertAlert
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and, or, gte, lte, count } from "drizzle-orm";

export interface IStorage {
  // User management
  getUser(id: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;

  // Incident management
  createIncident(incident: InsertIncident): Promise<Incident>;
  getIncident(id: string): Promise<Incident | undefined>;
  getIncidents(userId?: string, limit?: number): Promise<Incident[]>;
  updateIncidentStatus(id: string, status: string): Promise<void>;
  getIncidentsByDateRange(startDate: Date, endDate: Date): Promise<Incident[]>;

  // Safety checks
  createSafetyCheck(check: InsertSafetyCheck): Promise<SafetyCheck>;
  getSafetyChecks(userId?: string, limit?: number): Promise<SafetyCheck[]>;
  getSafetyChecksByType(type: string): Promise<SafetyCheck[]>;

  // Toolbox talks
  createToolboxTalk(talk: InsertToolboxTalk): Promise<ToolboxTalk>;
  getToolboxTalks(userId?: string): Promise<ToolboxTalk[]>;
  updateToolboxTalkAttendees(id: string, attendees: string[]): Promise<void>;

  // Document management
  createDocument(document: InsertDocument): Promise<Document>;
  getDocuments(userId?: string): Promise<Document[]>;
  updateDocumentStatus(id: string, status: string): Promise<void>;
  getDocumentsByType(type: string): Promise<Document[]>;

  // Alert system
  createAlert(alert: InsertAlert): Promise<Alert>;
  getUnsentAlerts(): Promise<Alert[]>;
  markAlertAsSent(id: string): Promise<void>;

  // Dashboard statistics
  getDashboardStats(userId: string): Promise<{
    todayInspections: number;
    todayAlerts: number;
    complianceScore: number;
    recentIncidents: Incident[];
  }>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }

  async createIncident(incident: InsertIncident): Promise<Incident> {
    const [newIncident] = await db.insert(incidents).values(incident).returning();
    return newIncident;
  }

  async getIncident(id: string): Promise<Incident | undefined> {
    const [incident] = await db.select().from(incidents).where(eq(incidents.id, id));
    return incident || undefined;
  }

  async getIncidents(userId?: string, limit: number = 50): Promise<Incident[]> {
    const query = db.select().from(incidents);
    
    if (userId) {
      return await query.where(eq(incidents.userId, userId))
        .orderBy(desc(incidents.createdAt))
        .limit(limit);
    }
    
    return await query.orderBy(desc(incidents.createdAt)).limit(limit);
  }

  async updateIncidentStatus(id: string, status: string): Promise<void> {
    await db.update(incidents)
      .set({ 
        status, 
        resolvedAt: status === 'resolved' ? new Date() : null 
      })
      .where(eq(incidents.id, id));
  }

  async getIncidentsByDateRange(startDate: Date, endDate: Date): Promise<Incident[]> {
    return await db.select().from(incidents)
      .where(and(
        gte(incidents.createdAt, startDate),
        lte(incidents.createdAt, endDate)
      ))
      .orderBy(desc(incidents.createdAt));
  }

  async createSafetyCheck(check: InsertSafetyCheck): Promise<SafetyCheck> {
    const [newCheck] = await db.insert(safetyChecks).values(check).returning();
    return newCheck;
  }

  async getSafetyChecks(userId?: string, limit: number = 50): Promise<SafetyCheck[]> {
    const query = db.select().from(safetyChecks);
    
    if (userId) {
      return await query.where(eq(safetyChecks.userId, userId))
        .orderBy(desc(safetyChecks.createdAt))
        .limit(limit);
    }
    
    return await query.orderBy(desc(safetyChecks.createdAt)).limit(limit);
  }

  async getSafetyChecksByType(type: string): Promise<SafetyCheck[]> {
    return await db.select().from(safetyChecks)
      .where(eq(safetyChecks.type, type))
      .orderBy(desc(safetyChecks.createdAt));
  }

  async createToolboxTalk(talk: InsertToolboxTalk): Promise<ToolboxTalk> {
    const [newTalk] = await db.insert(toolboxTalks).values(talk).returning();
    return newTalk;
  }

  async getToolboxTalks(userId?: string): Promise<ToolboxTalk[]> {
    const query = db.select().from(toolboxTalks);
    
    if (userId) {
      return await query.where(eq(toolboxTalks.conductorId, userId))
        .orderBy(desc(toolboxTalks.createdAt));
    }
    
    return await query.orderBy(desc(toolboxTalks.createdAt));
  }

  async updateToolboxTalkAttendees(id: string, attendees: string[]): Promise<void> {
    await db.update(toolboxTalks)
      .set({ attendees })
      .where(eq(toolboxTalks.id, id));
  }

  async createDocument(document: InsertDocument): Promise<Document> {
    const [newDocument] = await db.insert(documents).values(document).returning();
    return newDocument;
  }

  async getDocuments(userId?: string): Promise<Document[]> {
    const query = db.select().from(documents);
    
    if (userId) {
      return await query.where(eq(documents.userId, userId))
        .orderBy(desc(documents.createdAt));
    }
    
    return await query.orderBy(desc(documents.createdAt));
  }

  async updateDocumentStatus(id: string, status: string): Promise<void> {
    await db.update(documents)
      .set({ status, updatedAt: new Date() })
      .where(eq(documents.id, id));
  }

  async getDocumentsByType(type: string): Promise<Document[]> {
    return await db.select().from(documents)
      .where(eq(documents.type, type))
      .orderBy(desc(documents.createdAt));
  }

  async createAlert(alert: InsertAlert): Promise<Alert> {
    const [newAlert] = await db.insert(alerts).values(alert).returning();
    return newAlert;
  }

  async getUnsentAlerts(): Promise<Alert[]> {
    return await db.select().from(alerts)
      .where(eq(alerts.sent, false))
      .orderBy(desc(alerts.createdAt));
  }

  async markAlertAsSent(id: string): Promise<void> {
    await db.update(alerts)
      .set({ sent: true, sentAt: new Date() })
      .where(eq(alerts.id, id));
  }

  async getDashboardStats(userId: string): Promise<{
    todayInspections: number;
    todayAlerts: number;
    complianceScore: number;
    recentIncidents: Incident[];
  }> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Count today's inspections
    const [inspectionCount] = await db
      .select({ count: count() })
      .from(safetyChecks)
      .where(and(
        eq(safetyChecks.userId, userId),
        gte(safetyChecks.createdAt, today),
        lte(safetyChecks.createdAt, tomorrow)
      ));

    // Count today's alerts
    const [alertCount] = await db
      .select({ count: count() })
      .from(alerts)
      .where(and(
        eq(alerts.userId, userId),
        gte(alerts.createdAt, today),
        lte(alerts.createdAt, tomorrow)
      ));

    // Get recent incidents
    const recentIncidents = await db.select().from(incidents)
      .where(eq(incidents.userId, userId))
      .orderBy(desc(incidents.createdAt))
      .limit(5);

    // Calculate compliance score (based on recent safety checks)
    const recentChecks = await db.select().from(safetyChecks)
      .where(eq(safetyChecks.userId, userId))
      .orderBy(desc(safetyChecks.createdAt))
      .limit(10);

    const avgCompliance = recentChecks.length > 0 
      ? recentChecks.reduce((sum, check) => sum + (check.complianceScore || 0), 0) / recentChecks.length
      : 100;

    return {
      todayInspections: inspectionCount.count,
      todayAlerts: alertCount.count,
      complianceScore: Math.round(avgCompliance),
      recentIncidents,
    };
  }
}

export const storage = new DatabaseStorage();
