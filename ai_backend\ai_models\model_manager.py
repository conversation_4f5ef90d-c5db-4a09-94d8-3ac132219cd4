"""
Model Manager for AI Safety Assistant
Coordinates all AI models and provides unified interface
"""

import asyncio
import logging
import psutil
from typing import Dict, List, Optional, Any
from pathlib import Path

from .safety_detector import SafetyDetector
from .ppe_detector import PPEDetector
from .voice_processor import VoiceProcessor
from .zone_monitor import ZoneMonitor
from .fall_detector import FallDetector
from .hazard_detector import HazardDetector
from .toolbox_detector import ToolboxDetector
from .vehicle_proximity_detector import VehicleProximityDetector

logger = logging.getLogger(__name__)

class ModelManager:
    """
    Central manager for all AI models in the safety assistant
    """
    
    def __init__(self, model_path: str = "models/"):
        self.model_path = Path(model_path)
        self.model_path.mkdir(exist_ok=True)
        
        # Model instances
        self.safety_detector: Optional[SafetyDetector] = None
        self.ppe_detector: Optional[PPEDetector] = None
        self.voice_processor: Optional[VoiceProcessor] = None
        self.zone_monitor: Optional[ZoneMonitor] = None
        self.fall_detector: Optional[FallDetector] = None
        self.hazard_detector: Optional[HazardDetector] = None
        self.toolbox_detector: Optional[ToolboxDetector] = None
        self.vehicle_proximity_detector: Optional[VehicleProximityDetector] = None
        
        self._initialized = False
        self._model_status = {}
        
    async def initialize_models(self):
        """Initialize all AI models"""
        logger.info("Initializing AI models...")
        
        try:
            # Initialize models in parallel for faster startup
            initialization_tasks = [
                self._init_safety_detector(),
                self._init_ppe_detector(),
                self._init_voice_processor(),
                self._init_zone_monitor(),
                self._init_fall_detector(),
                self._init_hazard_detector(),
                self._init_toolbox_detector(),
                self._init_vehicle_proximity_detector()
            ]
            
            results = await asyncio.gather(*initialization_tasks, return_exceptions=True)
            
            # Check for any initialization failures
            for i, result in enumerate(results):
                model_name = [
                    "safety_detector", "ppe_detector", "voice_processor",
                    "zone_monitor", "fall_detector", "hazard_detector",
                    "toolbox_detector", "vehicle_proximity_detector"
                ][i]
                
                if isinstance(result, Exception):
                    logger.error(f"Failed to initialize {model_name}: {result}")
                    self._model_status[model_name] = {"status": "failed", "error": str(result)}
                else:
                    self._model_status[model_name] = {"status": "loaded", "version": "2.0.0"}
            
            self._initialized = True
            logger.info("Model initialization complete")
            
        except Exception as e:
            logger.error(f"Model initialization failed: {e}")
            raise
    
    async def _init_safety_detector(self):
        """Initialize main safety detector"""
        self.safety_detector = SafetyDetector(self.model_path / "safety")
        await self.safety_detector.load_model()
        
    async def _init_ppe_detector(self):
        """Initialize PPE detection model"""
        self.ppe_detector = PPEDetector(self.model_path / "ppe")
        await self.ppe_detector.load_model()
        
    async def _init_voice_processor(self):
        """Initialize voice processing model"""
        self.voice_processor = VoiceProcessor(self.model_path / "voice")
        await self.voice_processor.load_model()
        
    async def _init_zone_monitor(self):
        """Initialize zone monitoring model"""
        self.zone_monitor = ZoneMonitor(self.model_path / "zone")
        await self.zone_monitor.load_model()
        
    async def _init_fall_detector(self):
        """Initialize fall detection model"""
        self.fall_detector = FallDetector(self.model_path / "fall")
        await self.fall_detector.load_model()
        
    async def _init_hazard_detector(self):
        """Initialize hazard detection model"""
        self.hazard_detector = HazardDetector(self.model_path / "hazard")
        await self.hazard_detector.load_model()
        
    async def _init_toolbox_detector(self):
        """Initialize toolbox/tools detection model"""
        self.toolbox_detector = ToolboxDetector(self.model_path / "toolbox")
        await self.toolbox_detector.load_model()
        
    async def _init_vehicle_proximity_detector(self):
        """Initialize vehicle proximity detection model"""
        self.vehicle_proximity_detector = VehicleProximityDetector(self.model_path / "vehicle")
        await self.vehicle_proximity_detector.load_model()
    
    def is_initialized(self) -> bool:
        """Check if all models are initialized"""
        return self._initialized
    
    async def get_model_status(self) -> Dict[str, Any]:
        """Get status of all models"""
        return self._model_status.copy()
    
    async def get_detailed_status(self) -> Dict[str, Any]:
        """Get detailed status including performance metrics"""
        status = await self.get_model_status()
        
        # Add system metrics
        status["system"] = {
            "memory_usage": psutil.virtual_memory().percent,
            "cpu_usage": psutil.cpu_percent(),
            "gpu_available": self._check_gpu_availability()
        }
        
        # Add model performance metrics
        for model_name in status:
            if model_name != "system" and hasattr(self, model_name):
                model = getattr(self, model_name)
                if hasattr(model, "get_performance_metrics"):
                    status[model_name]["performance"] = await model.get_performance_metrics()
        
        return status
    
    async def get_memory_usage(self) -> Dict[str, float]:
        """Get memory usage for each model"""
        memory_info = {}
        
        for model_name in ["safety_detector", "ppe_detector", "voice_processor",
                          "zone_monitor", "fall_detector", "hazard_detector",
                          "toolbox_detector", "vehicle_proximity_detector"]:
            model = getattr(self, model_name)
            if model and hasattr(model, "get_memory_usage"):
                memory_info[model_name] = await model.get_memory_usage()
        
        return memory_info
    
    def _check_gpu_availability(self) -> bool:
        """Check if GPU is available for inference"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
    
    async def log_analysis(self, image_data: str, detections: List[Any], location: Optional[Dict] = None):
        """Log analysis results for continuous learning"""
        try:
            # This would typically save to a database or data lake
            # for model retraining and performance monitoring
            log_entry = {
                "timestamp": asyncio.get_event_loop().time(),
                "detections_count": len(detections),
                "location": location,
                "model_versions": {name: info.get("version") for name, info in self._model_status.items()}
            }
            
            # In production, this would be sent to a data pipeline
            logger.info(f"Analysis logged: {log_entry}")
            
        except Exception as e:
            logger.error(f"Failed to log analysis: {e}")
    
    async def update_model(self, model_name: str, model_path: str):
        """Hot-swap a model with a new version"""
        try:
            if not hasattr(self, model_name):
                raise ValueError(f"Unknown model: {model_name}")
            
            # Load new model
            old_model = getattr(self, model_name)
            new_model_class = type(old_model)
            new_model = new_model_class(model_path)
            await new_model.load_model()
            
            # Replace old model
            setattr(self, model_name, new_model)
            self._model_status[model_name]["version"] = "updated"
            
            logger.info(f"Model {model_name} updated successfully")
            
        except Exception as e:
            logger.error(f"Failed to update model {model_name}: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup all models and free resources"""
        logger.info("Cleaning up AI models...")
        
        cleanup_tasks = []
        for model_name in ["safety_detector", "ppe_detector", "voice_processor",
                          "zone_monitor", "fall_detector", "hazard_detector",
                          "toolbox_detector", "vehicle_proximity_detector"]:
            model = getattr(self, model_name)
            if model and hasattr(model, "cleanup"):
                cleanup_tasks.append(model.cleanup())
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        self._initialized = False
        logger.info("Model cleanup complete")
    
    async def benchmark_models(self) -> Dict[str, Dict[str, float]]:
        """Run performance benchmarks on all models"""
        benchmark_results = {}
        
        # This would run standardized test images/audio through each model
        # and measure inference time, accuracy, etc.
        
        for model_name in self._model_status:
            if model_name != "system":
                model = getattr(self, model_name)
                if model and hasattr(model, "benchmark"):
                    benchmark_results[model_name] = await model.benchmark()
        
        return benchmark_results
