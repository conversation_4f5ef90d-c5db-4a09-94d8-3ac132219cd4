import { <PERSON> } from 'wouter';
import { 
  Shield, 
  ArrowLeft,
  Users,
  Target,
  Award,
  Globe,
  Brain,
  Lightbulb,
  Heart,
  Zap
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

export default function AboutPage() {
  const values = [
    {
      icon: Shield,
      title: "Safety First",
      description: "Every decision we make is guided by our commitment to protecting human life and preventing workplace accidents."
    },
    {
      icon: Brain,
      title: "Innovation",
      description: "We leverage cutting-edge AI technology to solve complex safety challenges that traditional methods cannot address."
    },
    {
      icon: Heart,
      title: "Human-Centered",
      description: "Technology should enhance human capabilities, not replace them. We design AI that empowers safety professionals."
    },
    {
      icon: Globe,
      title: "Global Impact",
      description: "Our mission is to make every workplace safer, regardless of industry, size, or location around the world."
    }
  ];

  const team = [
    {
      name: "Dr. <PERSON>",
      role: "CEO & Co-Founder",
      bio: "Former Google AI researcher with 15+ years in computer vision and workplace safety. PhD from Stanford.",
      image: "👩‍💼"
    },
    {
      name: "<PERSON>",
      role: "CTO & Co-Founder",
      bio: "Ex-Tesla AI engineer specializing in real-time systems. Built safety-critical AI for autonomous vehicles.",
      image: "👨‍💻"
    },
    {
      name: "Dr. <PERSON>",
      role: "Head of AI Research",
      bio: "Leading expert in computer vision and neural networks. Former Microsoft Research scientist.",
      image: "👩‍🔬"
    },
    {
      name: "David Kim",
      role: "VP of Engineering",
      bio: "20+ years building scalable systems at Amazon and Uber. Expert in distributed AI infrastructure.",
      image: "👨‍🔧"
    },
    {
      name: "Lisa Thompson",
      role: "Head of Safety",
      bio: "Certified Safety Professional with 25+ years in industrial safety. Former OSHA inspector.",
      image: "👩‍🏭"
    },
    {
      name: "James Wilson",
      role: "VP of Sales",
      bio: "Enterprise software veteran with deep expertise in safety technology markets. Former Salesforce.",
      image: "👨‍💼"
    }
  ];

  const milestones = [
    {
      year: "2020",
      title: "Company Founded",
      description: "Started with a vision to revolutionize workplace safety using AI"
    },
    {
      year: "2021",
      title: "First AI Model",
      description: "Launched our first PPE detection model with 85% accuracy"
    },
    {
      year: "2022",
      title: "Series A Funding",
      description: "Raised $15M to accelerate AI research and product development"
    },
    {
      year: "2023",
      title: "100+ Customers",
      description: "Reached 100+ enterprise customers across 15 industries"
    },
    {
      year: "2024",
      title: "AI Breakthrough",
      description: "Achieved 94.2% detection accuracy with predictive analytics"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/">
              <Button variant="ghost" className="text-white hover:bg-white/10">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Home
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <Shield className="h-8 w-8 text-blue-400" />
              <span className="text-xl font-bold text-white">SafeGuard AI</span>
            </div>
            <Link href="/dashboard">
              <Button className="bg-blue-600 hover:bg-blue-700">
                Launch App
              </Button>
            </Link>
          </div>
        </div>
      </nav>

      {/* Hero */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Building the Future of
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              {" "}Workplace Safety
            </span>
          </h1>
          <p className="text-xl text-white/80 mb-8">
            We're on a mission to prevent workplace accidents before they happen using 
            the power of artificial intelligence and computer vision.
          </p>
        </div>
      </section>

      {/* Mission */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-white mb-6">Our Mission</h2>
              <p className="text-lg text-white/80 mb-6">
                Every year, millions of workers are injured in preventable workplace accidents. 
                Traditional safety methods rely on human observation and reactive measures, 
                which often fall short in fast-paced, complex work environments.
              </p>
              <p className="text-lg text-white/80 mb-6">
                We believe that artificial intelligence can fundamentally change this reality. 
                By combining computer vision, machine learning, and predictive analytics, 
                we can identify and prevent safety hazards in real-time.
              </p>
              <div className="flex items-center space-x-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-400">85%</div>
                  <div className="text-white/60">Incident Reduction</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-400">500+</div>
                  <div className="text-white/60">Companies Protected</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-400">1M+</div>
                  <div className="text-white/60">Workers Safer</div>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-2xl border border-white/10 flex items-center justify-center">
                <div className="text-center">
                  <Target className="h-24 w-24 text-blue-400 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-white mb-2">Zero Accidents</h3>
                  <p className="text-white/70">Our ultimate goal</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">Our Values</h2>
            <p className="text-xl text-white/80">
              The principles that guide everything we do
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 text-center">
                <CardContent className="p-6">
                  <value.icon className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-3">{value.title}</h3>
                  <p className="text-white/70">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">Our Journey</h2>
            <p className="text-xl text-white/80">
              Key milestones in our mission to revolutionize workplace safety
            </p>
          </div>
          <div className="space-y-8">
            {milestones.map((milestone, index) => (
              <div key={index} className="flex items-start space-x-6">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">{milestone.year}</span>
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-white mb-2">{milestone.title}</h3>
                  <p className="text-white/70">{milestone.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">Meet Our Team</h2>
            <p className="text-xl text-white/80">
              World-class experts in AI, safety, and enterprise software
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10">
                <CardContent className="p-6 text-center">
                  <div className="text-6xl mb-4">{member.image}</div>
                  <h3 className="text-xl font-semibold text-white mb-1">{member.name}</h3>
                  <p className="text-blue-400 mb-3">{member.role}</p>
                  <p className="text-white/70 text-sm">{member.bio}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Recognition */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">Recognition & Awards</h2>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="bg-white/5 backdrop-blur-sm border-white/10 text-center">
              <CardContent className="p-6">
                <Award className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">AI Innovation Award</h3>
                <p className="text-white/70">TechCrunch Disrupt 2023</p>
              </CardContent>
            </Card>
            <Card className="bg-white/5 backdrop-blur-sm border-white/10 text-center">
              <CardContent className="p-6">
                <Lightbulb className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Best Safety Technology</h3>
                <p className="text-white/70">Safety+Health Magazine 2024</p>
              </CardContent>
            </Card>
            <Card className="bg-white/5 backdrop-blur-sm border-white/10 text-center">
              <CardContent className="p-6">
                <Zap className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Fastest Growing Startup</h3>
                <p className="text-white/70">Forbes 30 Under 30 2024</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Join Us in Making Workplaces Safer
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Whether you're looking to protect your workforce or join our mission, 
            we'd love to hear from you.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/dashboard">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                Try SafeGuard AI
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
              View Careers
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
