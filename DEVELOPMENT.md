# 🛠️ Development Guide

## 🚀 Quick Development Setup

### 1. Initial Setup
```bash
# Clone and setup
git clone <repository-url>
cd SafeGuardAI

# Automated setup
./setup.sh        # macOS/Linux
setup.bat         # Windows

# Verify everything is working
node verify-setup.js
```

### 2. Development Workflow
```bash
# Start development server
npm run dev

# In another terminal, run quality checks
node check-quality.js

# Fix code issues automatically
cd client
npm run lint:fix
npm run format
```

## 📁 Project Structure

```
SafeGuardAI/
├── 🔧 Configuration Files
│   ├── .vscode/                    # VS Code settings
│   ├── client/
│   │   ├── .eslintrc.json         # ESLint configuration
│   │   ├── .prettierrc.json       # Prettier configuration
│   │   ├── tsconfig.json          # TypeScript configuration
│   │   ├── tailwind.config.js     # Tailwind CSS configuration
│   │   └── vite.config.ts         # Vite build configuration
│   └── tsconfig.json              # Root TypeScript configuration
│
├── 🎨 Frontend (React + TypeScript)
│   └── client/src/
│       ├── components/            # Reusable UI components
│       │   ├── ui/               # Base UI components (shadcn/ui)
│       │   ├── ai-dashboard.tsx  # AI command center
│       │   ├── neural-network-viz.tsx
│       │   └── ...
│       ├── pages/                # Application pages
│       ├── hooks/                # Custom React hooks
│       ├── lib/                  # Utility functions
│       ├── services/             # API services
│       └── types/                # TypeScript type definitions
│
├── 🤖 AI Backend (Python + FastAPI)
│   └── ai_backend/
│       ├── ai_models/            # AI detection models
│       ├── utils/                # Utility functions
│       └── main.py               # FastAPI application
│
└── 📜 Scripts & Documentation
    ├── setup.sh/setup.bat        # Setup scripts
    ├── start.sh/start.bat         # Start scripts
    ├── verify-setup.js            # Setup verification
    ├── check-quality.js           # Code quality checks
    └── *.md                       # Documentation
```

## 🔧 Development Tools

### TypeScript Configuration
- **Strict mode enabled** for better type safety
- **Consistent casing** for cross-platform compatibility
- **Path mapping** for clean imports (`@/components/...`)
- **Modern ES features** with proper compilation

### Code Quality Tools
- **ESLint**: Code linting and style enforcement
- **Prettier**: Automatic code formatting
- **TypeScript**: Static type checking
- **Tailwind CSS**: Utility-first styling

### VS Code Integration
- **Auto-formatting** on save
- **Import organization** on save
- **ESLint auto-fix** on save
- **Tailwind CSS IntelliSense**
- **TypeScript error highlighting**

## 📝 Development Commands

### Frontend Development
```bash
cd client

# Development
npm run dev              # Start development server
npm run dev:host         # Start with network access

# Code Quality
npm run type-check       # Check TypeScript types
npm run type-check:watch # Watch mode for type checking
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint issues automatically
npm run format           # Format code with Prettier

# Building
npm run build            # Build for production
npm run preview          # Preview production build

# Maintenance
npm run clean            # Clean build artifacts
```

### Backend Development (Optional)
```bash
cd ai_backend

# Setup virtual environment
python -m venv venv
source venv/bin/activate  # macOS/Linux
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Start development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Quality Assurance
```bash
# Run all quality checks
node check-quality.js

# Verify complete setup
node verify-setup.js

# Check for outdated dependencies
cd client && npm outdated
```

## 🎨 Styling Guidelines

### Tailwind CSS Best Practices
- Use utility classes for styling
- Create component classes for repeated patterns
- Use CSS custom properties for dynamic values
- Follow mobile-first responsive design

### Color System
```css
/* AI-themed colors */
--ai-primary: hsl(220, 100%, 60%)     /* Blue */
--ai-secondary: hsl(280, 100%, 70%)   /* Purple */
--ai-accent: hsl(180, 100%, 50%)      /* Cyan */
--ai-neural: hsl(260, 100%, 65%)      /* Neural purple */

/* Safety colors */
--safety-red: hsl(0, 76%, 50%)        /* Danger */
--safety-orange: hsl(36, 100%, 47%)   /* Warning */
--safety-green: hsl(134, 41%, 44%)    /* Safe */
--safety-blue: hsl(207, 90%, 44%)     /* Info */
```

### Component Patterns
```tsx
// Use consistent component structure
interface ComponentProps {
  // Props with clear types
}

export function Component({ prop }: ComponentProps) {
  // Hooks at the top
  const [state, setState] = useState();
  
  // Event handlers
  const handleClick = () => {
    // Implementation
  };
  
  // Render
  return (
    <div className="glass-card">
      {/* Content */}
    </div>
  );
}
```

## 🧪 Testing Strategy

### Unit Testing (Planned)
```bash
# Install testing dependencies
npm install --save-dev @testing-library/react @testing-library/jest-dom vitest

# Run tests
npm run test
npm run test:watch
npm run test:coverage
```

### E2E Testing (Planned)
```bash
# Install Playwright
npm install --save-dev @playwright/test

# Run E2E tests
npm run test:e2e
```

## 🚀 Deployment

### Development Deployment
```bash
# Build and preview
npm run build
npm run preview
```

### Production Deployment
```bash
# Build optimized version
npm run build

# Deploy to static hosting
# (Vercel, Netlify, GitHub Pages, etc.)
```

## 🐛 Debugging

### Common Issues
1. **TypeScript errors**: Run `npm run type-check`
2. **Linting errors**: Run `npm run lint:fix`
3. **Formatting issues**: Run `npm run format`
4. **Build failures**: Check console for specific errors

### Debug Tools
- **React DevTools**: Browser extension for React debugging
- **VS Code Debugger**: Built-in debugging support
- **Browser DevTools**: Network, Console, Performance tabs
- **TypeScript Compiler**: Detailed error messages

### Performance Monitoring
```tsx
// Use React DevTools Profiler
import { Profiler } from 'react';

function onRenderCallback(id, phase, actualDuration) {
  console.log('Component render time:', actualDuration);
}

<Profiler id="App" onRender={onRenderCallback}>
  <App />
</Profiler>
```

## 📚 Learning Resources

### Technologies Used
- **React 18**: [React Documentation](https://react.dev/)
- **TypeScript**: [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- **Tailwind CSS**: [Tailwind Documentation](https://tailwindcss.com/docs)
- **Vite**: [Vite Guide](https://vitejs.dev/guide/)
- **shadcn/ui**: [Component Library](https://ui.shadcn.com/)

### AI/ML Resources
- **YOLOv8**: [Ultralytics Documentation](https://docs.ultralytics.com/)
- **MediaPipe**: [Google MediaPipe](https://mediapipe.dev/)
- **FastAPI**: [FastAPI Documentation](https://fastapi.tiangolo.com/)

## 🤝 Contributing

### Code Style
- Follow TypeScript strict mode
- Use Prettier for formatting
- Follow ESLint rules
- Write meaningful commit messages

### Pull Request Process
1. Run quality checks: `node check-quality.js`
2. Ensure all tests pass
3. Update documentation if needed
4. Create descriptive PR title and description

### Commit Message Format
```
feat: add neural network visualization component
fix: resolve camera permission issues on mobile
docs: update development setup guide
style: format code with prettier
refactor: optimize AI detection performance
```

---

**Happy coding! 🚀 Build the future of AI-powered safety!**
