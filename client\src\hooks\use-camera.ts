import { useState, useRef, useCallback } from 'react';

export interface CameraState {
  isActive: boolean;
  isRecording: boolean;
  stream: MediaStream | null;
  error: string | null;
}

export function useCamera() {
  const [state, setState] = useState<CameraState>({
    isActive: false,
    isRecording: false,
    stream: null,
    error: null
  });

  const videoRef = useRef<HTMLVideoElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<Blob[]>([]);

  const startCamera = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { 
          facingMode: 'environment',
          width: { ideal: 1280 },
          height: { ideal: 720 }
        },
        audio: false
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }

      setState(prev => ({
        ...prev,
        isActive: true,
        stream,
        error: null
      }));

      return stream;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Camera access failed';
      setState(prev => ({
        ...prev,
        error: errorMessage
      }));
      throw error;
    }
  }, []);

  const stopCamera = useCallback(() => {
    if (state.stream) {
      state.stream.getTracks().forEach(track => track.stop());
    }

    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    setState(prev => ({
      ...prev,
      isActive: false,
      isRecording: false,
      stream: null,
      error: null
    }));
  }, [state.stream]);

  const capturePhoto = useCallback((): string | null => {
    if (!videoRef.current || !state.isActive) return null;

    const canvas = document.createElement('canvas');
    const video = videoRef.current;
    
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return null;
    
    ctx.drawImage(video, 0, 0);
    return canvas.toDataURL('image/jpeg', 0.8);
  }, [state.isActive]);

  const startRecording = useCallback(() => {
    if (!state.stream) return;

    try {
      const mediaRecorder = new MediaRecorder(state.stream, {
        mimeType: 'video/webm;codecs=vp9'
      });

      chunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.start(1000); // Collect data every second
      mediaRecorderRef.current = mediaRecorder;

      setState(prev => ({
        ...prev,
        isRecording: true
      }));
    } catch (error) {
      console.error('Recording start failed:', error);
    }
  }, [state.stream]);

  const stopRecording = useCallback((): Promise<Blob | null> => {
    return new Promise((resolve) => {
      if (!mediaRecorderRef.current) {
        resolve(null);
        return;
      }

      mediaRecorderRef.current.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: 'video/webm' });
        chunksRef.current = [];
        resolve(blob);
      };

      mediaRecorderRef.current.stop();
      mediaRecorderRef.current = null;

      setState(prev => ({
        ...prev,
        isRecording: false
      }));
    });
  }, []);

  const switchCamera = useCallback(async () => {
    if (!state.isActive) return;

    try {
      // Stop current stream
      if (state.stream) {
        state.stream.getTracks().forEach(track => track.stop());
      }

      // Start new stream with different facing mode
      const currentFacingMode = state.stream?.getVideoTracks()[0]?.getSettings()?.facingMode;
      const newFacingMode = currentFacingMode === 'environment' ? 'user' : 'environment';

      const newStream = await navigator.mediaDevices.getUserMedia({
        video: { 
          facingMode: newFacingMode,
          width: { ideal: 1280 },
          height: { ideal: 720 }
        },
        audio: false
      });

      if (videoRef.current) {
        videoRef.current.srcObject = newStream;
      }

      setState(prev => ({
        ...prev,
        stream: newStream
      }));
    } catch (error) {
      console.error('Camera switch failed:', error);
    }
  }, [state.isActive, state.stream]);

  return {
    ...state,
    videoRef,
    startCamera,
    stopCamera,
    capturePhoto,
    startRecording,
    stopRecording,
    switchCamera
  };
}
