"""
Video Processing Utilities
Advanced video processing functions for real-time safety detection
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Optional, Tuple, Generator
import io
import base64
from pathlib import Path

logger = logging.getLogger(__name__)

class VideoProcessor:
    """
    Advanced video processing utilities for safety detection
    """
    
    @staticmethod
    def extract_frame(video_data: bytes, frame_number: int = 0) -> np.ndarray:
        """
        Extract a specific frame from video data
        
        Args:
            video_data: Video data as bytes
            frame_number: Frame number to extract (0 for first frame)
            
        Returns:
            Extracted frame as numpy array
        """
        try:
            # Save video data to temporary file
            temp_path = "/tmp/temp_video.mp4"
            with open(temp_path, 'wb') as f:
                f.write(video_data)
            
            # Open video
            cap = cv2.VideoCapture(temp_path)
            
            if not cap.isOpened():
                raise ValueError("Could not open video")
            
            # Set frame position
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            # Read frame
            ret, frame = cap.read()
            cap.release()
            
            if not ret:
                raise ValueError("Could not read frame from video")
            
            return frame
            
        except Exception as e:
            logger.error(f"Failed to extract frame from video: {e}")
            raise
    
    @staticmethod
    def extract_frames_batch(video_data: bytes, max_frames: int = 10, 
                           interval: int = 1) -> List[np.ndarray]:
        """
        Extract multiple frames from video
        
        Args:
            video_data: Video data as bytes
            max_frames: Maximum number of frames to extract
            interval: Frame interval (1 = every frame, 2 = every other frame, etc.)
            
        Returns:
            List of extracted frames
        """
        try:
            # Save video data to temporary file
            temp_path = "/tmp/temp_video_batch.mp4"
            with open(temp_path, 'wb') as f:
                f.write(video_data)
            
            # Open video
            cap = cv2.VideoCapture(temp_path)
            
            if not cap.isOpened():
                raise ValueError("Could not open video")
            
            frames = []
            frame_count = 0
            extracted_count = 0
            
            while extracted_count < max_frames:
                ret, frame = cap.read()
                
                if not ret:
                    break
                
                # Extract frame at specified interval
                if frame_count % interval == 0:
                    frames.append(frame.copy())
                    extracted_count += 1
                
                frame_count += 1
            
            cap.release()
            
            return frames
            
        except Exception as e:
            logger.error(f"Failed to extract frames from video: {e}")
            return []
    
    @staticmethod
    def create_frame_generator(video_path: str) -> Generator[np.ndarray, None, None]:
        """
        Create a generator for processing video frames
        
        Args:
            video_path: Path to video file
            
        Yields:
            Video frames as numpy arrays
        """
        try:
            cap = cv2.VideoCapture(video_path)
            
            if not cap.isOpened():
                raise ValueError(f"Could not open video: {video_path}")
            
            while True:
                ret, frame = cap.read()
                
                if not ret:
                    break
                
                yield frame
            
            cap.release()
            
        except Exception as e:
            logger.error(f"Failed to create frame generator: {e}")
            return
    
    @staticmethod
    def get_video_info(video_data: bytes) -> Dict[str, Any]:
        """
        Get video information
        
        Args:
            video_data: Video data as bytes
            
        Returns:
            Dictionary with video information
        """
        try:
            # Save video data to temporary file
            temp_path = "/tmp/temp_video_info.mp4"
            with open(temp_path, 'wb') as f:
                f.write(video_data)
            
            # Open video
            cap = cv2.VideoCapture(temp_path)
            
            if not cap.isOpened():
                raise ValueError("Could not open video")
            
            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            duration = frame_count / fps if fps > 0 else 0
            
            cap.release()
            
            return {
                "fps": fps,
                "frame_count": frame_count,
                "width": width,
                "height": height,
                "duration": duration,
                "resolution": f"{width}x{height}"
            }
            
        except Exception as e:
            logger.error(f"Failed to get video info: {e}")
            return {}
    
    @staticmethod
    def stabilize_frame(frame: np.ndarray, previous_frame: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Stabilize video frame to reduce motion blur
        
        Args:
            frame: Current frame
            previous_frame: Previous frame for motion estimation
            
        Returns:
            Stabilized frame
        """
        try:
            if previous_frame is None:
                return frame
            
            # Convert to grayscale
            gray_current = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            gray_previous = cv2.cvtColor(previous_frame, cv2.COLOR_BGR2GRAY)
            
            # Detect features in previous frame
            corners = cv2.goodFeaturesToTrack(gray_previous, maxCorners=100, 
                                            qualityLevel=0.01, minDistance=10)
            
            if corners is None or len(corners) < 10:
                return frame
            
            # Track features using optical flow
            new_corners, status, _ = cv2.calcOpticalFlowPyrLK(
                gray_previous, gray_current, corners, None
            )
            
            # Filter good matches
            good_old = corners[status == 1]
            good_new = new_corners[status == 1]
            
            if len(good_old) < 10:
                return frame
            
            # Estimate transformation matrix
            transform_matrix = cv2.estimateAffinePartial2D(good_old, good_new)[0]
            
            if transform_matrix is None:
                return frame
            
            # Apply inverse transformation to stabilize
            height, width = frame.shape[:2]
            stabilized = cv2.warpAffine(frame, transform_matrix, (width, height))
            
            return stabilized
            
        except Exception as e:
            logger.error(f"Failed to stabilize frame: {e}")
            return frame
    
    @staticmethod
    def enhance_frame_for_detection(frame: np.ndarray) -> np.ndarray:
        """
        Enhance frame for better AI detection
        
        Args:
            frame: Input frame
            
        Returns:
            Enhanced frame
        """
        try:
            # Apply denoising
            denoised = cv2.fastNlMeansDenoisingColored(frame, None, 10, 10, 7, 21)
            
            # Enhance contrast using CLAHE
            lab = cv2.cvtColor(denoised, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l_enhanced = clahe.apply(l)
            
            enhanced = cv2.merge([l_enhanced, a, b])
            enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
            
            # Sharpen the image
            kernel = np.array([[-1, -1, -1],
                             [-1,  9, -1],
                             [-1, -1, -1]])
            sharpened = cv2.filter2D(enhanced, -1, kernel)
            
            # Blend original and sharpened
            result = cv2.addWeighted(enhanced, 0.7, sharpened, 0.3, 0)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to enhance frame: {e}")
            return frame
    
    @staticmethod
    def detect_motion_areas(current_frame: np.ndarray, background: np.ndarray, 
                          threshold: int = 30) -> List[Dict[str, Any]]:
        """
        Detect areas with motion between frames
        
        Args:
            current_frame: Current frame
            background: Background frame or previous frame
            threshold: Motion detection threshold
            
        Returns:
            List of motion areas with bounding boxes
        """
        try:
            # Convert to grayscale
            gray_current = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)
            gray_background = cv2.cvtColor(background, cv2.COLOR_BGR2GRAY)
            
            # Calculate absolute difference
            diff = cv2.absdiff(gray_current, gray_background)
            
            # Apply threshold
            _, thresh = cv2.threshold(diff, threshold, 255, cv2.THRESH_BINARY)
            
            # Apply morphological operations to clean up
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
            thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            motion_areas = []
            for contour in contours:
                area = cv2.contourArea(contour)
                
                # Filter small areas
                if area > 500:
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    motion_area = {
                        "bounding_box": {"x": x, "y": y, "width": w, "height": h},
                        "area": area,
                        "center": (x + w // 2, y + h // 2),
                        "motion_intensity": np.mean(diff[y:y+h, x:x+w])
                    }
                    motion_areas.append(motion_area)
            
            return motion_areas
            
        except Exception as e:
            logger.error(f"Failed to detect motion areas: {e}")
            return []
    
    @staticmethod
    def create_background_subtractor(history: int = 500, var_threshold: int = 50, 
                                   detect_shadows: bool = True):
        """
        Create background subtractor for motion detection
        
        Args:
            history: Number of frames in history
            var_threshold: Threshold for pixel classification
            detect_shadows: Whether to detect shadows
            
        Returns:
            Background subtractor object
        """
        try:
            return cv2.createBackgroundSubtractorMOG2(
                history=history,
                varThreshold=var_threshold,
                detectShadows=detect_shadows
            )
        except Exception as e:
            logger.error(f"Failed to create background subtractor: {e}")
            return None
    
    @staticmethod
    def apply_background_subtraction(frame: np.ndarray, bg_subtractor) -> np.ndarray:
        """
        Apply background subtraction to frame
        
        Args:
            frame: Input frame
            bg_subtractor: Background subtractor object
            
        Returns:
            Foreground mask
        """
        try:
            if bg_subtractor is None:
                return np.zeros(frame.shape[:2], dtype=np.uint8)
            
            fg_mask = bg_subtractor.apply(frame)
            
            # Clean up the mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
            
            return fg_mask
            
        except Exception as e:
            logger.error(f"Failed to apply background subtraction: {e}")
            return np.zeros(frame.shape[:2], dtype=np.uint8)
    
    @staticmethod
    def track_objects(frame: np.ndarray, detections: List[Dict], 
                     previous_tracks: List[Dict]) -> List[Dict]:
        """
        Track objects across frames using simple centroid tracking
        
        Args:
            frame: Current frame
            detections: Current frame detections
            previous_tracks: Previous frame tracks
            
        Returns:
            Updated tracks
        """
        try:
            current_tracks = []
            max_distance = 100  # Maximum distance for track association
            
            # Extract centroids from current detections
            current_centroids = []
            for detection in detections:
                bbox = detection.get("bounding_box", {})
                if bbox:
                    center_x = bbox["x"] + bbox["width"] // 2
                    center_y = bbox["y"] + bbox["height"] // 2
                    current_centroids.append((center_x, center_y))
            
            # If no previous tracks, create new tracks
            if not previous_tracks:
                for i, detection in enumerate(detections):
                    track = {
                        "id": i,
                        "detection": detection,
                        "centroid": current_centroids[i],
                        "age": 1,
                        "total_frames": 1
                    }
                    current_tracks.append(track)
                return current_tracks
            
            # Associate current detections with previous tracks
            used_detection_indices = set()
            used_track_indices = set()
            
            for track_idx, track in enumerate(previous_tracks):
                min_distance = float('inf')
                best_detection_idx = -1
                
                for det_idx, centroid in enumerate(current_centroids):
                    if det_idx in used_detection_indices:
                        continue
                    
                    distance = np.sqrt(
                        (track["centroid"][0] - centroid[0]) ** 2 +
                        (track["centroid"][1] - centroid[1]) ** 2
                    )
                    
                    if distance < min_distance and distance < max_distance:
                        min_distance = distance
                        best_detection_idx = det_idx
                
                # Update track if association found
                if best_detection_idx != -1:
                    updated_track = {
                        "id": track["id"],
                        "detection": detections[best_detection_idx],
                        "centroid": current_centroids[best_detection_idx],
                        "age": track["age"] + 1,
                        "total_frames": track["total_frames"] + 1
                    }
                    current_tracks.append(updated_track)
                    used_detection_indices.add(best_detection_idx)
                    used_track_indices.add(track_idx)
            
            # Create new tracks for unassociated detections
            next_id = max([t["id"] for t in previous_tracks], default=-1) + 1
            
            for det_idx, detection in enumerate(detections):
                if det_idx not in used_detection_indices:
                    track = {
                        "id": next_id,
                        "detection": detection,
                        "centroid": current_centroids[det_idx],
                        "age": 1,
                        "total_frames": 1
                    }
                    current_tracks.append(track)
                    next_id += 1
            
            return current_tracks
            
        except Exception as e:
            logger.error(f"Failed to track objects: {e}")
            return []
    
    @staticmethod
    def calculate_frame_quality(frame: np.ndarray) -> float:
        """
        Calculate frame quality score
        
        Args:
            frame: Input frame
            
        Returns:
            Quality score (0.0 to 1.0)
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Calculate sharpness using Laplacian variance
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            sharpness_score = min(laplacian_var / 1000.0, 1.0)
            
            # Calculate brightness distribution
            hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
            hist_norm = hist / hist.sum()
            
            # Penalize frames that are too dark or too bright
            dark_pixels = np.sum(hist_norm[:50])
            bright_pixels = np.sum(hist_norm[200:])
            exposure_score = 1.0 - max(dark_pixels, bright_pixels)
            
            # Calculate contrast
            contrast = gray.std() / 255.0
            contrast_score = min(contrast * 4, 1.0)
            
            # Combine scores
            quality_score = (sharpness_score * 0.5 + exposure_score * 0.3 + contrast_score * 0.2)
            
            return min(max(quality_score, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Failed to calculate frame quality: {e}")
            return 0.5
    
    @staticmethod
    def resize_frame_for_processing(frame: np.ndarray, target_size: Tuple[int, int] = (640, 640)) -> np.ndarray:
        """
        Resize frame for AI processing while maintaining aspect ratio
        
        Args:
            frame: Input frame
            target_size: Target size (width, height)
            
        Returns:
            Resized frame
        """
        try:
            height, width = frame.shape[:2]
            target_width, target_height = target_size
            
            # Calculate scaling factor
            scale = min(target_width / width, target_height / height)
            
            # Calculate new dimensions
            new_width = int(width * scale)
            new_height = int(height * scale)
            
            # Resize frame
            resized = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
            
            # Create canvas with target size
            canvas = np.zeros((target_height, target_width, 3), dtype=np.uint8)
            
            # Center the resized frame on canvas
            y_offset = (target_height - new_height) // 2
            x_offset = (target_width - new_width) // 2
            canvas[y_offset:y_offset + new_height, x_offset:x_offset + new_width] = resized
            
            return canvas
            
        except Exception as e:
            logger.error(f"Failed to resize frame: {e}")
            return frame
