@echo off
echo ========================================
echo   AI-Powered Safety Assistant Setup
echo ========================================
echo.

echo [1/5] Installing Node.js dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo [2/5] Installing client dependencies...
cd client
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install client dependencies
    cd ..
    pause
    exit /b 1
)

echo.
echo [3/5] Running code quality checks...
call npm run type-check
if %errorlevel% neq 0 (
    echo WARNING: TypeScript check failed, but continuing...
)

call npm run lint
if %errorlevel% neq 0 (
    echo WARNING: ESLint check failed, attempting to fix...
    call npm run lint:fix
)

echo.
echo [4/5] Setting up Python backend (optional)...
cd ..
if exist "ai_backend" (
    echo Python backend found, setting up virtual environment...
    python -m venv ai_backend/venv
    call ai_backend\venv\Scripts\activate.bat
    pip install -r ai_backend/requirements.txt
    if %errorlevel% neq 0 (
        echo WARNING: Python backend setup failed, but continuing...
    )
) else (
    echo Python backend not found, skipping...
)

echo.
echo [5/5] Setup complete!
echo.
echo ========================================
echo   Ready to run the application!
echo ========================================
echo.
echo To start the development server:
echo   npm run dev
echo.
echo To start the backend (if available):
echo   cd ai_backend
echo   uvicorn main:app --reload
echo.
pause
