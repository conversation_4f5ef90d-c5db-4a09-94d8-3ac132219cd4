import MobileLayout from '@/components/mobile-layout';
import Navigation from '@/components/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useQuery } from '@tanstack/react-query';
import { AlertTriangle, BarChart3, Calendar, Download, FileText, TrendingUp } from 'lucide-react';
import { useState } from 'react';

const MOCK_USER_ID = 'mock-user-123';

export default function Reports() {
  const [reportType, setReportType] = useState('incidents');
  const [dateRange, setDateRange] = useState('week');
  const [exportFormat, setExportFormat] = useState('csv');

  const { toast } = useToast();

  const { data: dashboardStats } = useQuery({
    queryKey: ['/api/dashboard', MOCK_USER_ID],
  });

  const { data: incidents = [] } = useQuery({
    queryKey: ['/api/incidents', MOCK_USER_ID, 10],
  });

  const { data: safetyChecks = [] } = useQuery({
    queryKey: ['/api/safety-checks', MOCK_USER_ID, 10],
  });

  const handleExportReports = async () => {
    try {
      const params = new URLSearchParams({
        format: exportFormat,
        type: reportType,
        range: dateRange,
      });

      const response = await fetch(`/api/reports/export?${params}`);

      if (!response.ok) {
        throw new Error('Export failed');
      }

      if (exportFormat === 'csv') {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `safety-report-${reportType}-${dateRange}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        const data = await response.json();
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `safety-report-${reportType}-${dateRange}.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }

      toast({
        title: 'Export Successful',
        description: `${reportType} report has been downloaded.`,
      });
    } catch (error) {
      toast({
        title: 'Export Failed',
        description: 'Failed to export reports. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const getIncidentStats = () => {
    const totalIncidents = (incidents as any[]).length;
    const highPriority = (incidents as any[]).filter((inc: any) => inc.severity === 'high').length;
    const resolved = (incidents as any[]).filter((inc: any) => inc.status === 'resolved').length;
    const resolutionRate = totalIncidents > 0 ? Math.round((resolved / totalIncidents) * 100) : 0;

    return { totalIncidents, highPriority, resolutionRate };
  };

  const getSafetyCheckStats = () => {
    const totalChecks = (safetyChecks as any[]).length;
    const avgCompliance =
      (safetyChecks as any[]).length > 0
        ? Math.round(
            (safetyChecks as any[]).reduce(
              (sum: number, check: any) => sum + (check.complianceScore || 0),
              0
            ) / (safetyChecks as any[]).length
          )
        : 0;

    return { totalChecks, avgCompliance };
  };

  const incidentStats = getIncidentStats();
  const checkStats = getSafetyCheckStats();

  const getIncidentTrend = () => {
    // Simple trend calculation based on recent incidents
    const recentIncidents = (incidents as any[]).slice(0, 5);
    const olderIncidents = (incidents as any[]).slice(5, 10);

    if ((recentIncidents as any[]).length === 0) return 'stable';
    if ((olderIncidents as any[]).length === 0) return 'up';

    return (recentIncidents as any[]).length > (olderIncidents as any[]).length ? 'up' : 'down';
  };

  const trend = getIncidentTrend();

  return (
    <>
      <MobileLayout title="Safety Reports" subtitle="Analytics and data export">
        <div className="px-4 py-6">
          {/* Quick Stats Overview */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-safety-blue rounded-full flex items-center justify-center mx-auto mb-2">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  {incidentStats.totalIncidents}
                </div>
                <div className="text-xs text-gray-600">Total Incidents</div>
                <div className="flex items-center justify-center mt-1">
                  <TrendingUp
                    className={`w-3 h-3 mr-1 ${
                      trend === 'up'
                        ? 'text-safety-red'
                        : trend === 'down'
                          ? 'text-safety-green'
                          : 'text-gray-400'
                    }`}
                  />
                  <span
                    className={`text-xs ${
                      trend === 'up'
                        ? 'text-safety-red'
                        : trend === 'down'
                          ? 'text-safety-green'
                          : 'text-gray-400'
                    }`}
                  >
                    {trend === 'up' ? 'Increasing' : trend === 'down' ? 'Decreasing' : 'Stable'}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-safety-green rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-white text-lg font-bold">%</span>
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  {incidentStats.resolutionRate}%
                </div>
                <div className="text-xs text-gray-600">Resolution Rate</div>
                <div className="flex items-center justify-center mt-1">
                  <div
                    className={`w-2 h-2 rounded-full mr-1 ${
                      incidentStats.resolutionRate >= 80
                        ? 'bg-safety-green'
                        : incidentStats.resolutionRate >= 60
                          ? 'bg-safety-orange'
                          : 'bg-safety-red'
                    }`}
                  ></div>
                  <span className="text-xs text-gray-500">
                    {incidentStats.resolutionRate >= 80
                      ? 'Excellent'
                      : incidentStats.resolutionRate >= 60
                        ? 'Good'
                        : 'Needs Improvement'}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Analytics */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Safety Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <AlertTriangle className="w-5 h-5 text-safety-red" />
                    <span className="font-medium">High Priority Incidents</span>
                  </div>
                  <span className="text-safety-red font-bold">{incidentStats.highPriority}</span>
                </div>

                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <BarChart3 className="w-5 h-5 text-safety-blue" />
                    <span className="font-medium">Safety Checks Completed</span>
                  </div>
                  <span className="text-safety-blue font-bold">{checkStats.totalChecks}</span>
                </div>

                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-safety-green rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">✓</span>
                    </div>
                    <span className="font-medium">Average Compliance Score</span>
                  </div>
                  <span className="text-safety-green font-bold">{checkStats.avgCompliance}%</span>
                </div>

                {dashboardStats && (
                  <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Calendar className="w-5 h-5 text-safety-orange" />
                      <span className="font-medium">Today's Inspections</span>
                    </div>
                    <span className="text-safety-orange font-bold">
                      {(dashboardStats as any)?.todayInspections || 0}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Export Configuration */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Download className="w-5 h-5 mr-2" />
                Export Reports
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Report Type
                  </label>
                  <Select value={reportType} onValueChange={setReportType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="incidents">Incidents Report</SelectItem>
                      <SelectItem value="safety-checks">Safety Checks Report</SelectItem>
                      <SelectItem value="compliance">Compliance Summary</SelectItem>
                      <SelectItem value="analytics">Analytics Report</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                  <Select value={dateRange} onValueChange={setDateRange}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="week">Last 7 Days</SelectItem>
                      <SelectItem value="month">Last 30 Days</SelectItem>
                      <SelectItem value="quarter">Last 3 Months</SelectItem>
                      <SelectItem value="year">Last Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Export Format
                  </label>
                  <Select value={exportFormat} onValueChange={setExportFormat}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="csv">CSV (Excel)</SelectItem>
                      <SelectItem value="json">JSON (Data)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  onClick={handleExportReports}
                  className="w-full bg-safety-blue hover:bg-blue-600"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Export Report
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {(incidents as any[]).slice(0, 3).map((incident: any) => (
                  <div
                    key={incident.id}
                    className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg"
                  >
                    <div
                      className={`w-3 h-3 rounded-full ${
                        incident.severity === 'high'
                          ? 'bg-safety-red'
                          : incident.severity === 'medium'
                            ? 'bg-safety-orange'
                            : 'bg-safety-green'
                      }`}
                    ></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {incident.description}
                      </p>
                      <p className="text-xs text-gray-600">
                        {new Date(incident.createdAt).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                        })}
                      </p>
                    </div>
                    <FileText className="w-4 h-4 text-gray-400" />
                  </div>
                ))}

                {(incidents as any[]).length === 0 && (
                  <div className="text-center py-4 text-gray-500">
                    <FileText className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                    <p className="text-sm">No recent activity</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </MobileLayout>

      <Navigation />
    </>
  );
}
