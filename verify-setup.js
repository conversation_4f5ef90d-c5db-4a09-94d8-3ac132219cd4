#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';

console.log('🔍 Verifying AI Safety Assistant Setup...\n');

const checks = [
  {
    name: 'Node.js Version',
    check: () => {
      const version = process.version;
      const major = parseInt(version.slice(1).split('.')[0]);
      return major >= 18 ? `✅ ${version}` : `❌ ${version} (requires 18+)`;
    }
  },
  {
    name: 'npm Version',
    check: () => {
      try {
        const version = execSync('npm --version', { encoding: 'utf8' }).trim();
        return `✅ ${version}`;
      } catch (error) {
        return '❌ npm not found';
      }
    }
  },
  {
    name: 'Root package.json',
    check: () => fs.existsSync('package.json') ? '✅ Found' : '❌ Missing'
  },
  {
    name: 'Client package.json',
    check: () => fs.existsSync('client/package.json') ? '✅ Found' : '❌ Missing'
  },
  {
    name: 'TypeScript Config',
    check: () => fs.existsSync('client/tsconfig.json') ? '✅ Found' : '❌ Missing'
  },
  {
    name: 'Tailwind Config',
    check: () => fs.existsSync('client/tailwind.config.js') ? '✅ Found' : '❌ Missing'
  },
  {
    name: 'Vite Config',
    check: () => fs.existsSync('client/vite.config.ts') ? '✅ Found' : '❌ Missing'
  },
  {
    name: 'Main App Component',
    check: () => fs.existsSync('client/src/App.tsx') ? '✅ Found' : '❌ Missing'
  },
  {
    name: 'AI Dashboard Component',
    check: () => fs.existsSync('client/src/components/ai-dashboard.tsx') ? '✅ Found' : '❌ Missing'
  },
  {
    name: 'Styles',
    check: () => fs.existsSync('client/src/index.css') ? '✅ Found' : '❌ Missing'
  },
  {
    name: 'ESLint Config',
    check: () => fs.existsSync('client/.eslintrc.json') ? '✅ Found' : '❌ Missing'
  },
  {
    name: 'Prettier Config',
    check: () => fs.existsSync('client/.prettierrc.json') ? '✅ Found' : '❌ Missing'
  },
  {
    name: 'VS Code Settings',
    check: () => fs.existsSync('.vscode/settings.json') ? '✅ Found' : '❌ Missing'
  },
  {
    name: 'Root Dependencies',
    check: () => {
      try {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        const hasReact = packageJson.dependencies?.react || packageJson.devDependencies?.react;
        return hasReact ? '✅ Installed' : '❌ Missing React';
      } catch (error) {
        return '❌ Error reading package.json';
      }
    }
  },
  {
    name: 'Client Dependencies',
    check: () => {
      try {
        const packageJson = JSON.parse(fs.readFileSync('client/package.json', 'utf8'));
        const hasReact = packageJson.dependencies?.react;
        const hasTailwind = packageJson.devDependencies?.tailwindcss;
        const hasVite = packageJson.devDependencies?.vite;
        
        if (hasReact && hasTailwind && hasVite) {
          return '✅ All key dependencies found';
        } else {
          const missing = [];
          if (!hasReact) missing.push('react');
          if (!hasTailwind) missing.push('tailwindcss');
          if (!hasVite) missing.push('vite');
          return `❌ Missing: ${missing.join(', ')}`;
        }
      } catch (error) {
        return '❌ Error reading client package.json';
      }
    }
  },
  {
    name: 'Node Modules (Root)',
    check: () => fs.existsSync('node_modules') ? '✅ Installed' : '❌ Run npm install'
  },
  {
    name: 'Node Modules (Client)',
    check: () => fs.existsSync('client/node_modules') ? '✅ Installed' : '❌ Run cd client && npm install'
  }
];

let allPassed = true;

checks.forEach(({ name, check }) => {
  const result = check();
  console.log(`${name.padEnd(25)} ${result}`);
  if (result.startsWith('❌')) {
    allPassed = false;
  }
});

console.log('\n' + '='.repeat(50));

if (allPassed) {
  console.log('🎉 All checks passed! Your setup is ready.');
  console.log('\nTo start the application:');
  console.log('  npm run dev');
  console.log('\nOr use the start script:');
  console.log('  ./start.sh (macOS/Linux)');
  console.log('  start.bat (Windows)');
} else {
  console.log('❌ Some checks failed. Please fix the issues above.');
  console.log('\nCommon solutions:');
  console.log('1. Run: npm install');
  console.log('2. Run: cd client && npm install');
  console.log('3. Check Node.js version (requires 18+)');
  console.log('4. See TROUBLESHOOTING.md for detailed help');
}

console.log('\n📖 For detailed setup instructions, see README.md');
console.log('🔧 For troubleshooting help, see TROUBLESHOOTING.md');
