import IncidentModal from '@/components/incident-modal';
import MobileLayout from '@/components/mobile-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useCamera } from '@/hooks/use-camera';
import { useToast } from '@/hooks/use-toast';
import { AIDetectionService } from '@/services/ai-detection';
import { ArrowLeft, Camera, Loader2, Upload } from 'lucide-react';
import { useState } from 'react';
import { Link } from 'wouter';

const MOCK_USER_ID = 'mock-user-123';

export default function PhotoCheck() {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [capturedImage, setCapturedImage] = useState<string>();
  const [isIncidentModalOpen, setIsIncidentModalOpen] = useState(false);

  const { videoRef, isActive, error, startCamera, stopCamera, capturePhoto } = useCamera();
  const { toast } = useToast();

  const handleTakePhoto = async () => {
    if (!isActive) {
      await startCamera();
      return;
    }

    const imageData = capturePhoto();
    if (imageData) {
      setCapturedImage(imageData);
      setIsAnalyzing(true);

      try {
        const analysis = await AIDetectionService.analyzeImage(imageData, 'PHOTO_CHECK');
        setAnalysisResult(analysis);
        stopCamera();
      } catch (error) {
        toast({
          title: 'Analysis Failed',
          description: 'Failed to analyze the image. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsAnalyzing(false);
      }
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async e => {
      const imageData = e.target?.result as string;
      setCapturedImage(imageData);
      setIsAnalyzing(true);

      try {
        const analysis = await AIDetectionService.analyzeImage(imageData, 'PHOTO_CHECK');
        setAnalysisResult(analysis);
      } catch (error) {
        toast({
          title: 'Analysis Failed',
          description: 'Failed to analyze the image. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsAnalyzing(false);
      }
    };
    reader.readAsDataURL(file);
  };

  const handleCreateReport = () => {
    setIsIncidentModalOpen(true);
  };

  const resetAnalysis = () => {
    setAnalysisResult(null);
    setCapturedImage(undefined);
    stopCamera();
  };

  return (
    <>
      <MobileLayout title="Photo Safety Check" subtitle="AI-powered image analysis">
        <div className="px-4 py-6">
          <div className="mb-6">
            <Link href="/">
              <Button variant="ghost" size="sm" className="mb-4">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>

          {!capturedImage && !isActive && (
            <div className="space-y-4">
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 bg-safety-blue rounded-full flex items-center justify-center mx-auto mb-4">
                    <Camera className="w-8 h-8 text-white" />
                  </div>
                  <h2 className="text-lg font-semibold mb-2">Take a Safety Photo</h2>
                  <p className="text-gray-600 mb-6">
                    Capture an image of your workplace for instant AI safety analysis
                  </p>
                  <Button
                    onClick={handleTakePhoto}
                    className="w-full bg-safety-blue hover:bg-blue-600 mb-4"
                  >
                    <Camera className="w-5 h-5 mr-2" />
                    Take Photo
                  </Button>

                  <div className="relative">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      aria-label="Select image file for safety analysis"
                      title="Choose an image file to analyze for safety violations"
                    />
                    <Button variant="outline" className="w-full">
                      <Upload className="w-5 h-5 mr-2" />
                      Upload Photo
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-2">What We Check:</h3>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Personal Protective Equipment (PPE)</li>
                    <li>• Workplace hazards and obstructions</li>
                    <li>• Tool placement and organization</li>
                    <li>• Safety compliance violations</li>
                    <li>• Environmental risks</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          )}

          {isActive && !capturedImage && (
            <div className="space-y-4">
              <div className="relative bg-black rounded-xl overflow-hidden">
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                  className="w-full h-64 object-cover"
                />
                {error && (
                  <div className="absolute inset-0 bg-gray-900 flex items-center justify-center">
                    <p className="text-white text-center px-4">{error}</p>
                  </div>
                )}
              </div>

              <div className="flex space-x-3">
                <Button onClick={stopCamera} variant="outline" className="flex-1">
                  Cancel
                </Button>
                <Button
                  onClick={handleTakePhoto}
                  className="flex-1 bg-safety-blue hover:bg-blue-600"
                >
                  <Camera className="w-5 h-5 mr-2" />
                  Capture
                </Button>
              </div>
            </div>
          )}

          {isAnalyzing && (
            <Card>
              <CardContent className="p-6 text-center">
                <Loader2 className="w-12 h-12 text-safety-blue animate-spin mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Analyzing Image</h3>
                <p className="text-gray-600">
                  AI is processing your image for safety violations...
                </p>
              </CardContent>
            </Card>
          )}

          {capturedImage && analysisResult && (
            <div className="space-y-4">
              <Card>
                <CardContent className="p-4">
                  <div className="relative">
                    <img
                      src={capturedImage}
                      alt="Safety analysis"
                      className="w-full h-48 object-cover rounded-lg"
                    />
                    <div
                      className={`absolute top-2 right-2 px-2 py-1 rounded text-xs font-medium ${
                        analysisResult.riskLevel === 'HIGH'
                          ? 'bg-safety-red text-white'
                          : analysisResult.riskLevel === 'MEDIUM'
                            ? 'bg-safety-orange text-white'
                            : 'bg-safety-green text-white'
                      }`}
                    >
                      {analysisResult.riskLevel} RISK
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-3">Analysis Results</h3>

                  {analysisResult.detections.length === 0 ? (
                    <div className="text-center py-4">
                      <div className="w-12 h-12 bg-safety-green rounded-full flex items-center justify-center mx-auto mb-2">
                        <span className="text-white text-xl">✓</span>
                      </div>
                      <p className="text-safety-green font-medium">No safety violations detected</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {analysisResult.detections.map((detection: any, index: number) => (
                        <div
                          key={index}
                          className={`flex items-center space-x-3 p-3 rounded-lg ${
                            detection.type === 'PPE_VIOLATION'
                              ? 'bg-red-50 border border-red-200'
                              : detection.type === 'SAFETY_EQUIPMENT'
                                ? 'bg-green-50 border border-green-200'
                                : 'bg-orange-50 border border-orange-200'
                          }`}
                        >
                          <div
                            className={`w-6 h-6 rounded-full flex items-center justify-center ${
                              detection.type === 'PPE_VIOLATION'
                                ? 'bg-safety-red'
                                : detection.type === 'SAFETY_EQUIPMENT'
                                  ? 'bg-safety-green'
                                  : 'bg-safety-orange'
                            }`}
                          >
                            <span className="text-white text-xs">
                              {detection.type === 'PPE_VIOLATION'
                                ? '!'
                                : detection.type === 'SAFETY_EQUIPMENT'
                                  ? '✓'
                                  : '⚠'}
                            </span>
                          </div>
                          <div className="flex-1">
                            <p className="font-medium text-sm">{detection.description}</p>
                            <p className="text-xs text-gray-600">
                              Confidence: {Math.round(detection.confidence * 100)}%
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {analysisResult.recommendations.length > 0 && (
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-safety-blue mb-2">Recommendations:</h4>
                      <ul className="text-sm text-gray-700 space-y-1">
                        {analysisResult.recommendations.map((rec: string, index: number) => (
                          <li key={index}>• {rec}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className="flex space-x-3">
                <Button onClick={resetAnalysis} variant="outline" className="flex-1">
                  New Analysis
                </Button>
                {analysisResult.riskLevel !== 'LOW' && (
                  <Button
                    onClick={handleCreateReport}
                    className="flex-1 bg-safety-red hover:bg-red-600"
                  >
                    Create Report
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </MobileLayout>

      <IncidentModal
        isOpen={isIncidentModalOpen}
        onClose={() => setIsIncidentModalOpen(false)}
        imageData={capturedImage}
        analysis={analysisResult}
        userId={MOCK_USER_ID}
      />
    </>
  );
}
