"""
Zone Monitoring Model
Detects unauthorized access to restricted areas and zone violations
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import cv2
import numpy as np
from ultralytics import YOLO
import torch

logger = logging.getLogger(__name__)

class ZoneMonitor:
    """
    Zone monitoring system for detecting unauthorized access and violations
    """
    
    def __init__(self, model_path: Path):
        self.model_path = model_path
        self.model_path.mkdir(exist_ok=True)
        
        self.model: Optional[YOLO] = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Predefined zone types and their characteristics
        self.zone_types = {
            "restricted": {
                "color": (0, 0, 255),  # Red
                "access_level": "authorized_only",
                "severity": "HIGH"
            },
            "chemical": {
                "color": (0, 255, 255),  # Yellow
                "access_level": "ppe_required",
                "severity": "HIGH"
            },
            "machinery": {
                "color": (255, 165, 0),  # Orange
                "access_level": "trained_only",
                "severity": "MEDIUM"
            },
            "caution": {
                "color": (255, 255, 0),  # Yellow
                "access_level": "caution_required",
                "severity": "MEDIUM"
            },
            "emergency_exit": {
                "color": (0, 255, 0),  # Green
                "access_level": "keep_clear",
                "severity": "HIGH"
            }
        }
        
        # Zone definitions (would be loaded from configuration)
        self.defined_zones = []
        
        self.performance_metrics = {
            "total_detections": 0,
            "avg_inference_time": 0.0,
            "zone_violations": 0
        }
    
    async def load_model(self):
        """Load zone monitoring model"""
        try:
            # Load YOLO for person detection
            self.model = YOLO("yolov8n.pt")
            self.model.to(self.device)
            
            # Load zone definitions (in production, this would come from database)
            await self._load_zone_definitions()
            
            logger.info("Zone monitor loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load zone monitor: {e}")
            raise
    
    async def _load_zone_definitions(self):
        """Load zone definitions from configuration"""
        # Example zone definitions (in production, these would be configurable)
        self.defined_zones = [
            {
                "id": "zone_1",
                "name": "Chemical Storage Area",
                "type": "chemical",
                "polygon": [(100, 100), (300, 100), (300, 250), (100, 250)],
                "required_ppe": ["helmet", "gloves", "safety_goggles", "face_mask"],
                "authorized_personnel": ["chemist", "safety_officer"],
                "active": True
            },
            {
                "id": "zone_2", 
                "name": "Machinery Operation Zone",
                "type": "machinery",
                "polygon": [(400, 150), (600, 150), (600, 350), (400, 350)],
                "required_ppe": ["helmet", "safety_vest", "safety_boots"],
                "authorized_personnel": ["operator", "maintenance"],
                "active": True
            },
            {
                "id": "zone_3",
                "name": "Emergency Exit Corridor",
                "type": "emergency_exit",
                "polygon": [(50, 400), (150, 400), (150, 500), (50, 500)],
                "required_ppe": [],
                "authorized_personnel": [],
                "active": True
            }
        ]
    
    async def detect(self, image: np.ndarray, confidence_threshold: float = 0.5) -> List[Dict[str, Any]]:
        """
        Detect zone violations in image
        """
        if not self.model:
            raise RuntimeError("Zone monitor model not loaded")
        
        start_time = time.time()
        
        try:
            detections = []
            
            # Step 1: Detect people in the image
            people = await self._detect_people(image, confidence_threshold)
            
            # Step 2: Check each person against defined zones
            for person in people:
                zone_violations = await self._check_zone_violations(person, image)
                detections.extend(zone_violations)
            
            # Step 3: Check for blocked emergency exits
            exit_violations = await self._check_emergency_exits(image)
            detections.extend(exit_violations)
            
            # Step 4: Detect zone boundary markers
            boundary_violations = await self._detect_boundary_violations(image)
            detections.extend(boundary_violations)
            
            # Update performance metrics
            inference_time = time.time() - start_time
            self.performance_metrics["total_detections"] += len(detections)
            self.performance_metrics["zone_violations"] += len(detections)
            self.performance_metrics["avg_inference_time"] = (
                (self.performance_metrics["avg_inference_time"] + inference_time) / 2
            )
            
            return detections
            
        except Exception as e:
            logger.error(f"Zone monitoring error: {e}")
            return []
    
    async def _detect_people(self, image: np.ndarray, confidence_threshold: float) -> List[Dict]:
        """Detect people in the image"""
        results = self.model(image, classes=[0], conf=confidence_threshold, verbose=False)
        
        people = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0].cpu().numpy())
                    
                    # Calculate person center point
                    center_x = int((x1 + x2) / 2)
                    center_y = int((y1 + y2) / 2)
                    
                    person = {
                        "confidence": confidence,
                        "bounding_box": {
                            "x": int(x1),
                            "y": int(y1),
                            "width": int(x2 - x1),
                            "height": int(y2 - y1)
                        },
                        "center": (center_x, center_y)
                    }
                    people.append(person)
        
        return people
    
    async def _check_zone_violations(self, person: Dict, image: np.ndarray) -> List[Dict]:
        """Check if person violates any zone restrictions"""
        violations = []
        person_center = person["center"]
        
        for zone in self.defined_zones:
            if not zone["active"]:
                continue
            
            # Check if person is inside the zone
            if self._point_in_polygon(person_center, zone["polygon"]):
                zone_info = self.zone_types[zone["type"]]
                
                # Create violation detection
                violation = {
                    "type": "ZONE_VIOLATION",
                    "zone_id": zone["id"],
                    "zone_name": zone["name"],
                    "zone_type": zone["type"],
                    "confidence": person["confidence"],
                    "bounding_box": person["bounding_box"],
                    "description": f"Unauthorized access to {zone['name']}",
                    "severity": zone_info["severity"],
                    "access_level": zone_info["access_level"],
                    "required_ppe": zone.get("required_ppe", []),
                    "recommendations": self._get_zone_recommendations(zone)
                }
                violations.append(violation)
        
        return violations
    
    async def _check_emergency_exits(self, image: np.ndarray) -> List[Dict]:
        """Check for blocked emergency exits"""
        violations = []
        
        # Find emergency exit zones
        exit_zones = [z for z in self.defined_zones if z["type"] == "emergency_exit"]
        
        for zone in exit_zones:
            if not zone["active"]:
                continue
            
            # Check if exit area is blocked by objects
            zone_roi = self._extract_zone_roi(image, zone["polygon"])
            
            if zone_roi is not None:
                # Detect objects in the exit zone
                objects = await self._detect_objects_in_zone(zone_roi)
                
                if objects:
                    # Calculate zone center for bounding box
                    polygon = np.array(zone["polygon"])
                    center_x = int(np.mean(polygon[:, 0]))
                    center_y = int(np.mean(polygon[:, 1]))
                    
                    violation = {
                        "type": "ZONE_VIOLATION",
                        "zone_id": zone["id"],
                        "zone_name": zone["name"],
                        "zone_type": "emergency_exit_blocked",
                        "confidence": 0.9,
                        "bounding_box": {
                            "x": center_x - 50,
                            "y": center_y - 50,
                            "width": 100,
                            "height": 100
                        },
                        "description": f"Emergency exit blocked: {zone['name']}",
                        "severity": "HIGH",
                        "blocking_objects": objects,
                        "recommendations": [
                            "Clear emergency exit immediately",
                            "Remove all obstructions",
                            "Ensure exit remains accessible",
                            "Review exit maintenance procedures"
                        ]
                    }
                    violations.append(violation)
        
        return violations
    
    async def _detect_boundary_violations(self, image: np.ndarray) -> List[Dict]:
        """Detect violations of zone boundaries and signage"""
        violations = []
        
        # Detect zone boundary markers using color detection
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Look for standard safety colors
        safety_colors = {
            "red_tape": ([0, 120, 70], [10, 255, 255]),      # Red warning tape
            "yellow_tape": ([20, 120, 70], [30, 255, 255]),  # Yellow caution tape
            "orange_cone": ([10, 120, 70], [20, 255, 255])   # Orange traffic cones
        }
        
        for color_name, (lower, upper) in safety_colors.items():
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 100:  # Minimum area threshold
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Check if this boundary marker is damaged or displaced
                    if self._is_boundary_damaged(mask[y:y+h, x:x+w]):
                        violation = {
                            "type": "ZONE_VIOLATION",
                            "zone_id": f"boundary_{color_name}",
                            "zone_name": f"Safety Boundary ({color_name})",
                            "zone_type": "boundary_violation",
                            "confidence": 0.7,
                            "bounding_box": {"x": x, "y": y, "width": w, "height": h},
                            "description": f"Damaged or displaced {color_name.replace('_', ' ')}",
                            "severity": "MEDIUM",
                            "recommendations": [
                                "Repair or replace safety boundary markers",
                                "Ensure proper zone demarcation",
                                "Check all boundary markers in area"
                            ]
                        }
                        violations.append(violation)
        
        return violations
    
    def _point_in_polygon(self, point: Tuple[int, int], polygon: List[Tuple[int, int]]) -> bool:
        """Check if a point is inside a polygon using ray casting algorithm"""
        x, y = point
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def _extract_zone_roi(self, image: np.ndarray, polygon: List[Tuple[int, int]]) -> Optional[np.ndarray]:
        """Extract region of interest for a zone"""
        try:
            # Create mask for the polygon
            mask = np.zeros(image.shape[:2], dtype=np.uint8)
            polygon_array = np.array(polygon, dtype=np.int32)
            cv2.fillPoly(mask, [polygon_array], 255)
            
            # Extract ROI
            roi = cv2.bitwise_and(image, image, mask=mask)
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(polygon_array)
            roi_cropped = roi[y:y+h, x:x+w]
            
            return roi_cropped
            
        except Exception as e:
            logger.warning(f"Failed to extract zone ROI: {e}")
            return None
    
    async def _detect_objects_in_zone(self, zone_roi: np.ndarray) -> List[str]:
        """Detect objects that might be blocking a zone"""
        if zone_roi is None or zone_roi.size == 0:
            return []
        
        # Use YOLO to detect common objects
        results = self.model(zone_roi, verbose=False)
        
        detected_objects = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    class_id = int(box.cls[0].cpu().numpy())
                    confidence = float(box.conf[0].cpu().numpy())
                    
                    # Map class ID to object name (COCO dataset classes)
                    object_names = {
                        0: "person", 1: "bicycle", 2: "car", 3: "motorcycle",
                        15: "bench", 16: "bird", 17: "cat", 18: "dog",
                        56: "chair", 57: "couch", 58: "potted plant",
                        59: "bed", 60: "dining table", 61: "toilet",
                        62: "tv", 63: "laptop", 64: "mouse", 65: "remote",
                        66: "keyboard", 67: "cell phone", 68: "microwave"
                    }
                    
                    object_name = object_names.get(class_id, f"object_{class_id}")
                    if confidence > 0.5:
                        detected_objects.append(object_name)
        
        return detected_objects
    
    def _is_boundary_damaged(self, boundary_mask: np.ndarray) -> bool:
        """Check if a boundary marker appears damaged"""
        if boundary_mask.size == 0:
            return False
        
        # Calculate the ratio of detected pixels to total area
        total_pixels = boundary_mask.shape[0] * boundary_mask.shape[1]
        detected_pixels = np.sum(boundary_mask > 0)
        
        if total_pixels == 0:
            return False
        
        coverage_ratio = detected_pixels / total_pixels
        
        # If coverage is too low, the boundary might be damaged
        return coverage_ratio < 0.3
    
    def _get_zone_recommendations(self, zone: Dict) -> List[str]:
        """Get recommendations for zone violations"""
        zone_type = zone["type"]
        
        recommendations = {
            "restricted": [
                "Verify personnel authorization",
                "Escort unauthorized personnel out",
                "Review access control procedures",
                "Check identification badges"
            ],
            "chemical": [
                "Ensure proper PPE is worn",
                "Verify chemical handling training",
                "Check ventilation systems",
                "Have emergency shower/eyewash accessible"
            ],
            "machinery": [
                "Verify operator certification",
                "Ensure machine guards are in place",
                "Check lockout/tagout procedures",
                "Provide safety training if needed"
            ],
            "caution": [
                "Proceed with caution",
                "Ensure proper safety measures",
                "Monitor for additional hazards"
            ],
            "emergency_exit": [
                "Keep exit clear at all times",
                "Remove any obstructions",
                "Ensure proper lighting",
                "Test exit accessibility"
            ]
        }
        
        return recommendations.get(zone_type, ["Follow safety protocols"])
    
    async def get_performance_metrics(self) -> Dict[str, float]:
        """Get performance metrics"""
        return self.performance_metrics.copy()
    
    async def get_memory_usage(self) -> float:
        """Get memory usage"""
        if self.model and torch.cuda.is_available():
            return torch.cuda.memory_allocated() / 1024**2
        return 0.0
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.model:
            del self.model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        logger.info("Zone monitor cleanup complete")
